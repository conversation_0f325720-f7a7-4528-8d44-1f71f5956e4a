import { createContext, useContext, useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import {
  getLinkedAccounts,
  getAllOrders,
  getTransactions,
} from "../actions/schwabTraderAPIactions";
import {
  accountObserver,
  orderObserver,
  transactionObserver,
} from "../utils/Observer";
import moment from "moment";
const EndpointAppContext = createContext();

export const EndpointAppProvider = ({ children }) => {
  const { data: session, status } = useSession();
  const [accountData, setAccountData] = useState(null);
  const [transactionsData, setTransactionsData] = useState(null);
  const [ordersData, setOrdersData] = useState(null);
  const [isLoggedInToSchwab, setIsLoggedInToSchwab] = useState(false);

  // Date range states for filtering
  const [transactionsDateRange, setTransactionsDateRange] = useState({
    key: "last7days",
    label: "Last 7 Days",
    startDate: moment().subtract(7, "days").startOf("day").toISOString(),
    endDate: moment().endOf("day").toISOString(),
  });

  const [ordersDateRange, setOrdersDateRange] = useState({
    key: "last7days",
    label: "Last 7 Days",
    startDate: moment().subtract(7, "days").startOf("day").toISOString(),
    endDate: moment().endOf("day").toISOString(),
  });

  const accountNumber = process.env.NEXT_PUBLIC_ACCOUNT_NUMBER; // we need to change this

  // Function to check Schwab login status
  const checkSchwabLogin = async () => {
    try {
      const res = await fetch("/api/schwab-status", {
        method: "GET",
        credentials: "include",
      });
      const data = await res.json();
      setIsLoggedInToSchwab(data.loggedIn);
      return data.loggedIn;
    } catch (error) {
      console.error("Error checking Schwab login status:", error);
      setIsLoggedInToSchwab(false);
      return false;
    }
  };

  // Function to fetch transactions with date range
  const fetchTransactions = async (dateRange = transactionsDateRange) => {
    // Only fetch if user is authenticated with both NextAuth and Schwab
    if (!session || status !== "authenticated") {
      console.log(
        "User not authenticated with NextAuth, skipping transactions fetch"
      );
      return;
    }

    if (!isLoggedInToSchwab) {
      console.log("User not connected to Schwab, skipping transactions fetch");
      return;
    }

    try {
      console.log("Temporarily disabling getTransactions in fetchTransactions function in EndpointAppContext.js");
      // const transactions = await getTransactions(
      //   accountNumber,
      //   dateRange.startDate,
      //   dateRange.endDate,
      //   "TRADE"
      // );
      // setTransactionsData(transactions);
      // transactionObserver.notify(transactions);
    } catch (error) {
      console.error("error fetching transactions:", error);
    }
  };

  // Function to fetch orders with date range
  const fetchOrders = async (dateRange = ordersDateRange) => {
    // Only fetch if user is authenticated with both NextAuth and Schwab
    if (!session || status !== "authenticated") {
      console.log(
        "User not authenticated with NextAuth, skipping orders fetch"
      );
      return;
    }

    if (!isLoggedInToSchwab) {
      console.log("User not connected to Schwab, skipping orders fetch");
      return;
    }

    try {
      const orders = await getAllOrders(dateRange.startDate, dateRange.endDate);
      setOrdersData(orders);
      orderObserver.notify(orders);
    } catch (error) {
      console.error("error fetching orders:", error);
    }
  };

  // Function to update transactions date range and refetch data
  const updateTransactionsDateRange = (newRange) => {
    setTransactionsDateRange(newRange);
    fetchTransactions(newRange);
  };

  // Function to update orders date range and refetch data
  const updateOrdersDateRange = (newRange) => {
    setOrdersDateRange(newRange);
    fetchOrders(newRange);
  };

  // Function to clear all user data
  const clearAllUserData = () => {
    console.log("Clearing all endpoint data for user logout");
    setAccountData(null);
    setTransactionsData(null);
    setOrdersData(null);

    // Reset date ranges to default
    const defaultRange = {
      key: "last7days",
      label: "Last 7 Days",
      startDate: moment().subtract(7, "days").startOf("day").toISOString(),
      endDate: moment().endOf("day").toISOString(),
    };
    setTransactionsDateRange(defaultRange);
    setOrdersDateRange(defaultRange);
  };

  // Check Schwab login status when session changes
  useEffect(() => {
    if (session && status === "authenticated") {
      checkSchwabLogin();
    } else {
      setIsLoggedInToSchwab(false);
    }
  }, [session, status]);

  // Fetch data when both NextAuth and Schwab authentication are confirmed
  useEffect(() => {
    // Clear data if user is not authenticated with NextAuth
    if (!session || status !== "authenticated") {
      console.log(
        "User not authenticated with NextAuth, clearing data and skipping API calls"
      );
      setAccountData(null);
      setTransactionsData(null);
      setOrdersData(null);
      return;
    }

    // Clear data if user is not connected to Schwab
    if (!isLoggedInToSchwab) {
      console.log(
        "User not connected to Schwab, clearing data and skipping API calls"
      );
      setAccountData(null);
      setTransactionsData(null);
      setOrdersData(null);
      return;
    }

    // Fetch account data (no date filtering needed)
    getLinkedAccounts()
      .then((positions) => {
        console.log("Dati aggiornati:", positions);
        setAccountData(positions);
        accountObserver.notify(positions);
      })
      .catch((error) => {
        console.error("error fetching account data:", error);
      });

    // Fetch initial transactions and orders with default ranges
    fetchTransactions();
    fetchOrders();
  }, [session, status, isLoggedInToSchwab]);

  return (
    <EndpointAppContext.Provider
      value={{
        accountData,
        setAccountData,
        transactionsData,
        setTransactionsData,
        ordersData,
        setOrdersData,
        transactionsDateRange,
        ordersDateRange,
        updateTransactionsDateRange,
        updateOrdersDateRange,
        clearAllUserData,
      }}
    >
      {children}
    </EndpointAppContext.Provider>
  );
};

export const useEndpointAppContext = () => useContext(EndpointAppContext);
