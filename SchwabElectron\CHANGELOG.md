# Changelog

All notable changes to the Schwab Dashboard project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added

- **Load from Database Feature**: Added "Load from Database" button to WB Dashboard controls that restores both pairs and underlying Excel data for full functionality
- Comprehensive user data cleanup system for secure logout
- Automated development environment setup scripts
- SSL certificate generation for local development
- Full logout API endpoint for complete session cleanup
- Reusable logout hook for consistent behavior across the app
- User data cleaner utility for browser storage cleanup

### Fixed

- **Dialog Accessibility**: Fixed missing DialogTitle components in OrderEntryDialog and OrdersCard for screen reader accessibility
- **Load from Database Integration**: Fixed Edit Data and Move functions not working after loading from database by ensuring both pairs and Excel data are restored together
- **Auto-Close Logic**: Disabled automatic pair movement to closed positions when moving from loaded to open pairs
- **PNL Calculations**: Corrected profit/loss calculations using proper bid/ask formulas instead of last price
- **User Data Persistence**: Eliminated data leakage between user sessions
- **Schwab Disconnect**: Fixed disconnect button to properly logout from Schwab without auto-reconnection

### Changed

- PNL calculations now display as whole numbers for better readability
- Enhanced logout process with comprehensive data cleanup
- Improved WebSocket connection management with proper disconnect handling
- WB Dashboard controls now include both save and load database operations with proper loading states and status feedback

---

## [v1.2.0] - 2024-12-XX

### 🔧 **Bug Fixes & Improvements**

#### Auto-Close Logic Fix

**Issue**: When moving a loaded pair to open pairs, the pair was automatically moved to closed pairs instead of staying in open pairs.

**Root Cause**: Automatic logic in `app/Strategies/WB/dashboard/page.js` monitored open pairs and moved pairs with 0 shares to closed positions immediately.

**Solution**:

- Temporarily commented out the `checkAndMoveZeroSharesPairs()` function
- Disabled the useEffect trigger that monitored openPairs changes
- Users now have full manual control over pair movements

**Files Modified**:

- `app/Strategies/WB/dashboard/page.js`

---

#### PNL Calculation Corrections

**Issue**: PNL calculations appeared to be divided by 100 and used incorrect price sources.

**Root Cause**:

- Dashboard used `formattedPnl` properties with incorrect calculation logic
- PairComponent.js used `last` price for both long and short sides
- String manipulation was truncating values (divide-by-100 effect)

**Solution**:

- **Long PNL**: `(bid - cost) * shares`
- **Short PNL**: `(cost - ask) * shares`
- Fixed calculations in both dashboard display and PairComponent.js
- Display PNL values as whole numbers

**Files Modified**:

- `app/Strategies/WB/dashboard/page.js`
- `app/pairArray/PairComponent.js`

**Code Example**:

```javascript
// Fixed PNL calculation
const longPNL =
  (parseFloat(bidValue) - parseFloat(formattedCost)) * parseFloat(formattedAmt);
const shortPNL =
  (parseFloat(formattedCost) - parseFloat(askValue)) * parseFloat(formattedAmt);
const formattedPnl = Math.round(pnlValue);
```

---

#### Comprehensive User Data Cleanup

**Issue**: When users signed out and different users logged in, they saw cached data from previous users.

**Root Cause**: User-specific data persisted in multiple locations:

- Trading data in localStorage (ExcelDataContext)
- Account information in component state (EndpointAppContext)
- Market data in component state (MarketDataContext)
- Pair data in component state (PairArrayContext)
- Server-side httpOnly cookies and sessions

**Solution**:

1. **Full Logout API** (`/api/full-logout`): Clears all server-side cookies
2. **Context Clear Functions**: Added `clearAllUserData()` to all contexts
3. **Data Cleaner Utility** (`utils/userDataCleaner.js`): Comprehensive browser storage cleanup
4. **Logout Hook** (`hooks/useComprehensiveLogout.js`): Consistent logout behavior
5. **Enhanced Home Page**: Integrated all cleanup functions

**Files Added**:

- `app/api/full-logout/route.js`
- `utils/userDataCleaner.js`
- `hooks/useComprehensiveLogout.js`

**Files Modified**:

- `app/Strategies/WB/ExcelDataContext.js`
- `components/EndpointAppContext.js`
- `app/testingWebsocket/MarketDataContext.js`
- `app/pairArray/PairArray.js`
- `app/page.js`

**Result**: Complete data isolation between user sessions with no data leakage.

---

#### Schwab Disconnect Functionality

**Issue**: Clicking "Disconnect" from Schwab would disconnect but immediately reconnect.

**Root Cause**:

- Client-side cookie clearing couldn't remove httpOnly cookies
- Page refresh triggered automatic reconnection logic
- MarketDataContext automatically connected on component mount

**Solution**:

- Created `/api/schwab-logout` endpoint for proper server-side cookie clearing
- Removed page refresh that caused reconnection
- Added login status check in MarketDataContext before auto-connecting
- Enhanced disconnect function with WebSocket cleanup

**Files Added**:

- `app/api/schwab-logout/route.js`

**Files Modified**:

- `app/page.js`
- `app/testingWebsocket/MarketDataContext.js`

---

### 🚀 **Development Environment Improvements**

#### Automated Setup System

**Added**: Complete automated setup for new developers and deployments.

**New Scripts**:

- `npm run setup`: One-command setup for everything
- `npm run generate-certs`: SSL certificate generation
- `npm run create-admin`: Admin user creation
- `npm run dev-web`: Web-only development mode

**New Files**:

- `scripts/setup-dev.js`: Automated development setup
- `scripts/generate-certs.js`: SSL certificate generation
- `scripts/create-admin-user.js`: Admin user creation
- `.env.example`: Environment variable template

**Enhanced Documentation**:

- Comprehensive README with quick setup guide
- Troubleshooting section for common issues
- Cross-platform installation instructions
- Project structure overview

---

### 📋 **Technical Improvements**

#### Code Organization

- Consolidated changelog from multiple files
- Improved error handling and logging
- Enhanced WebSocket connection management
- Better separation of concerns in logout logic

#### Security Enhancements

- Comprehensive cookie cleanup (regular + secure variants)
- Complete browser storage clearing
- Proper session termination
- Data isolation between users

#### Performance Optimizations

- Reduced unnecessary re-renders in contexts
- Improved WebSocket connection lifecycle
- Better memory management with proper cleanup

---

### 🔄 **Migration Notes**

#### For Developers

- Use `npm run setup` for new installations
- The old auto-close logic is commented out but preserved
- New logout hook available for consistent behavior
- SSL certificates now auto-generate

#### For Users

- Logout now completely clears all data
- PNL calculations are more accurate
- Pair movements are fully manual
- Schwab disconnect works properly

---

### 📚 **Documentation Updates**

- Enhanced README with automated setup instructions
- Added troubleshooting guide for common issues
- Documented all new scripts and their usage
- Created comprehensive changelog structure

---

## [v1.1.0] - Previous Release

### Added

- Initial WB Dashboard functionality
- Schwab API integration
- Real-time market data streaming
- User authentication system
- Trading pair management

### Features

- Account summary and portfolio overview
- Real-time trading pair monitoring
- Strategy configuration tools
- File handling utilities
- Account activity tracking

---

## [v1.0.0] - Initial Release

### Added

- Basic Next.js application structure
- Electron desktop app support
- Database integration with Prisma
- Authentication with NextAuth.js
- Basic UI components and styling
