import { cookies } from "next/headers";

export async function POST(req) {
  try {
    // Get the cookies object
    const cookiesObj = await cookies();

    // Clear all application cookies
    const allCookies = [
      // Schwab-related cookies
      "access_token",
      "authorization_code",
      "refresh_token",
      "client_correlId",
      "client_customerId",
      // NextAuth cookies
      "next-auth.session-token",
      "next-auth.csrf-token",
      "next-auth.callback-url",
      "__Secure-next-auth.session-token",
      "__Host-next-auth.csrf-token",
      // Any other application cookies
      "session",
      "user-preferences",
    ];

    // Create response
    const response = new Response(
      JSON.stringify({
        success: true,
        message: "Successfully logged out and cleared all user data",
      }),
      {
        status: 200,
        headers: { "Content-Type": "application/json" },
      }
    );

    // Clear each cookie by setting it to expire
    allCookies.forEach((cookieName) => {
      // Clear both regular and secure versions
      response.headers.append(
        "Set-Cookie",
        `${cookieName}=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT; HttpOnly; SameSite=Strict`
      );
      response.headers.append(
        "Set-Cookie",
        `${cookieName}=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT; HttpOnly; Secure; SameSite=Strict`
      );
      response.headers.append(
        "Set-Cookie",
        `__Secure-${cookieName}=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT; HttpOnly; Secure; SameSite=Strict`
      );
      response.headers.append(
        "Set-Cookie",
        `__Host-${cookieName}=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT; HttpOnly; Secure; SameSite=Strict`
      );
    });

    return response;
  } catch (error) {
    console.error("Error during full logout:", error);
    return new Response(
      JSON.stringify({
        success: false,
        error: "Failed to complete full logout",
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" },
      }
    );
  }
}
