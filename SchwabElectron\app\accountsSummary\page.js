"use client";


import ProtectedRoute from "../../components/ProtectedRoute";
import AccountBalancesCard from "./AccountBalancesCard";
import PositionsCard from "./PositionsCard";
import OrdersCard from "./OrdersCard";
import TransactionsCard from "./TransactionsCard";
import { motion, AnimatePresence } from "framer-motion";
import { useState } from "react";
import { useEndpointAppContext } from "@/components/EndpointAppContext";
import DateRangeSelector from "@/components/DateRangeSelector";

export default function AccountsSummary() {
  const [focused, setFocused] = useState("balances");

  // Get date range context for header display
  const {
    transactionsDateRange,
    ordersDateRange,
    updateTransactionsDateRange,
    updateOrdersDateRange
  } = useEndpointAppContext();
  const cards = [
    {
      key: "balances",
      title: "Account Balances",
      color: "from-slate-700 to-slate-800 dark:from-slate-800 dark:to-slate-900",
      component: <AccountBalancesCard />,
    },
    {
      key: "positions",
      title: "Positions",
      color: "from-blue-700 to-blue-800 dark:from-blue-800 dark:to-blue-900",
      component: <PositionsCard />,
    },
    {
      key: "orders",
      title: "Orders",
      color: "from-indigo-700 to-indigo-800 dark:from-indigo-800 dark:to-indigo-900",
      component: <OrdersCard />,
    },
    {
      key: "transactions",
      title: "Transactions",
      color: "from-gray-700 to-gray-800 dark:from-gray-800 dark:to-gray-900",
      component: <TransactionsCard />,
    },
  ];

  const focusedCard = cards.find((c) => c.key === focused);
  const smallCards = cards.filter((c) => c.key !== focused);

  return (
    <ProtectedRoute>
      <div className="min-h-screen h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-blue-900/20 dark:to-indigo-900/20 flex flex-col">
        <div className="w-full flex-1 flex flex-col">
          {/* Professional Header section */}
          <div className="relative overflow-hidden bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 dark:from-black dark:via-slate-900 dark:to-blue-900 text-white py-8 px-6 shadow-xl">
            {/* Subtle Background Pattern */}
            <div className="absolute inset-0 bg-black/20">
              <div className="absolute inset-0" style={{
                backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Cpath d='M20 20c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm10 0c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z'/%3E%3C/g%3E%3C/svg%3E")`,
              }}></div>
            </div>

            <div className="relative flex justify-between items-start">
              <div>
                <div className="mb-3">
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-white/10 text-white backdrop-blur-sm border border-white/20">
                    Portfolio Overview
                  </span>
                </div>
                <h1 className="text-3xl font-bold mb-2">Account Summary</h1>
                <p className="text-slate-300 text-base font-light">Comprehensive view of your account balances, positions, orders, and transaction history</p>
              </div>

              {/* Professional Date Range Selector for Orders/Transactions */}
              {focused === 'orders' && (
                <div className="flex items-center gap-3 bg-white/10 backdrop-blur-sm rounded-lg px-4 py-2 border border-white/20">
                  <span className="text-sm font-medium text-white">
                    Orders Period:
                  </span>
                  <DateRangeSelector
                    selectedRange={ordersDateRange}
                    onRangeChange={updateOrdersDateRange}
                  />
                </div>
              )}

              {focused === 'transactions' && (
                <div className="flex items-center gap-3 bg-white/10 backdrop-blur-sm rounded-lg px-4 py-2 border border-white/20">
                  <span className="text-sm font-medium text-white">
                    Transactions Period:
                  </span>
                  <DateRangeSelector
                    selectedRange={transactionsDateRange}
                    onRangeChange={updateTransactionsDateRange}
                  />
                </div>
              )}
            </div>

            {/* Subtle Geometric Elements */}
            <div className="absolute top-4 right-8 w-16 h-16 border border-white/10 rounded-full"></div>
            <div className="absolute bottom-4 left-8 w-12 h-12 border border-blue-400/20 rounded-lg rotate-45"></div>
          </div>

          {/* Animated Cards Layout - Full Height */}
          <div className="flex flex-col gap-4 px-6 flex-1 min-h-0 pb-6">
            {/* Top row: small cards (1/3 height) */}
            <div className="flex gap-4 mb-2 h-1/3 min-h-[180px] max-h-[33vh]">
              {smallCards.map((card) => (
                <motion.div
                  key={card.key}
                  layout
                  whileHover={{ scale: 1.04 }}
                  className="flex-1 min-w-0 cursor-pointer rounded-xl shadow-md dark:shadow-gray-900 bg-white dark:bg-gray-800 overflow-hidden flex flex-col transition-all duration-300 border-2 border-transparent hover:border-indigo-400"
                  onClick={() => setFocused(card.key)}
                >
                  <div className={`bg-gradient-to-r ${card.color} py-2 px-4`}>
                    <h2 className="text-lg font-semibold text-white select-none">{card.title}</h2>
                  </div>
                  <div className="flex-1 p-2 overflow-auto">
                    {card.component}
                  </div>
                </motion.div>
              ))}
            </div>
            {/* Focused card: large and animated (2/3 height) */}
            <AnimatePresence mode="wait">
              <motion.div
                key={focusedCard.key}
                layout
                initial={{ opacity: 0, y: 40 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 40 }}
                transition={{ duration: 0.4 }}
                className="max-w-full self-stretch bg-white dark:bg-gray-800 rounded-xl shadow-lg dark:shadow-gray-900 overflow-hidden flex flex-col flex-1 min-h-0"
                style={{ minHeight: 0, maxHeight: '67vh' }}
              >
                <div
                  className={`bg-gradient-to-r ${focusedCard.color} py-3 px-6 cursor-pointer group transition-all duration-300`}
                  onClick={() => {
                    // Optionally collapse or do nothing
                  }}
                >
                  <h2 className="text-xl font-bold text-white group-hover:underline select-none">
                    {focusedCard.title}
                  </h2>
                </div>
                <div className="flex-1 min-h-0 p-4 overflow-auto">
                  {focusedCard.component}
                </div>
              </motion.div>
            </AnimatePresence>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
