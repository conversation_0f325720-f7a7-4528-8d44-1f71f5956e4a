"use client";

import { useState } from "react";
import ProtectedRoute from "../../components/ProtectedRoute";
import { useExcelData } from "../Strategies/WB/ExcelDataContext";
import TooltipButton from "@/components/TooltipButton";

export default function FileHandler() {
  const {
    shortOpenTableData,
    shortLoadedTableData,
    longOpenTableData,
    longLoadedTableData,
    shortClosedTableData,
    longClosedTableData,
    setShortOpenTableData,
    setShortLoadedTableData,
    setLongOpenTableData,
    setLongLoadedTableData,
    setShortClosedTableData,
    setLongClosedTableData,
  } = useExcelData();

  const [isSaving, setIsSaving] = useState(false);
  const [saveStatus, setSaveStatus] = useState("");

  const [loadStatus, setLoadStatus] = useState("");
  const [dbStatus, setDbStatus] = useState("");
  const [isLoadingFromDb, setIsLoadingFromDb] = useState(false);
  const [activeTab, setActiveTab] = useState("excelData");

  // Function to save Excel data to a file
  const saveExcelData = () => {
    const excelData = {
      shortOpenTableData,
      shortLoadedTableData,
      longOpenTableData,
      longLoadedTableData,
      shortClosedTableData,
      longClosedTableData,
    };

    const a = document.createElement("a");
    const file = new Blob([JSON.stringify(excelData, null, 2)], {
      type: "application/json",
    });
    const url = URL.createObjectURL(file);
    a.href = url;
    a.download = `excel_data_backup_${new Date()
      .toISOString()
      .slice(0, 10)}.json`;
    a.click();
    setTimeout(() => {
      URL.revokeObjectURL(url);
    }, 0);
  };

  // Function to load Excel data from a file
  const loadExcelData = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (event) => {
      try {
        const data = JSON.parse(event.target.result);

        // Validate the data structure
        if (
          !data.shortOpenTableData ||
          !data.shortLoadedTableData ||
          !data.longOpenTableData ||
          !data.longLoadedTableData ||
          !data.shortClosedTableData ||
          !data.longClosedTableData
        ) {
          throw new Error("Invalid Excel data format");
        }

        // Update the Excel data
        setShortOpenTableData(data.shortOpenTableData);
        setShortLoadedTableData(data.shortLoadedTableData);
        setLongOpenTableData(data.longOpenTableData);
        setLongLoadedTableData(data.longLoadedTableData);
        setShortClosedTableData(data.shortClosedTableData);
        setLongClosedTableData(data.longClosedTableData);

        setLoadStatus("Excel data loaded successfully");
        setTimeout(() => setLoadStatus(""), 3000);
      } catch (error) {
        console.error("Error loading Excel data:", error);
        setLoadStatus(`Error: ${error.message}`);
        setTimeout(() => setLoadStatus(""), 5000);
      }
    };
    reader.readAsText(file);
  };

  // Function to handle clicking the load button
  const handleLoadClick = () => {
    const input = document.createElement("input");
    input.type = "file";
    input.accept = ".json";
    input.onchange = loadExcelData;
    input.click();
  };

  // Function to load data from database
  const loadFromDatabase = async () => {
    try {
      setIsLoadingFromDb(true);
      setDbStatus("Loading data from database...");

      // Load Excel data first
      const excelResponse = await fetch("/api/mongodb/excel");

      if (excelResponse.ok) {
        const excelData = await excelResponse.json();

        if (excelData.success && excelData.excelData) {
          // Update Excel data
          setShortOpenTableData(excelData.excelData.shortOpenTableData || []);
          setShortLoadedTableData(
            excelData.excelData.shortLoadedTableData || []
          );
          setLongOpenTableData(excelData.excelData.longOpenTableData || []);
          setLongLoadedTableData(excelData.excelData.longLoadedTableData || []);
          setShortClosedTableData(
            excelData.excelData.shortClosedTableData || []
          );
          setLongClosedTableData(excelData.excelData.longClosedTableData || []);

          setDbStatus("Excel data loaded successfully from database");
        } else {
          setDbStatus("No Excel data found in database");
        }
      } else {
        console.error("Failed to fetch Excel data:", excelResponse.statusText);
        setDbStatus(`Error loading Excel data: ${excelResponse.statusText}`);
      }

      setTimeout(() => setDbStatus(""), 3000);
    } catch (error) {
      console.error("Error loading data from database:", error);
      setDbStatus(`Error: ${error.message}`);
      setTimeout(() => setDbStatus(""), 5000);
    } finally {
      setIsLoadingFromDb(false);
    }
  };

  return (
    <ProtectedRoute>
      <div className='min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-blue-900/20 dark:to-indigo-900/20'>
        {/* Professional Hero Section */}
        <div className='relative overflow-hidden bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 dark:from-black dark:via-slate-900 dark:to-blue-900'>
          {/* Subtle Background Pattern */}
          <div className='absolute inset-0 bg-black/20'>
            <div
              className='absolute inset-0'
              style={{
                backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Cpath d='M20 20c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm10 0c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z'/%3E%3C/g%3E%3C/svg%3E")`,
              }}
            ></div>
          </div>

          <div className='relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16'>
            <div className='text-center'>
              <h1 className='text-4xl md:text-5xl font-bold text-white mb-6 leading-tight'>
                File Management &
                <span className='bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent'>
                  {" "}
                  Backup
                </span>
              </h1>
            </div>
          </div>

          {/* Subtle Geometric Elements */}
          <div className='absolute top-1/4 left-8 w-24 h-24 border border-white/10 rounded-full'></div>
          <div className='absolute bottom-1/4 right-8 w-16 h-16 border border-blue-400/20 rounded-lg rotate-45'></div>
          <div className='absolute top-1/2 right-1/4 w-12 h-12 bg-gradient-to-br from-blue-500/10 to-cyan-500/10 rounded-full'></div>
        </div>

        <main className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8'>
          {/* Professional Tab Navigation */}
          <div className='mb-12'>
            <div className='flex justify-center'>
              <div className='bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-2'>
                <div className='flex space-x-2'>
                  <button
                    className={`relative px-6 py-3 rounded-lg font-semibold transition-all duration-300 ${
                      activeTab === "excelData"
                        ? "bg-blue-600 text-white shadow-lg transform scale-105"
                        : "text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20"
                    }`}
                    onClick={() => setActiveTab("excelData")}
                  >
                    <div className='flex items-center space-x-2'>
                      <svg
                        xmlns='http://www.w3.org/2000/svg'
                        className='h-5 w-5'
                        fill='none'
                        viewBox='0 0 24 24'
                        stroke='currentColor'
                      >
                        <path
                          strokeLinecap='round'
                          strokeLinejoin='round'
                          strokeWidth={2}
                          d='M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
                        />
                      </svg>
                      <span>File Operations</span>
                    </div>
                  </button>
                  <button
                    className={`relative px-6 py-3 rounded-lg font-semibold transition-all duration-300 ${
                      activeTab === "database"
                        ? "bg-indigo-600 text-white shadow-lg transform scale-105"
                        : "text-gray-600 dark:text-gray-400 hover:text-indigo-600 dark:hover:text-indigo-400 hover:bg-indigo-50 dark:hover:bg-indigo-900/20"
                    }`}
                    onClick={() => setActiveTab("database")}
                  >
                    <div className='flex items-center space-x-2'>
                      <svg
                        xmlns='http://www.w3.org/2000/svg'
                        className='h-5 w-5'
                        fill='none'
                        viewBox='0 0 24 24'
                        stroke='currentColor'
                      >
                        <path
                          strokeLinecap='round'
                          strokeLinejoin='round'
                          strokeWidth={2}
                          d='M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4'
                        />
                      </svg>
                      <span>Database Operations</span>
                    </div>
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Professional File Operations Section */}
          {activeTab === "excelData" && (
            <div className='grid grid-cols-1 lg:grid-cols-2 gap-8'>
              {/* File Operations Card */}
              <div className='group relative overflow-hidden rounded-xl bg-gradient-to-br from-slate-700 to-slate-800 shadow-xl shadow-slate-500/20 hover:shadow-slate-500/40 hover:-translate-y-1 transition-all duration-300 border border-white/10'>
                {/* Subtle Background */}
                <div className='absolute inset-0 bg-black/20'>
                  <div className='absolute inset-0 bg-gradient-to-br from-white/5 to-transparent'></div>
                </div>

                <div className='relative p-8 text-white'>
                  <div className='flex items-center justify-between mb-6'>
                    <div className='w-12 h-12 bg-white/10 rounded-lg flex items-center justify-center backdrop-blur-sm border border-white/20'>
                      <svg
                        xmlns='http://www.w3.org/2000/svg'
                        className='h-6 w-6'
                        fill='none'
                        viewBox='0 0 24 24'
                        stroke='currentColor'
                      >
                        <path
                          strokeLinecap='round'
                          strokeLinejoin='round'
                          strokeWidth={1.5}
                          d='M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
                        />
                      </svg>
                    </div>
                    <div className='text-sm font-medium text-white/80'>
                      File Operations
                    </div>
                  </div>

                  <h3 className='text-xl font-semibold mb-4 group-hover:text-blue-200 transition-colors duration-300'>
                    Local File Management
                  </h3>

                  <p className='text-white/80 text-sm leading-relaxed mb-8'>
                    Export your trading data to secure JSON files for local
                    backup and import previously saved configurations to restore
                    your trading setup.
                  </p>

                  <div className='space-y-4'>
                    <TooltipButton
                      onClick={saveExcelData}
                      className='w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 flex items-center justify-center'
                      tooltipText='Export all trading data to a secure JSON backup file'
                    >
                      <svg
                        xmlns='http://www.w3.org/2000/svg'
                        className='h-5 w-5 mr-3'
                        fill='none'
                        viewBox='0 0 24 24'
                        stroke='currentColor'
                      >
                        <path
                          strokeLinecap='round'
                          strokeLinejoin='round'
                          strokeWidth={2}
                          d='M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
                        />
                      </svg>
                      Export Data to File
                    </TooltipButton>

                    <TooltipButton
                      onClick={handleLoadClick}
                      className='w-full bg-emerald-600 hover:bg-emerald-700 text-white font-semibold py-3 px-6 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 flex items-center justify-center'
                      tooltipText='Import trading data from a previously exported JSON file'
                    >
                      <svg
                        xmlns='http://www.w3.org/2000/svg'
                        className='h-5 w-5 mr-3'
                        fill='none'
                        viewBox='0 0 24 24'
                        stroke='currentColor'
                      >
                        <path
                          strokeLinecap='round'
                          strokeLinejoin='round'
                          strokeWidth={2}
                          d='M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10'
                        />
                      </svg>
                      Import Data from File
                    </TooltipButton>
                  </div>
                </div>
              </div>

              {/* Data Summary Card */}
              <div className='bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden'>
                <div className='bg-gradient-to-r from-blue-600 to-indigo-600 p-6'>
                  <div className='flex items-center justify-between'>
                    <div>
                      <h3 className='text-xl font-semibold text-white'>
                        Portfolio Data Overview
                      </h3>
                      <p className='text-blue-100 text-sm mt-1'>
                        Current data statistics and metrics
                      </p>
                    </div>
                    <div className='w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center backdrop-blur-sm'>
                      <svg
                        xmlns='http://www.w3.org/2000/svg'
                        className='h-6 w-6 text-white'
                        fill='none'
                        viewBox='0 0 24 24'
                        stroke='currentColor'
                      >
                        <path
                          strokeLinecap='round'
                          strokeLinejoin='round'
                          strokeWidth={2}
                          d='M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z'
                        />
                      </svg>
                    </div>
                  </div>
                </div>

                <div className='p-6'>
                  <div className='grid grid-cols-2 gap-4'>
                    <div className='bg-slate-50 dark:bg-slate-800/50 rounded-lg p-4'>
                      <div className='text-2xl font-bold text-slate-700 dark:text-slate-300'>
                        {shortOpenTableData.length}
                      </div>
                      <div className='text-sm text-slate-600 dark:text-slate-400'>
                        Short Open
                      </div>
                    </div>
                    <div className='bg-blue-50 dark:bg-blue-800/50 rounded-lg p-4'>
                      <div className='text-2xl font-bold text-blue-700 dark:text-blue-300'>
                        {shortLoadedTableData.length}
                      </div>
                      <div className='text-sm text-blue-600 dark:text-blue-400'>
                        Short Loaded
                      </div>
                    </div>
                    <div className='bg-emerald-50 dark:bg-emerald-800/50 rounded-lg p-4'>
                      <div className='text-2xl font-bold text-emerald-700 dark:text-emerald-300'>
                        {longOpenTableData.length}
                      </div>
                      <div className='text-sm text-emerald-600 dark:text-emerald-400'>
                        Long Open
                      </div>
                    </div>
                    <div className='bg-indigo-50 dark:bg-indigo-800/50 rounded-lg p-4'>
                      <div className='text-2xl font-bold text-indigo-700 dark:text-indigo-300'>
                        {longLoadedTableData.length}
                      </div>
                      <div className='text-sm text-indigo-600 dark:text-indigo-400'>
                        Long Loaded
                      </div>
                    </div>
                    <div className='bg-purple-50 dark:bg-purple-800/50 rounded-lg p-4'>
                      <div className='text-2xl font-bold text-purple-700 dark:text-purple-300'>
                        {shortClosedTableData.length}
                      </div>
                      <div className='text-sm text-purple-600 dark:text-purple-400'>
                        Short Closed
                      </div>
                    </div>
                    <div className='bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4'>
                      <div className='text-2xl font-bold text-gray-700 dark:text-gray-300'>
                        {longClosedTableData.length}
                      </div>
                      <div className='text-sm text-gray-600 dark:text-gray-400'>
                        Long Closed
                      </div>
                    </div>
                  </div>

                  <div className='mt-6 pt-6 border-t border-gray-200 dark:border-gray-700'>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm font-medium text-gray-700 dark:text-gray-300'>
                        Total Entries
                      </span>
                      <span className='text-2xl font-bold text-gray-900 dark:text-white'>
                        {shortOpenTableData.length +
                          shortLoadedTableData.length +
                          longOpenTableData.length +
                          longLoadedTableData.length +
                          shortClosedTableData.length +
                          longClosedTableData.length}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
          {/* Professional Database Operations Section */}
          {activeTab === "database" && (
            <div className='max-w-4xl mx-auto'>
              <div className='group relative overflow-hidden rounded-xl bg-gradient-to-br from-indigo-700 to-indigo-800 shadow-xl shadow-indigo-500/20 hover:shadow-indigo-500/40 hover:-translate-y-1 transition-all duration-300 border border-white/10'>
                {/* Subtle Background */}
                <div className='absolute inset-0 bg-black/20'>
                  <div className='absolute inset-0 bg-gradient-to-br from-white/5 to-transparent'></div>
                </div>

                <div className='relative p-8 text-white'>
                  <div className='text-center mb-8'>
                    <div className='w-16 h-16 bg-white/10 rounded-xl flex items-center justify-center mx-auto mb-4 backdrop-blur-sm border border-white/20'>
                      <svg
                        xmlns='http://www.w3.org/2000/svg'
                        className='h-8 w-8'
                        fill='none'
                        viewBox='0 0 24 24'
                        stroke='currentColor'
                      >
                        <path
                          strokeLinecap='round'
                          strokeLinejoin='round'
                          strokeWidth={1.5}
                          d='M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4'
                        />
                      </svg>
                    </div>

                    <h3 className='text-2xl font-semibold mb-4 group-hover:text-blue-200 transition-colors duration-300'>
                      Database Operations
                    </h3>

                    <p className='text-white/80 text-base leading-relaxed max-w-2xl mx-auto mb-8'>
                      Secure cloud-based storage for your trading data with
                      automatic synchronization and enterprise-grade backup
                      capabilities.
                    </p>
                  </div>

                  <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                    <TooltipButton
                      onClick={async () => {
                        try {
                          setIsSaving(true);
                          setSaveStatus("Saving Excel data to database...");

                          // Prepare the Excel data
                          const excelData = {
                            shortOpenTableData,
                            shortLoadedTableData,
                            longOpenTableData,
                            longLoadedTableData,
                            shortClosedTableData,
                            longClosedTableData,
                          };

                          // Save Excel data to database
                          const response = await fetch("/api/mongodb/excel", {
                            method: "POST",
                            headers: {
                              "Content-Type": "application/json",
                            },
                            body: JSON.stringify(excelData),
                          });

                          const data = await response.json();

                          if (!response.ok) {
                            throw new Error(
                              data.error || "Failed to save Excel data"
                            );
                          }

                          setSaveStatus(
                            "Excel data saved successfully to database"
                          );
                          setTimeout(() => setSaveStatus(""), 3000);
                        } catch (error) {
                          console.error(
                            "Error saving data to database:",
                            error
                          );
                          setSaveStatus(`Error: ${error.message}`);
                          setTimeout(() => setSaveStatus(""), 5000);
                        } finally {
                          setIsSaving(false);
                        }
                      }}
                      disabled={isSaving}
                      className={`${
                        isSaving
                          ? "bg-gray-500 cursor-not-allowed"
                          : "bg-purple-600 hover:bg-purple-700"
                      } w-full text-white font-semibold py-4 px-6 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 flex items-center justify-center`}
                      tooltipText='Securely backup all trading data to the cloud database'
                    >
                      <svg
                        xmlns='http://www.w3.org/2000/svg'
                        className='h-6 w-6 mr-3'
                        fill='none'
                        viewBox='0 0 24 24'
                        stroke='currentColor'
                      >
                        <path
                          strokeLinecap='round'
                          strokeLinejoin='round'
                          strokeWidth={2}
                          d='M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12'
                        />
                      </svg>
                      {isSaving ? "Backing Up..." : "Backup to Database"}
                    </TooltipButton>

                    <TooltipButton
                      onClick={loadFromDatabase}
                      disabled={isLoadingFromDb}
                      className={`${
                        isLoadingFromDb
                          ? "bg-gray-500 cursor-not-allowed"
                          : "bg-emerald-600 hover:bg-emerald-700"
                      } w-full text-white font-semibold py-4 px-6 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 flex items-center justify-center`}
                      tooltipText='Restore trading data from the cloud database'
                    >
                      <svg
                        xmlns='http://www.w3.org/2000/svg'
                        className='h-6 w-6 mr-3'
                        fill='none'
                        viewBox='0 0 24 24'
                        stroke='currentColor'
                      >
                        <path
                          strokeLinecap='round'
                          strokeLinejoin='round'
                          strokeWidth={2}
                          d='M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 11l3 3m0 0l3-3m-3 3V8'
                        />
                      </svg>
                      {isLoadingFromDb
                        ? "Restoring..."
                        : "Restore from Database"}
                    </TooltipButton>
                  </div>

                  <div className='mt-8 p-4 bg-white/10 rounded-lg backdrop-blur-sm border border-white/20'>
                    <div className='flex items-center text-sm text-white/90'>
                      <svg
                        xmlns='http://www.w3.org/2000/svg'
                        className='h-5 w-5 mr-2 text-blue-300'
                        fill='none'
                        viewBox='0 0 24 24'
                        stroke='currentColor'
                      >
                        <path
                          strokeLinecap='round'
                          strokeLinejoin='round'
                          strokeWidth={2}
                          d='M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z'
                        />
                      </svg>
                      <span>
                        Enterprise-grade encryption and automatic versioning
                        ensure your data is always secure and recoverable.
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Professional Status Messages */}
          {(loadStatus || saveStatus || dbStatus) && (
            <div className='max-w-4xl mx-auto mt-8'>
              <div className='space-y-4'>
                {loadStatus && (
                  <div
                    className={`p-4 rounded-xl shadow-sm border ${
                      loadStatus.includes("Error")
                        ? "bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 text-red-800 dark:text-red-300 border-red-200 dark:border-red-800"
                        : "bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 text-green-800 dark:text-green-300 border-green-200 dark:border-green-800"
                    }`}
                  >
                    <div className='flex items-center gap-3'>
                      <span className='text-lg'>
                        {loadStatus.includes("Error") ? "❌" : "✅"}
                      </span>
                      <span className='font-medium'>{loadStatus}</span>
                    </div>
                  </div>
                )}

                {saveStatus && (
                  <div
                    className={`p-4 rounded-xl shadow-sm border ${
                      saveStatus.includes("Error")
                        ? "bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 text-red-800 dark:text-red-300 border-red-200 dark:border-red-800"
                        : "bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 text-green-800 dark:text-green-300 border-green-200 dark:border-green-800"
                    }`}
                  >
                    <div className='flex items-center gap-3'>
                      <span className='text-lg'>
                        {saveStatus.includes("Error") ? "❌" : "✅"}
                      </span>
                      <span className='font-medium'>{saveStatus}</span>
                    </div>
                  </div>
                )}

                {dbStatus && (
                  <div
                    className={`p-4 rounded-xl shadow-sm border ${
                      dbStatus.includes("Error")
                        ? "bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 text-red-800 dark:text-red-300 border-red-200 dark:border-red-800"
                        : "bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 text-green-800 dark:text-green-300 border-green-200 dark:border-green-800"
                    }`}
                  >
                    <div className='flex items-center gap-3'>
                      <span className='text-lg'>
                        {dbStatus.includes("Error") ? "❌" : "✅"}
                      </span>
                      <span className='font-medium'>{dbStatus}</span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Professional Warning Notice */}
          <div className='max-w-4xl mx-auto mt-12 mb-8'>
            <div className='bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20 rounded-xl border border-amber-200 dark:border-amber-800 p-6 shadow-sm'>
              <div className='flex items-start'>
                <div className='flex-shrink-0'>
                  <div className='w-10 h-10 bg-amber-100 dark:bg-amber-900/30 rounded-lg flex items-center justify-center'>
                    <svg
                      className='h-6 w-6 text-amber-600 dark:text-amber-400'
                      fill='none'
                      viewBox='0 0 24 24'
                      stroke='currentColor'
                    >
                      <path
                        strokeLinecap='round'
                        strokeLinejoin='round'
                        strokeWidth={2}
                        d='M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z'
                      />
                    </svg>
                  </div>
                </div>
                <div className='ml-4'>
                  <h3 className='text-lg font-semibold text-amber-800 dark:text-amber-200 mb-2'>
                    Data Management Notice
                  </h3>
                  <p className='text-amber-700 dark:text-amber-300 leading-relaxed'>
                    This module provides enterprise-grade backup and recovery
                    capabilities for your trading data. Please exercise caution
                    when performing restore operations, as they will completely
                    replace your current dataset. Always ensure you have recent
                    backups before proceeding with data restoration.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </ProtectedRoute>
  );
}
