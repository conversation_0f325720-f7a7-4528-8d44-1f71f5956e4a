/**
 * Prisma Data Access Layer (DAL)
 *
 * This file provides a layer of abstraction for database operations using Prisma.
 * It replaces the direct MongoDB operations in lib/mongodb.js.
 */

import { PrismaClient } from '@prisma/client';

// Create a new PrismaClient instance
const prisma = new PrismaClient();

/**
 * User-related operations
 */

// Get a user by email
export async function getUserByEmail(email) {
  try {
    console.log('Getting user by email:', email);

    // Log available models
    const availableModels = Object.keys(prisma).filter(key =>
      !key.startsWith('_') &&
      !key.startsWith('$') &&
      typeof prisma[key] === 'object'
    );
    console.log('Available models:', availableModels);

    // Determine which model to use for users
    let userModel;
    if (availableModels.includes('users')) {
      userModel = prisma.users;
    } else {
      console.error('No user model found in Prisma client');
      return null;
    }

    // Use the determined model
    const user = await userModel.findUnique({
      where: { email },
    });

    console.log('User found:', user ? 'Yes' : 'No');
    if (user) {
      // With Prisma, the id is already a string
      // Just make sure we have a consistent property name
      user.id = user.id || user._id;

      // Log the user object for debugging
      console.log('User object:', {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role
      });
    }
    return user;
  } catch (error) {
    console.error('Error getting user by email:', error);
    return null;
  }
}


// Verify user credentials
export async function verifyUserCredentials(email) {
  try {
    const user = await getUserByEmail(email);
    return user;
  } catch (error) {
    console.error('Error verifying user credentials:', error);
    return null;
  }
}

/**
 * Trading Pair operations
 */

// Save trading pairs
export async function saveTradingPairs(pairs, userId) {
  try {
    console.log('saveTradingPairs called with pairs:', pairs.length);
    console.log('saveTradingPairs called with userId:', userId);

    // Delete all existing pairs for this user
    await prisma.tradingPair.deleteMany({
      where: { userId },
    });

    // Prepare the current date
    const now = new Date();

    // Log a sample pair for debugging
    if (pairs.length > 0) {
      console.log('Sample pair to save:', {
        pairKey: pairs[0].pairKey,
        status: pairs[0].status,
        combinedPNL: pairs[0].combinedPNL
      });
    }

    // Create all new pairs - we need to handle each pair individually
    // since createMany doesn't work well with JSON fields in MongoDB
    let insertedCount = 0;

    for (const pair of pairs) {
      try {
        // For pairKey, ensure it's a string or null
        const pairKeyValue = pair.pairKey ? String(pair.pairKey) : null;

        // Transform the shortComponent to match the expected schema
        const shortComponent = {
          dividendUserValue: parseInt(pair.shortComponent?.dividendUserValue || 0),
          dollarCost: parseInt(pair.shortComponent?.dollarCost || 0),
          expectedQuantity: pair.shortComponent?.expectedQuantity || "0",
          formattedAmt: pair.shortComponent?.formattedAmt || "0",
          formattedAsk: pair.shortComponent?.formattedAsk || "0.00",
          formattedBid: pair.shortComponent?.formattedBid || "0.00",
          formattedChange: pair.shortComponent?.formattedChange || "0.0%",
          formattedCost: pair.shortComponent?.formattedCost || "0.00",
          formattedDividend: pair.shortComponent?.formattedDividend || "0.00",
          formattedLast: pair.shortComponent?.formattedLast || "0.00",
          formattedLoadedVolume: pair.shortComponent?.formattedLoadedVolume || "0",
          formattedSpreadUser: pair.shortComponent?.formattedSpreadUser || "0.00",
          formattedUserDividend: pair.shortComponent?.formattedUserDividend || "0.00",
          formattedVolume: pair.shortComponent?.formattedVolume || "0",
          id: pair.shortComponent?.id || "0",
          pnl: parseInt(pair.shortComponent?.pnl || 0),
          sectorValue: pair.shortComponent?.sectorValue || "",
          spreadUserValue: pair.shortComponent?.spreadUserValue || "0",
          // spreadValue is a Json type in the schema
          spreadValue: pair.shortComponent?.spreadValue || 0,
          statusValue: pair.shortComponent?.statusValue || "",
          ticker: pair.shortComponent?.ticker || ""
        };

        // Transform the longComponent to match the expected schema
        const longComponent = {
          dividendUserValue: parseInt(pair.longComponent?.dividendUserValue || 0),
          dollarCost: parseInt(pair.longComponent?.dollarCost || 0),
          // expectedQuantity is a Json type in the schema
          expectedQuantity: pair.longComponent?.expectedQuantity || "0",
          formattedAmt: pair.longComponent?.formattedAmt || "0",
          formattedAsk: pair.longComponent?.formattedAsk || "0.00",
          formattedBid: pair.longComponent?.formattedBid || "0.00",
          formattedChange: pair.longComponent?.formattedChange || "0.0%",
          formattedCost: pair.longComponent?.formattedCost || "0.00",
          formattedDividend: pair.longComponent?.formattedDividend || "0.00",
          formattedLast: pair.longComponent?.formattedLast || "0.00",
          formattedLoadedVolume: pair.longComponent?.formattedLoadedVolume || "0",
          formattedSpreadUser: pair.longComponent?.formattedSpreadUser || "0.00",
          formattedUserDividend: pair.longComponent?.formattedUserDividend || "0.00",
          formattedVolume: pair.longComponent?.formattedVolume || "0",
          id: pair.longComponent?.id || "0",
          pnl: parseInt(pair.longComponent?.pnl || 0),
          sectorValue: pair.longComponent?.sectorValue || "",
          // spreadUserValue is a Json type in the schema
          spreadUserValue: pair.longComponent?.spreadUserValue || "0",
          // spreadValue is a Json type in the schema
          spreadValue: pair.longComponent?.spreadValue || 0,
          statusValue: pair.longComponent?.statusValue || "",
          ticker: pair.longComponent?.ticker || ""
        };

        await prisma.tradingPair.create({
          data: {
            pairKey: pairKeyValue,
            status: pair.status || "",
            shortComponent: shortComponent,
            longComponent: longComponent,
            combinedPNL: pair.combinedPNL || "0",
            userId,
            createdAt: now,
            updatedAt: now,
          },
        });

        insertedCount++;
      } catch (error) {
        console.error(`Error inserting pair:`, error);
        // Continue with the next pair
      }
    }

    const createdPairs = { count: insertedCount };

    return {
      success: true,
      insertedCount: createdPairs.count,
      overwritten: true,
    };
  } catch (error) {
    console.error('Error saving trading pairs:', error);
    throw error;
  }
}

// Get trading pairs
export async function getTradingPairs(userId, status = null) {
  try {
    const whereClause = { userId };
    if (status) {
      whereClause.status = status;
    }

    const pairs = await prisma.tradingPair.findMany({
      where: whereClause,
      orderBy: { createdAt: 'desc' },
    });

    return pairs;
  } catch (error) {
    console.error('Error getting trading pairs:', error);
    return [];
  }
}

/**
 * Excel Data operations
 */

// Save Excel data
export async function saveExcelData(excelData, userId) {
  try {
    // Delete all existing Excel data for this user
    await prisma.excelData.deleteMany({
      where: { userId },
    });

    // Prepare the current date
    const now = new Date();

    // Create new Excel data
    const createdExcelData = await prisma.excelData.create({
      data: {
        shortOpenTableData: excelData.shortOpenTableData || [],
        shortLoadedTableData: excelData.shortLoadedTableData || [],
        longOpenTableData: excelData.longOpenTableData || [],
        longLoadedTableData: excelData.longLoadedTableData || [],
        shortClosedTableData: excelData.shortClosedTableData || [],
        longClosedTableData: excelData.longClosedTableData || [],
        userId,
        createdAt: now,
        updatedAt: now,
      },
    });

    return {
      success: true,
      insertedId: createdExcelData.id,
      overwritten: true,
    };
  } catch (error) {
    console.error('Error saving Excel data:', error);
    throw error;
  }
}

// Get Excel data
export async function getExcelData(userId) {
  try {
    const excelData = await prisma.excelData.findFirst({
      where: { userId },
      orderBy: { createdAt: 'desc' },
    });

    return excelData;
  } catch (error) {
    console.error('Error getting Excel data:', error);
    return null;
  }
}
