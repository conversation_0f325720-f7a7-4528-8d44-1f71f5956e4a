/**
 * Validates stock symbols
 * @param {Array} symbols - Array of stock symbols to validate
 * @returns {object} - Validation result
 */
export function validateStockSymbols(symbols) {
  // Check if symbols is an array
  if (!symbols || !Array.isArray(symbols)) {
    return {
      valid: false,
      message: "Symbols must be an array"
    };
  }

  // Check if symbols array is empty
  if (symbols.length === 0) {
    return {
      valid: false,
      message: "Symbols array cannot be empty"
    };
  }

  // Sanitize symbols (convert to uppercase and trim whitespace)
  const sanitizedSymbols = symbols.map(symbol => {
    if (typeof symbol !== 'string') {
      return String(symbol).trim().toUpperCase();
    }
    return symbol.trim().toUpperCase();
  });

  return {
    valid: true,
    sanitizedSymbols
  };
}
