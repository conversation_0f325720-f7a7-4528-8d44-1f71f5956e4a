/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/[...nextauth]/route";
exports.ids = ["app/api/auth/[...nextauth]/route"];
exports.modules = {

/***/ "(rsc)/./app/api/auth/[...nextauth]/route.js":
/*!*********************************************!*\
  !*** ./app/api/auth/[...nextauth]/route.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ handler),\n/* harmony export */   POST: () => (/* binding */ handler),\n/* harmony export */   dynamic: () => (/* binding */ dynamic),\n/* harmony export */   revalidate: () => (/* binding */ revalidate)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var _lib_auth_options__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth-options */ \"(rsc)/./lib/auth-options.js\");\nconst dynamic = \"force-dynamic\";\nconst revalidate = 0;\n\n\nconst NextAuth = next_auth__WEBPACK_IMPORTED_MODULE_0__[\"default\"] || next_auth__WEBPACK_IMPORTED_MODULE_0__;\nconst handler = NextAuth(_lib_auth_options__WEBPACK_IMPORTED_MODULE_1__.authOptions);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvYXBpL2F1dGgvWy4uLm5leHRhdXRoXS9yb3V0ZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBTyxNQUFNQSxVQUFVLGdCQUFnQjtBQUNoQyxNQUFNQyxhQUFhLEVBQUU7QUFFVztBQUNVO0FBRWpELE1BQU1HLFdBQVdGLGlEQUFzQixJQUFJQSxzQ0FBY0E7QUFFekQsTUFBTUksVUFBVUYsU0FBU0QsMERBQVdBO0FBQ08iLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcRWxsZW5cXE9uZURyaXZlXFxEZXNrdG9wXFxEYXNoYm9hcmRcXFNjaHdhYkVsZWN0cm9uXFxTY2h3YWJFbGVjdHJvblxcYXBwXFxhcGlcXGF1dGhcXFsuLi5uZXh0YXV0aF1cXHJvdXRlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBkeW5hbWljID0gXCJmb3JjZS1keW5hbWljXCI7XHJcbmV4cG9ydCBjb25zdCByZXZhbGlkYXRlID0gMDtcclxuXHJcbmltcG9ydCBOZXh0QXV0aEltcG9ydCBmcm9tIFwibmV4dC1hdXRoXCI7XHJcbmltcG9ydCB7IGF1dGhPcHRpb25zIH0gZnJvbSBcIkAvbGliL2F1dGgtb3B0aW9uc1wiO1xyXG5cclxuY29uc3QgTmV4dEF1dGggPSBOZXh0QXV0aEltcG9ydC5kZWZhdWx0IHx8IE5leHRBdXRoSW1wb3J0O1xyXG5cclxuY29uc3QgaGFuZGxlciA9IE5leHRBdXRoKGF1dGhPcHRpb25zKTtcclxuZXhwb3J0IHsgaGFuZGxlciBhcyBHRVQsIGhhbmRsZXIgYXMgUE9TVCB9O1xyXG4iXSwibmFtZXMiOlsiZHluYW1pYyIsInJldmFsaWRhdGUiLCJOZXh0QXV0aEltcG9ydCIsImF1dGhPcHRpb25zIiwiTmV4dEF1dGgiLCJkZWZhdWx0IiwiaGFuZGxlciIsIkdFVCIsIlBPU1QiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/api/auth/[...nextauth]/route.js\n");

/***/ }),

/***/ "(rsc)/./lib/auth-options.js":
/*!*****************************!*\
  !*** ./lib/auth-options.js ***!
  \*****************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var _rateLimit_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./rateLimit.js */ \"(rsc)/./lib/rateLimit.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var _prisma_dal_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./prisma-dal.js */ \"(rsc)/./lib/prisma-dal.js\");\n\n\n\n\nconst CredentialsProvider = next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__[\"default\"] || next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__;\nconst authOptions = {\n    secret: process.env.NEXTAUTH_SECRET,\n    session: {\n        strategy: \"jwt\",\n        maxAge: 30 * 24 * 60 * 60\n    },\n    providers: [\n        CredentialsProvider({\n            name: \"Credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\",\n                    required: true\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\",\n                    required: true\n                }\n            },\n            async authorize (credentials, req) {\n                // Rate limit by IP address (or fallback to email if no IP)\n                let ip = null;\n                if (req && req.headers) {\n                    ip = req.headers[\"x-forwarded-for\"]?.split(\",\")[0]?.trim() || req.headers[\"x-real-ip\"] || req.socket?.remoteAddress;\n                }\n                // fallback to email if no IP (not ideal, but better than nothing)\n                const rateLimitKey = ip || (credentials?.email ? `email:${credentials.email}` : \"unknown\");\n                if ((0,_rateLimit_js__WEBPACK_IMPORTED_MODULE_0__.rateLimit)(rateLimitKey, 5, 60 * 1000)) {\n                    throw new Error(\"Too many login attempts. Please try again in a minute.\");\n                }\n                try {\n                    // Check if credentials are provided\n                    if (!credentials?.email || !credentials?.password) {\n                        throw new Error(\"Email and password are required\");\n                    }\n                    // Find the user using Prisma\n                    const user = await (0,_prisma_dal_js__WEBPACK_IMPORTED_MODULE_3__.verifyUserCredentials)(credentials.email);\n                    if (!user) {\n                        console.log(\"User not found\");\n                        throw new Error(\"Invalid email or password\");\n                    }\n                    console.log(\"User found:\", user.email);\n                    // Verify password\n                    const isValidPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_2__.compare(credentials.password, user.password);\n                    if (!isValidPassword) {\n                        console.log(\"Invalid password\");\n                        throw new Error(\"Invalid email or password\");\n                    }\n                    console.log(\"Password verified successfully\");\n                    // Return the user object\n                    return {\n                        id: user.id,\n                        email: user.email,\n                        name: user.name || user.email.split('@')[0],\n                        role: user.role || 'user'\n                    };\n                } catch (error) {\n                    console.error(\"Authentication error:\", error);\n                    throw error;\n                }\n            }\n        })\n    ],\n    pages: {\n        signIn: \"/generalLogin\",\n        signOut: \"/generalLogin\",\n        error: \"/generalLogin\"\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            // Add user data to the token when signing in\n            if (user) {\n                token.id = user.id;\n                // Explicitly check for null/undefined and set to 'user' if so\n                token.role = user.role === undefined || user.role === null ? 'user' : user.role;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            // Reject session if token is missing or expired\n            if (!token || !token.id || token.exp && Date.now() / 1000 > token.exp) {\n                return null;\n            }\n            if (session.user) {\n                session.user.id = token.id;\n                session.user.role = token.role === undefined || token.role === null ? 'user' : token.role;\n            }\n            return session;\n        }\n    },\n    debug: \"development\" === \"development\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth-options.js\n");

/***/ }),

/***/ "(rsc)/./lib/prisma-dal.js":
/*!***************************!*\
  !*** ./lib/prisma-dal.js ***!
  \***************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getExcelData: () => (/* binding */ getExcelData),\n/* harmony export */   getTradingPairs: () => (/* binding */ getTradingPairs),\n/* harmony export */   getUserByEmail: () => (/* binding */ getUserByEmail),\n/* harmony export */   saveExcelData: () => (/* binding */ saveExcelData),\n/* harmony export */   saveTradingPairs: () => (/* binding */ saveTradingPairs),\n/* harmony export */   verifyUserCredentials: () => (/* binding */ verifyUserCredentials)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/**\r\n * Prisma Data Access Layer (DAL)\r\n *\r\n * This file provides a layer of abstraction for database operations using Prisma.\r\n * It replaces the direct MongoDB operations in lib/mongodb.js.\r\n */ \n// Create a new PrismaClient instance\nconst prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\n/**\r\n * User-related operations\r\n */ // Get a user by email\nasync function getUserByEmail(email) {\n    try {\n        console.log('Getting user by email:', email);\n        // Log available models\n        const availableModels = Object.keys(prisma).filter((key)=>!key.startsWith('_') && !key.startsWith('$') && typeof prisma[key] === 'object');\n        console.log('Available models:', availableModels);\n        // Determine which model to use for users\n        let userModel;\n        if (availableModels.includes('users')) {\n            userModel = prisma.users;\n        } else {\n            console.error('No user model found in Prisma client');\n            return null;\n        }\n        // Use the determined model\n        const user = await userModel.findUnique({\n            where: {\n                email\n            }\n        });\n        console.log('User found:', user ? 'Yes' : 'No');\n        if (user) {\n            // With Prisma, the id is already a string\n            // Just make sure we have a consistent property name\n            user.id = user.id || user._id;\n            // Log the user object for debugging\n            console.log('User object:', {\n                id: user.id,\n                email: user.email,\n                name: user.name,\n                role: user.role\n            });\n        }\n        return user;\n    } catch (error) {\n        console.error('Error getting user by email:', error);\n        return null;\n    }\n}\n// Verify user credentials\nasync function verifyUserCredentials(email) {\n    try {\n        const user = await getUserByEmail(email);\n        return user;\n    } catch (error) {\n        console.error('Error verifying user credentials:', error);\n        return null;\n    }\n}\n/**\r\n * Trading Pair operations\r\n */ // Save trading pairs\nasync function saveTradingPairs(pairs, userId) {\n    try {\n        console.log('saveTradingPairs called with pairs:', pairs.length);\n        console.log('saveTradingPairs called with userId:', userId);\n        // Delete all existing pairs for this user\n        await prisma.tradingPair.deleteMany({\n            where: {\n                userId\n            }\n        });\n        // Prepare the current date\n        const now = new Date();\n        // Log a sample pair for debugging\n        if (pairs.length > 0) {\n            console.log('Sample pair to save:', {\n                pairKey: pairs[0].pairKey,\n                status: pairs[0].status,\n                combinedPNL: pairs[0].combinedPNL\n            });\n        }\n        // Create all new pairs - we need to handle each pair individually\n        // since createMany doesn't work well with JSON fields in MongoDB\n        let insertedCount = 0;\n        for (const pair of pairs){\n            try {\n                // For pairKey, ensure it's a string or null\n                const pairKeyValue = pair.pairKey ? String(pair.pairKey) : null;\n                // Transform the shortComponent to match the expected schema\n                const shortComponent = {\n                    dividendUserValue: parseInt(pair.shortComponent?.dividendUserValue || 0),\n                    dollarCost: parseInt(pair.shortComponent?.dollarCost || 0),\n                    expectedQuantity: pair.shortComponent?.expectedQuantity || \"0\",\n                    formattedAmt: pair.shortComponent?.formattedAmt || \"0\",\n                    formattedAsk: pair.shortComponent?.formattedAsk || \"0.00\",\n                    formattedBid: pair.shortComponent?.formattedBid || \"0.00\",\n                    formattedChange: pair.shortComponent?.formattedChange || \"0.0%\",\n                    formattedCost: pair.shortComponent?.formattedCost || \"0.00\",\n                    formattedDividend: pair.shortComponent?.formattedDividend || \"0.00\",\n                    formattedLast: pair.shortComponent?.formattedLast || \"0.00\",\n                    formattedLoadedVolume: pair.shortComponent?.formattedLoadedVolume || \"0\",\n                    formattedSpreadUser: pair.shortComponent?.formattedSpreadUser || \"0.00\",\n                    formattedUserDividend: pair.shortComponent?.formattedUserDividend || \"0.00\",\n                    formattedVolume: pair.shortComponent?.formattedVolume || \"0\",\n                    id: pair.shortComponent?.id || \"0\",\n                    pnl: parseInt(pair.shortComponent?.pnl || 0),\n                    sectorValue: pair.shortComponent?.sectorValue || \"\",\n                    spreadUserValue: pair.shortComponent?.spreadUserValue || \"0\",\n                    // spreadValue is a Json type in the schema\n                    spreadValue: pair.shortComponent?.spreadValue || 0,\n                    statusValue: pair.shortComponent?.statusValue || \"\",\n                    ticker: pair.shortComponent?.ticker || \"\"\n                };\n                // Transform the longComponent to match the expected schema\n                const longComponent = {\n                    dividendUserValue: parseInt(pair.longComponent?.dividendUserValue || 0),\n                    dollarCost: parseInt(pair.longComponent?.dollarCost || 0),\n                    // expectedQuantity is a Json type in the schema\n                    expectedQuantity: pair.longComponent?.expectedQuantity || \"0\",\n                    formattedAmt: pair.longComponent?.formattedAmt || \"0\",\n                    formattedAsk: pair.longComponent?.formattedAsk || \"0.00\",\n                    formattedBid: pair.longComponent?.formattedBid || \"0.00\",\n                    formattedChange: pair.longComponent?.formattedChange || \"0.0%\",\n                    formattedCost: pair.longComponent?.formattedCost || \"0.00\",\n                    formattedDividend: pair.longComponent?.formattedDividend || \"0.00\",\n                    formattedLast: pair.longComponent?.formattedLast || \"0.00\",\n                    formattedLoadedVolume: pair.longComponent?.formattedLoadedVolume || \"0\",\n                    formattedSpreadUser: pair.longComponent?.formattedSpreadUser || \"0.00\",\n                    formattedUserDividend: pair.longComponent?.formattedUserDividend || \"0.00\",\n                    formattedVolume: pair.longComponent?.formattedVolume || \"0\",\n                    id: pair.longComponent?.id || \"0\",\n                    pnl: parseInt(pair.longComponent?.pnl || 0),\n                    sectorValue: pair.longComponent?.sectorValue || \"\",\n                    // spreadUserValue is a Json type in the schema\n                    spreadUserValue: pair.longComponent?.spreadUserValue || \"0\",\n                    // spreadValue is a Json type in the schema\n                    spreadValue: pair.longComponent?.spreadValue || 0,\n                    statusValue: pair.longComponent?.statusValue || \"\",\n                    ticker: pair.longComponent?.ticker || \"\"\n                };\n                await prisma.tradingPair.create({\n                    data: {\n                        pairKey: pairKeyValue,\n                        status: pair.status || \"\",\n                        shortComponent: shortComponent,\n                        longComponent: longComponent,\n                        combinedPNL: pair.combinedPNL || \"0\",\n                        userId,\n                        createdAt: now,\n                        updatedAt: now\n                    }\n                });\n                insertedCount++;\n            } catch (error) {\n                console.error(`Error inserting pair:`, error);\n            // Continue with the next pair\n            }\n        }\n        const createdPairs = {\n            count: insertedCount\n        };\n        return {\n            success: true,\n            insertedCount: createdPairs.count,\n            overwritten: true\n        };\n    } catch (error) {\n        console.error('Error saving trading pairs:', error);\n        throw error;\n    }\n}\n// Get trading pairs\nasync function getTradingPairs(userId, status = null) {\n    try {\n        const whereClause = {\n            userId\n        };\n        if (status) {\n            whereClause.status = status;\n        }\n        const pairs = await prisma.tradingPair.findMany({\n            where: whereClause,\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n        return pairs;\n    } catch (error) {\n        console.error('Error getting trading pairs:', error);\n        return [];\n    }\n}\n/**\r\n * Excel Data operations\r\n */ // Save Excel data\nasync function saveExcelData(excelData, userId) {\n    try {\n        // Delete all existing Excel data for this user\n        await prisma.excelData.deleteMany({\n            where: {\n                userId\n            }\n        });\n        // Prepare the current date\n        const now = new Date();\n        // Create new Excel data\n        const createdExcelData = await prisma.excelData.create({\n            data: {\n                shortOpenTableData: excelData.shortOpenTableData || [],\n                shortLoadedTableData: excelData.shortLoadedTableData || [],\n                longOpenTableData: excelData.longOpenTableData || [],\n                longLoadedTableData: excelData.longLoadedTableData || [],\n                shortClosedTableData: excelData.shortClosedTableData || [],\n                longClosedTableData: excelData.longClosedTableData || [],\n                userId,\n                createdAt: now,\n                updatedAt: now\n            }\n        });\n        return {\n            success: true,\n            insertedId: createdExcelData.id,\n            overwritten: true\n        };\n    } catch (error) {\n        console.error('Error saving Excel data:', error);\n        throw error;\n    }\n}\n// Get Excel data\nasync function getExcelData(userId) {\n    try {\n        const excelData = await prisma.excelData.findFirst({\n            where: {\n                userId\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n        return excelData;\n    } catch (error) {\n        console.error('Error getting Excel data:', error);\n        return null;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma-dal.js\n");

/***/ }),

/***/ "(rsc)/./lib/rateLimit.js":
/*!**************************!*\
  !*** ./lib/rateLimit.js ***!
  \**************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   rateLimit: () => (/* binding */ rateLimit)\n/* harmony export */ });\n// lib/rateLimit.js\n// Simple in-memory rate limiter for development/testing (not for production scale)\nconst store = new Map();\nfunction rateLimit(key, limit = 5, windowMs = 60 * 1000) {\n    const now = Date.now();\n    let entry = store.get(key);\n    if (!entry || now - entry.last > windowMs) {\n        entry = {\n            count: 1,\n            last: now\n        };\n    } else {\n        entry.count += 1;\n    }\n    store.set(key, entry);\n    return entry.count > limit;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcmF0ZUxpbWl0LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxtQkFBbUI7QUFDbkIsbUZBQW1GO0FBQ25GLE1BQU1BLFFBQVEsSUFBSUM7QUFFWCxTQUFTQyxVQUFVQyxHQUFHLEVBQUVDLFFBQVEsQ0FBQyxFQUFFQyxXQUFXLEtBQUssSUFBSTtJQUM1RCxNQUFNQyxNQUFNQyxLQUFLRCxHQUFHO0lBQ3BCLElBQUlFLFFBQVFSLE1BQU1TLEdBQUcsQ0FBQ047SUFDdEIsSUFBSSxDQUFDSyxTQUFTRixNQUFNRSxNQUFNRSxJQUFJLEdBQUdMLFVBQVU7UUFDekNHLFFBQVE7WUFBRUcsT0FBTztZQUFHRCxNQUFNSjtRQUFJO0lBQ2hDLE9BQU87UUFDTEUsTUFBTUcsS0FBSyxJQUFJO0lBQ2pCO0lBQ0FYLE1BQU1ZLEdBQUcsQ0FBQ1QsS0FBS0s7SUFDZixPQUFPQSxNQUFNRyxLQUFLLEdBQUdQO0FBQ3ZCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEVsbGVuXFxPbmVEcml2ZVxcRGVza3RvcFxcRGFzaGJvYXJkXFxTY2h3YWJFbGVjdHJvblxcU2Nod2FiRWxlY3Ryb25cXGxpYlxccmF0ZUxpbWl0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIGxpYi9yYXRlTGltaXQuanNcclxuLy8gU2ltcGxlIGluLW1lbW9yeSByYXRlIGxpbWl0ZXIgZm9yIGRldmVsb3BtZW50L3Rlc3RpbmcgKG5vdCBmb3IgcHJvZHVjdGlvbiBzY2FsZSlcclxuY29uc3Qgc3RvcmUgPSBuZXcgTWFwKCk7XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gcmF0ZUxpbWl0KGtleSwgbGltaXQgPSA1LCB3aW5kb3dNcyA9IDYwICogMTAwMCkge1xyXG4gIGNvbnN0IG5vdyA9IERhdGUubm93KCk7XHJcbiAgbGV0IGVudHJ5ID0gc3RvcmUuZ2V0KGtleSk7XHJcbiAgaWYgKCFlbnRyeSB8fCBub3cgLSBlbnRyeS5sYXN0ID4gd2luZG93TXMpIHtcclxuICAgIGVudHJ5ID0geyBjb3VudDogMSwgbGFzdDogbm93IH07XHJcbiAgfSBlbHNlIHtcclxuICAgIGVudHJ5LmNvdW50ICs9IDE7XHJcbiAgfVxyXG4gIHN0b3JlLnNldChrZXksIGVudHJ5KTtcclxuICByZXR1cm4gZW50cnkuY291bnQgPiBsaW1pdDtcclxufVxyXG4iXSwibmFtZXMiOlsic3RvcmUiLCJNYXAiLCJyYXRlTGltaXQiLCJrZXkiLCJsaW1pdCIsIndpbmRvd01zIiwibm93IiwiRGF0ZSIsImVudHJ5IiwiZ2V0IiwibGFzdCIsImNvdW50Iiwic2V0Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/rateLimit.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.js&appDir=C%3A%5CUsers%5CEllen%5COneDrive%5CDesktop%5CDashboard%5CSchwabElectron%5CSchwabElectron%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CEllen%5COneDrive%5CDesktop%5CDashboard%5CSchwabElectron%5CSchwabElectron&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.js&appDir=C%3A%5CUsers%5CEllen%5COneDrive%5CDesktop%5CDashboard%5CSchwabElectron%5CSchwabElectron%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CEllen%5COneDrive%5CDesktop%5CDashboard%5CSchwabElectron%5CSchwabElectron&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Ellen_OneDrive_Desktop_Dashboard_SchwabElectron_SchwabElectron_app_api_auth_nextauth_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/auth/[...nextauth]/route.js */ \"(rsc)/./app/api/auth/[...nextauth]/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/[...nextauth]/route\",\n        pathname: \"/api/auth/[...nextauth]\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/[...nextauth]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabElectron\\\\SchwabElectron\\\\app\\\\api\\\\auth\\\\[...nextauth]\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Users_Ellen_OneDrive_Desktop_Dashboard_SchwabElectron_SchwabElectron_app_api_auth_nextauth_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.js&appDir=C%3A%5CUsers%5CEllen%5COneDrive%5CDesktop%5CDashboard%5CSchwabElectron%5CSchwabElectron%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CEllen%5COneDrive%5CDesktop%5CDashboard%5CSchwabElectron%5CSchwabElectron&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/uuid","vendor-chunks/oauth","vendor-chunks/@panva","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/oidc-token-hash","vendor-chunks/bcryptjs","vendor-chunks/preact","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.js&appDir=C%3A%5CUsers%5CEllen%5COneDrive%5CDesktop%5CDashboard%5CSchwabElectron%5CSchwabElectron%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CEllen%5COneDrive%5CDesktop%5CDashboard%5CSchwabElectron%5CSchwabElectron&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();