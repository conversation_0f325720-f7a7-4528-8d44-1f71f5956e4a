"use client";

import { useState, useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import { useSession, signOut } from "next-auth/react";
import { motion, AnimatePresence } from "framer-motion";
import Link from "next/link";
import {
  ChartBarIcon,
  ChartPieIcon,
  DocumentCheckIcon,
  Square2StackIcon,
  UserIcon,
  HomeIcon,
  ClipboardDocumentListIcon,
  BriefcaseIcon,
  ArrowTrendingUpIcon,
  AdjustmentsHorizontalIcon,
  TableCellsIcon,
  ShieldCheckIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  Bars3Icon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import ThemeToggle from "../ThemeToggle";
import styles from "./TopNavbar.module.css";

const TopNavbar = () => {
  const [isMinimized, setIsMinimized] = useState(false);
  const [showProfile, setShowProfile] = useState(false);
  const [showStrategies, setShowStrategies] = useState(false);
  const { data: session } = useSession();
  const pathname = usePathname();
  const router = useRouter();

  // Navigation items
  const mainNavItems = [
    { name: "Home", href: "/", icon: HomeIcon },
    { name: "Accounts Summary", href: "/accountsSummary", icon: ChartPieIcon },
    { name: "P & L Tracker", href: "/plTracker", icon: ChartBarIcon },
    { name: "Events Log", href: "/eventsLog", icon: ClipboardDocumentListIcon },
    { name: "File Handler", href: "/fileHandler", icon: BriefcaseIcon },
    { name: "Saved Pairs", href: "/savedPairs", icon: TableCellsIcon },
  ];

  const strategyItems = [
    { name: "WB Dashboard", href: "/Strategies/WB/dashboard", icon: ArrowTrendingUpIcon, category: "WB" },
    { name: "WB Configuration", href: "/Strategies/WB/configuration", icon: AdjustmentsHorizontalIcon, category: "WB" },
    { name: "SCALP Dashboard", href: "/Strategies/Scalp/dashboard", icon: ArrowTrendingUpIcon, category: "SCALP" },
    { name: "SCALP Configuration", href: "/Strategies/Scalp/configuration", icon: AdjustmentsHorizontalIcon, category: "SCALP" },
  ];

  const NavItem = ({ item, isActive }) => {
    const IconComponent = item.icon;
    
    return (
      <TooltipProvider delayDuration={100}>
        <Tooltip>
          <TooltipTrigger asChild>
            <Link
              href={item.href}
              className={`
                relative flex items-center gap-2 px-2 py-1.5 rounded-lg transition-all duration-200
                ${isActive 
                  ? 'bg-blue-600 text-white shadow-lg' 
                  : 'text-gray-300 hover:text-white hover:bg-gray-700/50'
                }
                ${isMinimized ? 'justify-center' : ''}
              `}
            >
              <IconComponent className="w-5 h-5 flex-shrink-0" />
              {!isMinimized && (
                <span className="text-sm font-medium whitespace-nowrap">{item.name}</span>
              )}
              {isActive && (
                <motion.div
                  layoutId="activeTab"
                  className="absolute inset-0 bg-blue-600 rounded-lg -z-10"
                  initial={false}
                  transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                />
              )}
            </Link>
          </TooltipTrigger>
          {isMinimized && (
            <TooltipContent side="bottom">
              <span>{item.name}</span>
            </TooltipContent>
          )}
        </Tooltip>
      </TooltipProvider>
    );
  };

  return (
    <>
      <motion.nav
        initial={false}
        animate={{ height: isMinimized ? "40px" : "60px" }}
        className={`fixed top-0 left-0 right-0 z-50 shadow-lg ${styles.navbar}`}
      >
        <div className="max-w-full mx-auto px-4 h-full">
          <div className="flex items-center justify-between h-full">
            {/* Left section - Logo and main nav */}
            <div className="flex items-center gap-6 h-full">
              {/* Logo/Brand */}
              <div className="flex items-center gap-3">
                <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${styles.logo}`}>
                  <span className="text-white font-bold text-sm">T</span>
                </div>
                {!isMinimized && (
                  <span className="text-white font-semibold text-lg">Trading Dashboard</span>
                )}
              </div>

              {/* Main Navigation */}
              {!isMinimized && (
                <div className="flex items-center gap-1">
                  {mainNavItems.map((item) => (
                    <NavItem
                      key={item.href}
                      item={item}
                      isActive={pathname === item.href}
                    />
                  ))}
                </div>
              )}
            </div>

            {/* Center section - Strategies dropdown (when not minimized) */}
            {!isMinimized && (
              <div className="relative">
                <TooltipProvider delayDuration={100}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        onClick={() => setShowStrategies(!showStrategies)}
                        className="flex items-center gap-2 px-3 py-1.5 rounded-lg text-gray-300 hover:text-white hover:bg-gray-700/50 transition-all duration-200"
                      >
                        <Square2StackIcon className="w-5 h-5" />
                        <span className="text-sm font-medium">Strategies</span>
                        {showStrategies ? (
                          <ChevronUpIcon className="w-4 h-4" />
                        ) : (
                          <ChevronDownIcon className="w-4 h-4" />
                        )}
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side="bottom">
                      <span>Trading Strategies</span>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                {/* Strategies Dropdown */}
                <AnimatePresence>
                  {showStrategies && (
                    <motion.div
                      initial={{ opacity: 0, y: -10, scale: 0.95 }}
                      animate={{ opacity: 1, y: 0, scale: 1 }}
                      exit={{ opacity: 0, y: -10, scale: 0.95 }}
                      transition={{ duration: 0.2 }}
                      className={`absolute top-full mt-2 left-0 rounded-xl min-w-[250px] overflow-hidden ${styles.dropdown}`}
                    >
                      <div className="p-2">
                        {/* WB Section */}
                        <div className="mb-2">
                          <div className="px-3 py-2 text-xs font-semibold text-gray-400 uppercase tracking-wider">
                            WB Strategy
                          </div>
                          {strategyItems.filter(item => item.category === "WB").map((item) => (
                            <NavItem
                              key={item.href}
                              item={item}
                              isActive={pathname === item.href}
                            />
                          ))}
                        </div>

                        {/* SCALP Section */}
                        <div>
                          <div className="px-3 py-2 text-xs font-semibold text-gray-400 uppercase tracking-wider">
                            SCALP Strategy
                          </div>
                          {strategyItems.filter(item => item.category === "SCALP").map((item) => (
                            <NavItem
                              key={item.href}
                              item={item}
                              isActive={pathname === item.href}
                            />
                          ))}
                        </div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            )}

            {/* Right section - Profile, Theme, Minimize */}
            <div className="flex items-center gap-3 h-full">
              {/* Admin link (if admin) */}
              {session?.user?.role?.toLowerCase() === "admin" && (
                <TooltipProvider delayDuration={100}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Link
                        href="/admin"
                        className={`
                          flex items-center gap-2 px-2 py-1.5 rounded-lg transition-all duration-200
                          ${pathname === "/admin" 
                            ? 'bg-red-600 text-white' 
                            : 'text-gray-300 hover:text-white hover:bg-gray-700/50'
                          }
                          ${isMinimized ? 'justify-center' : ''}
                        `}
                      >
                        <ShieldCheckIcon className="w-5 h-5" />
                        {!isMinimized && <span className="text-sm font-medium">Admin</span>}
                      </Link>
                    </TooltipTrigger>
                    {isMinimized && (
                      <TooltipContent side="bottom">
                        <span>Admin Panel</span>
                      </TooltipContent>
                    )}
                  </Tooltip>
                </TooltipProvider>
              )}

              {/* Theme Toggle */}
              <ThemeToggle iconOnly={true} />

              {/* Profile */}
              <TooltipProvider delayDuration={100}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button
                      onClick={() => setShowProfile(true)}
                      className="flex items-center gap-2 px-2 py-1.5 rounded-lg text-gray-300 hover:text-white hover:bg-gray-700/50 transition-all duration-200"
                    >
                      <UserIcon className="w-5 h-5" />
                      {!isMinimized && (
                        <span className="text-sm font-medium">
                          {session?.user?.name || session?.user?.email || 'Profile'}
                        </span>
                      )}
                    </button>
                  </TooltipTrigger>
                  <TooltipContent side="bottom">
                    <span>Profile & Settings</span>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              {/* Minimize/Expand Toggle */}
              <TooltipProvider delayDuration={100}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button
                      onClick={() => setIsMinimized(!isMinimized)}
                      className="flex items-center justify-center w-8 h-8 rounded-lg text-gray-300 hover:text-white hover:bg-gray-700/50 transition-all duration-200"
                    >
                      {isMinimized ? (
                        <Bars3Icon className="w-5 h-5" />
                      ) : (
                        <XMarkIcon className="w-5 h-5" />
                      )}
                    </button>
                  </TooltipTrigger>
                  <TooltipContent side="bottom">
                    <span>{isMinimized ? "Expand Navbar" : "Minimize Navbar"}</span>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
        </div>
      </motion.nav>

      {/* Profile Dialog */}
      <Dialog open={showProfile} onOpenChange={setShowProfile}>
        <DialogContent className="max-w-md mx-auto">
          <DialogHeader>
            <DialogTitle>Profile & Settings</DialogTitle>
          </DialogHeader>
          <div className="flex flex-col items-center gap-4 mb-4">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
              <UserIcon className="w-8 h-8 text-white" />
            </div>
            <div className="text-center">
              <div className="font-semibold text-lg text-gray-800 dark:text-gray-100">
                {session?.user?.name || session?.user?.email || 'Not signed in'}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                {session?.user?.email}
              </div>
              <div className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                Role: {session?.user?.role || 'unknown'}
              </div>
            </div>
          </div>
          <DialogFooter>
            <button
              className="w-full bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200"
              onClick={() => {
                setShowProfile(false);
                localStorage.clear();
                sessionStorage.clear();
                document.cookie = 'access_token=; Max-Age=0; path=/; domain=' + window.location.hostname;
                document.cookie = 'authorization_code=; Max-Age=0; path=/; domain=' + window.location.hostname;
                document.cookie = 'refresh_token=; Max-Age=0; path=/; domain=' + window.location.hostname;
                signOut({ callbackUrl: '/generalLogin' });
              }}
            >
              Sign Out
            </button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Click outside to close strategies dropdown */}
      {showStrategies && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowStrategies(false)}
        />
      )}
    </>
  );
};

export default TopNavbar;
