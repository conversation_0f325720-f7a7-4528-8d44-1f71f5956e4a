/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/oauth/schwab/page"],{

/***/ "(app-pages-browser)/./actions/schwabAccess.js":
/*!*********************************!*\
  !*** ./actions/schwabAccess.js ***!
  \*********************************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAccessToken: () => (/* binding */ getAccessToken),\n/* harmony export */   getAccounts: () => (/* binding */ getAccounts),\n/* harmony export */   getAuthorizationCodeCallbackHandler: () => (/* binding */ getAuthorizationCodeCallbackHandler),\n/* harmony export */   getAuthorizationCodeURL: () => (/* binding */ getAuthorizationCodeURL),\n/* harmony export */   getMarketData: () => (/* binding */ getMarketData),\n/* harmony export */   makeApiCall: () => (/* binding */ makeApiCall),\n/* harmony export */   refreshToken: () => (/* binding */ refreshToken),\n/* harmony export */   retrieveAccessToken: () => (/* binding */ retrieveAccessToken),\n/* harmony export */   retrieveAuthorizationCode: () => (/* binding */ retrieveAuthorizationCode),\n/* harmony export */   retrieveCorrelId: () => (/* binding */ retrieveCorrelId),\n/* harmony export */   retrieveCustomerId: () => (/* binding */ retrieveCustomerId),\n/* harmony export */   retrieveRefreshToken: () => (/* binding */ retrieveRefreshToken),\n/* harmony export */   storeAccessToken: () => (/* binding */ storeAccessToken),\n/* harmony export */   storeAccessTokenFirstLogin: () => (/* binding */ storeAccessTokenFirstLogin),\n/* harmony export */   storeAuthorizationCode: () => (/* binding */ storeAuthorizationCode),\n/* harmony export */   storeCorrelId: () => (/* binding */ storeCorrelId),\n/* harmony export */   storeCustomerId: () => (/* binding */ storeCustomerId),\n/* harmony export */   storeRefreshToken: () => (/* binding */ storeRefreshToken)\n/* harmony export */ });\n/* harmony import */ var private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! private-next-rsc-action-client-wrapper */ \"(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js\");\n/* __next_internal_action_entry_do_not_use__ {\"001f36d72a56e1b90e3b0cf3ac5b17ad2d052e90e3\":\"storeCustomerId\",\"007c5de391090de3c9df44e3f17cb14daddc363652\":\"storeCorrelId\",\"00869d9fb01177ec20ee695030af19e178e287c0c0\":\"getAuthorizationCodeURL\",\"008fa5309f6f5251d1b5768888c7e469e6554ee274\":\"retrieveAuthorizationCode\",\"00bd88f9a940ddade7a285542560af54563b908150\":\"getAccessToken\",\"00d576640475cc1dcc197a621db1ed396c89222ea3\":\"refreshToken\",\"401210bca393bb6dc70ea3d821d8e58ba0648b87ff\":\"retrieveAccessToken\",\"401e770d8047386dea81340af1ee77cb728049491b\":\"retrieveCustomerId\",\"4033437112d8841e6fb9a9329c3c38125980ba8833\":\"storeAccessTokenFirstLogin\",\"403db084fa4ebd72ecedbc7617491c1c5efe686638\":\"retrieveRefreshToken\",\"40b2a2f3571a50cc3f0ca96765eb8231c00c10f7f2\":\"storeAuthorizationCode\",\"40c06a7baae0dd738f4e48a678eb1b1fde7cc6a041\":\"retrieveCorrelId\",\"40cdd4a0fd146a33613647ed3512ef3b5141c8f97a\":\"storeAccessToken\",\"40e1053e53e59becc603e14253b89ffe213187e264\":\"storeRefreshToken\",\"609efbe637c6f5b642db5308468a6fd7f9dc4b48f4\":\"getAuthorizationCodeCallbackHandler\",\"78506afc4b55a567f35cfdad9ace9b8efe007a8013\":\"makeApiCall\",\"7f0aac4ac77cd7f7af69f789650dfee8d3448bd83c\":\"getAccounts\",\"7fe926c3ac96a7043781ab10a8c1e06873761a9d95\":\"getMarketData\"} */ \nvar getAuthorizationCodeURL = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"00869d9fb01177ec20ee695030af19e178e287c0c0\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"getAuthorizationCodeURL\");\nvar getAuthorizationCodeCallbackHandler = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"609efbe637c6f5b642db5308468a6fd7f9dc4b48f4\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"getAuthorizationCodeCallbackHandler\");\nvar getAccessToken = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"00bd88f9a940ddade7a285542560af54563b908150\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"getAccessToken\");\nvar refreshToken = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"00d576640475cc1dcc197a621db1ed396c89222ea3\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"refreshToken\");\nvar storeAuthorizationCode = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"40b2a2f3571a50cc3f0ca96765eb8231c00c10f7f2\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"storeAuthorizationCode\");\nvar retrieveAuthorizationCode = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"008fa5309f6f5251d1b5768888c7e469e6554ee274\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"retrieveAuthorizationCode\");\nvar storeAccessToken = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"40cdd4a0fd146a33613647ed3512ef3b5141c8f97a\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"storeAccessToken\");\nvar storeAccessTokenFirstLogin = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"4033437112d8841e6fb9a9329c3c38125980ba8833\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"storeAccessTokenFirstLogin\");\nvar retrieveAccessToken = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"401210bca393bb6dc70ea3d821d8e58ba0648b87ff\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"retrieveAccessToken\");\nvar storeRefreshToken = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"40e1053e53e59becc603e14253b89ffe213187e264\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"storeRefreshToken\");\nvar retrieveRefreshToken = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"403db084fa4ebd72ecedbc7617491c1c5efe686638\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"retrieveRefreshToken\");\nvar storeCorrelId = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"007c5de391090de3c9df44e3f17cb14daddc363652\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"storeCorrelId\");\nvar retrieveCorrelId = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"40c06a7baae0dd738f4e48a678eb1b1fde7cc6a041\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"retrieveCorrelId\");\nvar storeCustomerId = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"001f36d72a56e1b90e3b0cf3ac5b17ad2d052e90e3\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"storeCustomerId\");\nvar retrieveCustomerId = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"401e770d8047386dea81340af1ee77cb728049491b\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"retrieveCustomerId\");\nvar makeApiCall = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"78506afc4b55a567f35cfdad9ace9b8efe007a8013\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"makeApiCall\");\nvar getAccounts = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"7f0aac4ac77cd7f7af69f789650dfee8d3448bd83c\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"getAccounts\");\nvar getMarketData = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"7fe926c3ac96a7043781ab10a8c1e06873761a9d95\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"getMarketData\");\n // export {\n //   getAccounts,\n //   getMarketData,\n //   getAuthorizationCodeURL,\n //   getAuthorizationCodeCallbackHandler,\n //   getAccessToken,\n //   storeAuthorizationCode,\n //   retrieveAuthorizationCode,\n //   storeAccessToken,\n //   retrieveAccessToken,\n //   storeRefreshToken,\n //   retrieveRefreshToken,\n //   refreshToken,\n // };\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FjdGlvbnMvc2Nod2FiQWNjZXNzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBY3NCQSx3Q0FBQUEsNkZBQUFBLCtDQUFBQSw4RUFBQUEsVUFBQUEsb0ZBQUFBO0lBMkJBQyxvREFBQUEsNkZBQUFBLCtDQUFBQSw4RUFBQUEsVUFBQUEsb0ZBQUFBO0lBK0RBQywrQkFBQUEsNkZBQUFBLCtDQUFBQSw4RUFBQUEsVUFBQUEsb0ZBQUFBO0lBa0hBQyw2QkFBQUEsNkZBQUFBLCtDQUFBQSw4RUFBQUEsVUFBQUEsb0ZBQUFBO0lBK0VBQyx1Q0FBQUEsNkZBQUFBLCtDQUFBQSw4RUFBQUEsVUFBQUEsb0ZBQUFBO0lBNENBQywwQ0FBQUEsNkZBQUFBLCtDQUFBQSw4RUFBQUEsVUFBQUEsb0ZBQUFBO0lBdUJBQyxpQ0FBQUEsNkZBQUFBLCtDQUFBQSw4RUFBQUEsVUFBQUEsb0ZBQUFBO0lBMkJBQywyQ0FBQUEsNkZBQUFBLCtDQUFBQSw4RUFBQUEsVUFBQUEsb0ZBQUFBO0lBdUJBQyxvQ0FBQUEsNkZBQUFBLCtDQUFBQSw4RUFBQUEsVUFBQUEsb0ZBQUFBO0lBOENBQyxrQ0FBQUEsNkZBQUFBLCtDQUFBQSw4RUFBQUEsVUFBQUEsb0ZBQUFBO0lBc0JBQyxxQ0FBQUEsNkZBQUFBLCtDQUFBQSw4RUFBQUEsVUFBQUEsb0ZBQUFBO0lBY0FDLDhCQUFBQSw2RkFBQUEsK0NBQUFBLDhFQUFBQSxVQUFBQSxvRkFBQUE7SUEwQkFDLGlDQUFBQSw2RkFBQUEsK0NBQUFBLDhFQUFBQSxVQUFBQSxvRkFBQUE7SUFjQUMsZ0NBQUFBLDZGQUFBQSwrQ0FBQUEsOEVBQUFBLFVBQUFBLG9GQUFBQTtJQTBCQUMsbUNBQUFBLDZGQUFBQSwrQ0FBQUEsOEVBQUFBLFVBQUFBLG9GQUFBQTtJQWtCQUMsNEJBQUFBLDZGQUFBQSwrQ0FBQUEsOEVBQUFBLFVBQUFBLG9GQUFBQTtJQXFEVEMsNEJBQUFBLDZGQUFBQSwrQ0FBQUEsOEVBQUFBLFVBQUFBLG9GQUFBQTtJQWdEQUMsOEJBQUFBLDZGQUFBQSwrQ0FBQUEsOEVBQUFBLFVBQUFBLG9GQUFBQTtDQWdEYixXQUFXO0NBQ1gsaUJBQWlCO0NBQ2pCLG1CQUFtQjtDQUNuQiw2QkFBNkI7Q0FDN0IseUNBQXlDO0NBQ3pDLG9CQUFvQjtDQUNwQiw0QkFBNEI7Q0FDNUIsK0JBQStCO0NBQy9CLHNCQUFzQjtDQUN0Qix5QkFBeUI7Q0FDekIsdUJBQXVCO0NBQ3ZCLDBCQUEwQjtDQUMxQixrQkFBa0I7Q0FDbEIsS0FBSyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxFbGxlblxcT25lRHJpdmVcXERlc2t0b3BcXERhc2hib2FyZFxcU2Nod2FiRWxlY3Ryb25cXFNjaHdhYkVsZWN0cm9uXFxhY3Rpb25zXFxzY2h3YWJBY2Nlc3MuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc2VydmVyXCI7XHJcblxyXG5pbXBvcnQgYXhpb3MgZnJvbSBcImF4aW9zXCI7XHJcbmltcG9ydCB7IGNvb2tpZXMgfSBmcm9tIFwibmV4dC9oZWFkZXJzXCI7XHJcbmltcG9ydCB7IGdldFVzZXJQcmVmZXJlbmNlIH0gZnJvbSBcIi4vc2Nod2FiVHJhZGVyQVBJYWN0aW9uc1wiO1xyXG5cclxuY29uc3QgY2xpZW50SWQgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TQ0hXQUJfQVBQX0tFWTtcclxuY29uc3QgY2xpZW50U2VjcmV0ID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU0NIV0FCX1NFQ1JFVDtcclxuY29uc3QgY2FsbGJhY2tVcmwgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TQ0hXQUJfQ0FMTEJBQ0tVUkw7XHJcblxyXG4vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy9cclxuLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vXHJcbi8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vL1xyXG5cclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdldEF1dGhvcml6YXRpb25Db2RlVVJMKCkge1xyXG4gIHRyeSB7XHJcbiAgICAvLyBHZW5lcmF0ZSBhIHJhbmRvbSBzdGF0ZSBwYXJhbWV0ZXIgZm9yIENTUkYgcHJvdGVjdGlvblxyXG4gICAgLy8gV2UnbGwgc2tpcCB0aGlzIGZvciBub3cgc2luY2Ugd2UncmUgaGF2aW5nIGlzc3VlcyB3aXRoIGl0XHJcbiAgICAvLyBjb25zdCBzdGF0ZSA9IGNyeXB0by5yYW5kb21CeXRlcygzMikudG9TdHJpbmcoJ2hleCcpO1xyXG5cclxuICAgIC8vIC8vIFN0b3JlIHRoZSBzdGF0ZSBpbiBhIGNvb2tpZSBmb3IgdmVyaWZpY2F0aW9uIHdoZW4gdGhlIHVzZXIgcmV0dXJuc1xyXG4gICAgLy8gY29uc3QgY29va2llc09iaiA9IGF3YWl0IGNvb2tpZXMoKTtcclxuICAgIC8vIGNvb2tpZXNPYmouc2V0KFwic2Nod2FiX29hdXRoX3N0YXRlXCIsIHN0YXRlLCB7XHJcbiAgICAvLyAgIGh0dHBPbmx5OiB0cnVlLFxyXG4gICAgLy8gICBzZWN1cmU6IHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSBcInByb2R1Y3Rpb25cIixcclxuICAgIC8vICAgc2FtZVNpdGU6IFwibGF4XCIsXHJcbiAgICAvLyAgIG1heEFnZTogNjAgKiAxMCwgLy8gMTAgbWludXRlc1xyXG4gICAgLy8gICBwYXRoOiBcIi9cIixcclxuICAgIC8vIH0pO1xyXG5cclxuICAgIC8vIENvbnN0cnVjdCB0aGUgYXV0aG9yaXphdGlvbiBVUkwgd2l0aG91dCB0aGUgc3RhdGUgcGFyYW1ldGVyIGZvciBub3dcclxuICAgIGNvbnN0IGF1dGhvcml6YXRpb25Db2RlVXJsID0gYGh0dHBzOi8vYXBpLnNjaHdhYmFwaS5jb20vdjEvb2F1dGgvYXV0aG9yaXplP2NsaWVudF9pZD0ke2NsaWVudElkfSZyZWRpcmVjdF91cmk9JHtjYWxsYmFja1VybH1gO1xyXG5cclxuICAgIGNvbnNvbGUubG9nKFwiR2VuZXJhdGVkIGF1dGhvcml6YXRpb24gVVJMXCIpO1xyXG4gICAgcmV0dXJuIGF1dGhvcml6YXRpb25Db2RlVXJsO1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgZ2VuZXJhdGluZyBhdXRob3JpemF0aW9uIFVSTDpcIiwgZXJyb3IpO1xyXG4gICAgdGhyb3cgZXJyb3I7XHJcbiAgfVxyXG59XHJcblxyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2V0QXV0aG9yaXphdGlvbkNvZGVDYWxsYmFja0hhbmRsZXIoY29kZSwgc3RhdGUpIHtcclxuICB0cnkge1xyXG4gICAgY29uc29sZS5sb2coXCJQcm9jZXNzaW5nIE9BdXRoIGNhbGxiYWNrIHdpdGggY29kZVwiKTtcclxuXHJcbiAgICBpZiAoIWNvZGUpIHtcclxuICAgICAgY29uc29sZS5lcnJvcihcIk5vIGF1dGhvcml6YXRpb24gY29kZSBwcm92aWRlZFwiKTtcclxuICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiBcIk5vIGF1dGhvcml6YXRpb24gY29kZSBwcm92aWRlZFwiIH07XHJcbiAgICB9XHJcblxyXG4gICAgLy8gT25seSB2ZXJpZnkgc3RhdGUgaWYgaXQncyBub3QgdGhlIGJ5cGFzcyB2YWx1ZVxyXG4gICAgaWYgKHN0YXRlICE9PSBcImJ5cGFzc19zdGF0ZV9jaGVja1wiKSB7XHJcbiAgICAgIGlmICghc3RhdGUpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKFwiTm8gc3RhdGUgcGFyYW1ldGVyIHByb3ZpZGVkXCIpO1xyXG4gICAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogXCJObyBzdGF0ZSBwYXJhbWV0ZXIgcHJvdmlkZWRcIiB9O1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBWZXJpZnkgdGhlIHN0YXRlIHBhcmFtZXRlciB0byBwcmV2ZW50IENTUkYgYXR0YWNrc1xyXG4gICAgICBjb25zdCBjb29raWVzT2JqID0gYXdhaXQgY29va2llcygpO1xyXG4gICAgICBjb25zdCBzdG9yZWRTdGF0ZSA9IGNvb2tpZXNPYmouZ2V0KFwic2Nod2FiX29hdXRoX3N0YXRlXCIpPy52YWx1ZTtcclxuXHJcbiAgICAgIGlmICghc3RvcmVkU3RhdGUgfHwgc3RhdGUgIT09IHN0b3JlZFN0YXRlKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcihcIkludmFsaWQgc3RhdGUgcGFyYW1ldGVyXCIpO1xyXG4gICAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogXCJJbnZhbGlkIHN0YXRlIHBhcmFtZXRlclwiIH07XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIENsZWFyIHRoZSBzdGF0ZSBjb29raWVcclxuICAgICAgY29va2llc09iai5kZWxldGUoXCJzY2h3YWJfb2F1dGhfc3RhdGVcIik7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBjb25zb2xlLmxvZyhcIkJ5cGFzc2luZyBzdGF0ZSBwYXJhbWV0ZXIgY2hlY2tcIik7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gU3RvcmUgdGhlIGF1dGhvcml6YXRpb24gY29kZSBmb3IgbGF0ZXIgdXNlXHJcbiAgICBjb25zdCBzdG9yZVJlc3VsdCA9IGF3YWl0IHN0b3JlQXV0aG9yaXphdGlvbkNvZGUoY29kZSk7XHJcblxyXG4gICAgaWYgKCFzdG9yZVJlc3VsdCkge1xyXG4gICAgICBjb25zb2xlLmVycm9yKFwiRmFpbGVkIHRvIHN0b3JlIGF1dGhvcml6YXRpb24gY29kZVwiKTtcclxuICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiBcIkZhaWxlZCB0byBzdG9yZSBhdXRob3JpemF0aW9uIGNvZGVcIiB9O1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnNvbGUubG9nKFwiQXV0aG9yaXphdGlvbiBjb2RlIHN0b3JlZCBzdWNjZXNzZnVsbHlcIik7XHJcblxyXG4gICAgLy8gR2V0IHRoZSBhY2Nlc3MgdG9rZW5cclxuICAgIGNvbnN0IHRva2VuUmVzdWx0ID0gYXdhaXQgZ2V0QWNjZXNzVG9rZW4oKTtcclxuXHJcbiAgICBpZiAoIXRva2VuUmVzdWx0KSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJGYWlsZWQgdG8gZ2V0IGFjY2VzcyB0b2tlblwiKTtcclxuICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiBcIkZhaWxlZCB0byBnZXQgYWNjZXNzIHRva2VuXCIgfTtcclxuICAgIH1cclxuXHJcbiAgICBjb25zb2xlLmxvZyhcIkFjY2VzcyB0b2tlbiByZXRyaWV2ZWQgc3VjY2Vzc2Z1bGx5XCIpO1xyXG5cclxuICAgIHJldHVybiB7XHJcbiAgICAgIHN1Y2Nlc3M6IHRydWUsXHJcbiAgICAgIG1lc3NhZ2U6IFwiQXV0aG9yaXphdGlvbiBzdWNjZXNzZnVsXCIsXHJcbiAgICAgIGhhc0FjY2Vzc1Rva2VuOiAhIXRva2VuUmVzdWx0LmFjY2Vzc190b2tlbixcclxuICAgICAgaGFzUmVmcmVzaFRva2VuOiAhIXRva2VuUmVzdWx0LnJlZnJlc2hfdG9rZW4sXHJcbiAgICB9O1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgaW4gYXV0aG9yaXphdGlvbiBjb2RlIGNhbGxiYWNrOlwiLCBlcnJvcik7XHJcbiAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6IGVycm9yLm1lc3NhZ2UgfHwgXCJVbmtub3duIGVycm9yXCIgfTtcclxuICB9XHJcbn1cclxuXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZXRBY2Nlc3NUb2tlbigpIHtcclxuICB0cnkge1xyXG4gICAgY29uc29sZS5sb2coXCJHZXR0aW5nIGFjY2VzcyB0b2tlbi4uLlwiKTtcclxuICAgIGNvbnN0IGF1dGhvcml6YXRpb25Db2RlID0gYXdhaXQgcmV0cmlldmVBdXRob3JpemF0aW9uQ29kZSgpO1xyXG5cclxuICAgIGlmICghYXV0aG9yaXphdGlvbkNvZGUpIHtcclxuICAgICAgY29uc29sZS5lcnJvcihcIk5vIGF1dGhvcml6YXRpb24gY29kZSBmb3VuZCBpbiBjb29raWVzXCIpO1xyXG4gICAgICAvLyBUcnkgdG8gcmVmcmVzaCB0aGUgdG9rZW4gaW5zdGVhZCBvZiB0aHJvd2luZyBhbiBlcnJvclxyXG4gICAgICBjb25zdCByZWZyZXNoZWRUb2tlbiA9IGF3YWl0IHJlZnJlc2hUb2tlbigpO1xyXG4gICAgICBpZiAocmVmcmVzaGVkVG9rZW4pIHtcclxuICAgICAgICBjb25zb2xlLmxvZyhcclxuICAgICAgICAgIFwiU3VjY2Vzc2Z1bGx5IHJlZnJlc2hlZCB0b2tlbiBpbnN0ZWFkIG9mIGdldHRpbmcgYSBuZXcgb25lXCJcclxuICAgICAgICApO1xyXG4gICAgICAgIHJldHVybiByZWZyZXNoZWRUb2tlbjtcclxuICAgICAgfVxyXG4gICAgICAvLyBJZiB3ZSBjYW4ndCByZWZyZXNoLCByZXR1cm4gbnVsbCBpbnN0ZWFkIG9mIHRocm93aW5nXHJcbiAgICAgIGNvbnNvbGUubG9nKFwiQ291bGQgbm90IHJlZnJlc2ggdG9rZW4gZWl0aGVyLCByZXR1cm5pbmcgbnVsbFwiKTtcclxuICAgICAgcmV0dXJuIG51bGw7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gQ2hlY2sgZm9yIGNsaWVudCBjcmVkZW50aWFsc1xyXG4gICAgaWYgKCFjbGllbnRJZCB8fCAhY2xpZW50U2VjcmV0KSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJNaXNzaW5nIGNsaWVudCBjcmVkZW50aWFsc1wiKTtcclxuICAgICAgcmV0dXJuIG51bGw7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gQ3JlYXRlIHRoZSBhdXRob3JpemF0aW9uIGhlYWRlclxyXG4gICAgY29uc3QgYXV0aEhlYWRlciA9IEJ1ZmZlci5mcm9tKGAke2NsaWVudElkfToke2NsaWVudFNlY3JldH1gKS50b1N0cmluZyhcclxuICAgICAgXCJiYXNlNjRcIlxyXG4gICAgKTtcclxuXHJcbiAgICAvLyBFbnN1cmUgdGhlIGNvZGUgaXMgcHJvcGVybHkgZW5jb2RlZFxyXG4gICAgY29uc3QgZW5jb2RlZENvZGUgPSBlbmNvZGVVUklDb21wb25lbnQoYXV0aG9yaXphdGlvbkNvZGUpO1xyXG4gICAgY29uc3QgZW5jb2RlZENhbGxiYWNrVXJsID0gZW5jb2RlVVJJQ29tcG9uZW50KGNhbGxiYWNrVXJsKTtcclxuXHJcbiAgICAvLyBTZXQgdXAgdGhlIHJlcXVlc3RcclxuICAgIGNvbnN0IGFjY2Vzc1Rva2VuVXJsID0gXCJodHRwczovL2FwaS5zY2h3YWJhcGkuY29tL3YxL29hdXRoL3Rva2VuXCI7XHJcbiAgICBjb25zdCBoZWFkZXJzID0ge1xyXG4gICAgICBcIkNvbnRlbnQtVHlwZVwiOiBcImFwcGxpY2F0aW9uL3gtd3d3LWZvcm0tdXJsZW5jb2RlZFwiLFxyXG4gICAgICBBdXRob3JpemF0aW9uOiBgQmFzaWMgJHthdXRoSGVhZGVyfWAsXHJcbiAgICB9O1xyXG5cclxuICAgIC8vIENyZWF0ZSB0aGUgcmVxdWVzdCBib2R5XHJcbiAgICBjb25zdCBib2R5ID0gYGdyYW50X3R5cGU9YXV0aG9yaXphdGlvbl9jb2RlJmNvZGU9JHtlbmNvZGVkQ29kZX0mcmVkaXJlY3RfdXJpPSR7ZW5jb2RlZENhbGxiYWNrVXJsfWA7XHJcblxyXG4gICAgY29uc29sZS5sb2coXCJNYWtpbmcgdG9rZW4gcmVxdWVzdCB0bzpcIiwgYWNjZXNzVG9rZW5VcmwpO1xyXG4gICAgY29uc29sZS5sb2coXHJcbiAgICAgIFwiVXNpbmcgYXV0aG9yaXphdGlvbiBjb2RlOlwiLFxyXG4gICAgICBhdXRob3JpemF0aW9uQ29kZS5zdWJzdHJpbmcoMCwgMTApICsgXCIuLi5cIlxyXG4gICAgKTtcclxuXHJcbiAgICAvLyBNYWtlIHRoZSByZXF1ZXN0XHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGF4aW9zLnBvc3QoYWNjZXNzVG9rZW5VcmwsIGJvZHksIHsgaGVhZGVycyB9KTtcclxuXHJcbiAgICAvLyBQcm9jZXNzIHRoZSByZXNwb25zZVxyXG4gICAgaWYgKHJlc3BvbnNlLnN0YXR1cyA9PT0gMjAwKSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKFwiVG9rZW4gcmVzcG9uc2Ugc3RhdHVzOlwiLCByZXNwb25zZS5zdGF0dXMpO1xyXG5cclxuICAgICAgY29uc3QgYWNjZXNzVG9rZW5SZXNwb25zZSA9IHJlc3BvbnNlLmRhdGE7XHJcbiAgICAgIGNvbnNvbGUubG9nKFwiQWNjZXNzIHRva2VuIHJldHJpZXZlZCBzdWNjZXNzZnVsbHlcIik7XHJcblxyXG4gICAgICAvLyBTdG9yZSB0aGUgdG9rZW5zXHJcbiAgICAgIGlmIChhY2Nlc3NUb2tlblJlc3BvbnNlLmFjY2Vzc190b2tlbikge1xyXG4gICAgICAgIGF3YWl0IHN0b3JlQWNjZXNzVG9rZW4oYWNjZXNzVG9rZW5SZXNwb25zZSk7XHJcbiAgICAgICAgY29uc29sZS5sb2coXCJBY2Nlc3MgdG9rZW4gc3RvcmVkIHN1Y2Nlc3NmdWxseVwiKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgaWYgKGFjY2Vzc1Rva2VuUmVzcG9uc2UucmVmcmVzaF90b2tlbikge1xyXG4gICAgICAgIGF3YWl0IHN0b3JlUmVmcmVzaFRva2VuKGFjY2Vzc1Rva2VuUmVzcG9uc2UucmVmcmVzaF90b2tlbik7XHJcbiAgICAgICAgY29uc29sZS5sb2coXCJSZWZyZXNoIHRva2VuIHN0b3JlZCBzdWNjZXNzZnVsbHlcIik7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHJldHVybiBhY2Nlc3NUb2tlblJlc3BvbnNlO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgY29uc29sZS5lcnJvcihcIlVuZXhwZWN0ZWQgcmVzcG9uc2Ugc3RhdHVzOlwiLCByZXNwb25zZS5zdGF0dXMpO1xyXG4gICAgICByZXR1cm4gbnVsbDtcclxuICAgIH1cclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgY29uc29sZS5lcnJvcihcIkVycm9yIGluIGdldEFjY2Vzc1Rva2VuOlwiLCBlcnJvci5yZXNwb25zZT8uc3RhdHVzKTtcclxuICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBkZXRhaWxzOlwiLCBlcnJvci5yZXNwb25zZT8uZGF0YSk7XHJcbiAgICAvLyBSZXR1cm4gbnVsbCBpbnN0ZWFkIG9mIHRocm93aW5nXHJcbiAgICByZXR1cm4gbnVsbDtcclxuICB9XHJcbn1cclxuXHJcbi8vIGFzeW5jIGZ1bmN0aW9uIGdldEF1dGhvcml6YXRpb25Db2RlKCkge1xyXG4vLyBjb25zb2xlLmxvZyhcIlJlY2VpdmVkIHJlcXVlc3Qgd2l0aCBtZXRob2Q6XCIsIHJlcS5tZXRob2QpO1xyXG4vLyBpZiAocmVxLm1ldGhvZCA9PT0gXCJHRVRcIikge1xyXG4vLyAgIGNvbnN0IGF1dGhvcml6YXRpb25Db2RlID0gcmVxLnF1ZXJ5LmNvZGU7XHJcbi8vICAgY29uc29sZS5sb2coXCJSZWNlaXZlZCBhdXRob3JpemF0aW9uIGNvZGU6IFwiICsgYXV0aG9yaXphdGlvbkNvZGUpO1xyXG4vLyAgIGlmIChhdXRob3JpemF0aW9uQ29kZSkge1xyXG4vLyAgICAgYXdhaXQgc3RvcmVBdXRob3JpemF0aW9uQ29kZShhdXRob3JpemF0aW9uQ29kZSk7XHJcbi8vICAgICByZXMucmVkaXJlY3QoXCIvZGFzaGJvYXJkXCIpO1xyXG4vLyAgIH0gZWxzZSB7XHJcbi8vICAgICByZXMuc3RhdHVzKDQwMCkuc2VuZChcIk5vIGF1dGhvcml6YXRpb24gY29kZSBwcm92aWRlZFwiKTtcclxuLy8gICB9XHJcbi8vIH0gZWxzZSB7XHJcbi8vICAgcmVzLnN0YXR1cyg0MDUpLnNlbmQoXCJNZXRob2Qgbm90IGFsbG93ZWRcIik7XHJcbi8vIH1cclxuLy8gfVxyXG5cclxuLy8gYXN5bmMgZnVuY3Rpb24gbG9naW4oKSB7XHJcbi8vICAgdHJ5IHtcclxuLy8gICAgIGNvbnN0IGFjY2Vzc1Rva2VuID0gYXdhaXQgZ2V0QWNjZXNzVG9rZW4oKTtcclxuLy8gICAgIC8vIFN0b3JlIHRoZSBhY2Nlc3MgdG9rZW4gZm9yIGxhdGVyIHVzZVxyXG4vLyAgICAgYXdhaXQgc3RvcmVBY2Nlc3NUb2tlbihhY2Nlc3NUb2tlblJlc3BvbnNlLmFjY2Vzc190b2tlbik7IC8vdmFsaWQgZm9yIDMwIG1pbnV0ZXNcclxuLy8gICAgIGF3YWl0IHN0b3JlUmVmcmVzaFRva2VuKGFjY2Vzc1Rva2VuUmVzcG9uc2UucmVmcmVzaF90b2tlbik7IC8vdmFsaWQgZm9yIDcgZGF5c1xyXG4vLyAgICAgcmV0dXJuIHsgYWNjZXNzVG9rZW4gfTtcclxuLy8gICB9IGNhdGNoIChlcnJvcikge1xyXG4vLyAgICAgY29uc29sZS5lcnJvcihlcnJvcik7XHJcbi8vICAgICB0aHJvdyBlcnJvcjtcclxuLy8gICB9XHJcbi8vIH1cclxuXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiByZWZyZXNoVG9rZW4oKSB7XHJcbiAgdHJ5IHtcclxuICAgIGNvbnNvbGUubG9nKFwiUmVmcmVzaGluZyBhY2Nlc3MgdG9rZW4uLi5cIik7XHJcblxyXG4gICAgY29uc3QgcmVmcmVzaFRva2VuID0gYXdhaXQgcmV0cmlldmVSZWZyZXNoVG9rZW4odHJ1ZSk7XHJcbiAgICBpZiAoIXJlZnJlc2hUb2tlbikge1xyXG4gICAgICBjb25zb2xlLmxvZyhcIk5vIHJlZnJlc2ggdG9rZW4gZm91bmQuIFVzZXIgbWlnaHQgbmVlZCB0byBsb2cgaW4gYWdhaW4uXCIpO1xyXG4gICAgICByZXR1cm4gbnVsbDtcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBhY2Nlc3NUb2tlblVybCA9IFwiaHR0cHM6Ly9hcGkuc2Nod2FiYXBpLmNvbS92MS9vYXV0aC90b2tlblwiO1xyXG5cclxuICAgIC8vIENyZWF0ZSB0aGUgQXV0aG9yaXphdGlvbiBoZWFkZXIgd2l0aCBCYXNpYyBBdXRoXHJcbiAgICBjb25zdCBhdXRoSGVhZGVyID0gQnVmZmVyLmZyb20oYCR7Y2xpZW50SWR9OiR7Y2xpZW50U2VjcmV0fWApLnRvU3RyaW5nKFxyXG4gICAgICBcImJhc2U2NFwiXHJcbiAgICApO1xyXG4gICAgY29uc3QgaGVhZGVycyA9IHtcclxuICAgICAgXCJDb250ZW50LVR5cGVcIjogXCJhcHBsaWNhdGlvbi94LXd3dy1mb3JtLXVybGVuY29kZWRcIixcclxuICAgICAgQXV0aG9yaXphdGlvbjogYEJhc2ljICR7YXV0aEhlYWRlcn1gLCAvLyBCYXNpYyBBdXRoZW50aWNhdGlvblxyXG4gICAgfTtcclxuXHJcbiAgICBjb25zdCBib2R5ID0gYGdyYW50X3R5cGU9cmVmcmVzaF90b2tlbiZyZWZyZXNoX3Rva2VuPSR7cmVmcmVzaFRva2VufWA7XHJcblxyXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChhY2Nlc3NUb2tlblVybCwge1xyXG4gICAgICBtZXRob2Q6IFwiUE9TVFwiLFxyXG4gICAgICBoZWFkZXJzLFxyXG4gICAgICBib2R5LFxyXG4gICAgfSk7XHJcblxyXG4gICAgaWYgKCFyZXNwb25zZS5vaykge1xyXG4gICAgICBjb25zdCBlcnJvclJlc3BvbnNlID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gICAgICBjb25zb2xlLmVycm9yKFxyXG4gICAgICAgIGBFcnJvciByZWZyZXNoaW5nIHRva2VuOiAke3Jlc3BvbnNlLnN0YXR1c30gLSAke1xyXG4gICAgICAgICAgcmVzcG9uc2Uuc3RhdHVzVGV4dFxyXG4gICAgICAgIH06IFwiJHtKU09OLnN0cmluZ2lmeShlcnJvclJlc3BvbnNlKX1cImBcclxuICAgICAgKTtcclxuXHJcbiAgICAgIC8vIElmIGl0J3MgYSA0MDAgZXJyb3IgcmVsYXRlZCB0byByZWZyZXNoIHRva2VuIGF1dGhlbnRpY2F0aW9uLCBjbGVhciB0aGUgaW52YWxpZCByZWZyZXNoIHRva2VuXHJcbiAgICAgIGlmIChcclxuICAgICAgICByZXNwb25zZS5zdGF0dXMgPT09IDQwMCAmJlxyXG4gICAgICAgIGVycm9yUmVzcG9uc2UuZXJyb3IgPT09IFwicmVmcmVzaF90b2tlbl9hdXRoZW50aWNhdGlvbl9lcnJvclwiXHJcbiAgICAgICkge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKFwiSW52YWxpZCByZWZyZXNoIHRva2VuIGRldGVjdGVkLCBjbGVhcmluZyBpdFwiKTtcclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgLy8gQ2xlYXIgdGhlIGludmFsaWQgcmVmcmVzaCB0b2tlblxyXG4gICAgICAgICAgY29uc3QgY29va2llc09iaiA9IGF3YWl0IGNvb2tpZXMoKTtcclxuICAgICAgICAgIGNvb2tpZXNPYmouZGVsZXRlKFwicmVmcmVzaF90b2tlblwiKTtcclxuICAgICAgICB9IGNhdGNoIChjbGVhckVycm9yKSB7XHJcbiAgICAgICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgY2xlYXJpbmcgaW52YWxpZCByZWZyZXNoIHRva2VuOlwiLCBjbGVhckVycm9yKTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHJldHVybiBudWxsO1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IG5ld0FjY2Vzc1Rva2VuUmVzcG9uc2UgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcbiAgICBjb25zb2xlLmxvZyhcclxuICAgICAgXCJOZXcgYWNjZXNzIHRva2VuIHJldHJpZXZlZDpcIixcclxuICAgICAgbmV3QWNjZXNzVG9rZW5SZXNwb25zZS5hY2Nlc3NfdG9rZW5cclxuICAgICk7XHJcblxyXG4gICAgLy8gU3RvcmUgdGhlIG5ldyBhY2Nlc3MgdG9rZW5cclxuICAgIGF3YWl0IHN0b3JlQWNjZXNzVG9rZW4obmV3QWNjZXNzVG9rZW5SZXNwb25zZSk7XHJcblxyXG4gICAgLy8gU3RvcmUgdGhlIG5ldyByZWZyZXNoIHRva2VuIChvbmx5IGlmIHByZXNlbnQpXHJcbiAgICBpZiAobmV3QWNjZXNzVG9rZW5SZXNwb25zZS5yZWZyZXNoX3Rva2VuKSB7XHJcbiAgICAgIGF3YWl0IHN0b3JlUmVmcmVzaFRva2VuKG5ld0FjY2Vzc1Rva2VuUmVzcG9uc2UucmVmcmVzaF90b2tlbik7XHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIG5ld0FjY2Vzc1Rva2VuUmVzcG9uc2U7XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBpbiByZWZyZXNoVG9rZW46XCIsIGVycm9yLm1lc3NhZ2UpO1xyXG4gICAgcmV0dXJuIG51bGw7XHJcbiAgfVxyXG59XHJcblxyXG4vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy9cclxuXHJcbi8vIEhlbHBlciBmdW5jdGlvbnMgdG8gc3RvcmUgYW5kIHJldHJpZXZlIGF1dGhvcml6YXRpb24gY29kZSBhbmQgYWNjZXNzIHRva2VuXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBzdG9yZUF1dGhvcml6YXRpb25Db2RlKGNvZGUpIHtcclxuICB0cnkge1xyXG4gICAgaWYgKCFjb2RlKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJObyBhdXRob3JpemF0aW9uIGNvZGUgcHJvdmlkZWRcIik7XHJcbiAgICAgIHJldHVybiBmYWxzZTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBDbGVhbiB0aGUgY29kZSBpZiBpdCBjb250YWlucyBVUkwtZW5jb2RlZCBjaGFyYWN0ZXJzXHJcbiAgICBjb25zdCBjbGVhbkNvZGUgPSBjb2RlLnJlcGxhY2UoLyU0MC9nLCBcIkBcIik7XHJcblxyXG4gICAgY29uc29sZS5sb2coYFN0b3JpbmcgYXV0aG9yaXphdGlvbiBjb2RlOiAke2NsZWFuQ29kZS5zdWJzdHJpbmcoMCwgMTApfS4uLmApO1xyXG5cclxuICAgIGNvbnN0IGNvb2tpZXNPYmogPSBhd2FpdCBjb29raWVzKCk7XHJcblxyXG4gICAgLy8gU2V0IHRoZSBjb29raWUgd2l0aCBhIGxvbmdlciBleHBpcmF0aW9uICgyIGhvdXJzKSB0byBlbnN1cmUgd2UgaGF2ZSB0aW1lIHRvIHVzZSBpdFxyXG4gICAgY29va2llc09iai5zZXQoXCJhdXRob3JpemF0aW9uX2NvZGVcIiwgY2xlYW5Db2RlLCB7XHJcbiAgICAgIG1heEFnZTogNzIwMCwgLy8gMiBob3VycyBpbiBzZWNvbmRzXHJcbiAgICAgIHBhdGg6IFwiL1wiLFxyXG4gICAgICBodHRwT25seTogdHJ1ZSxcclxuICAgICAgc2VjdXJlOiBwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gXCJwcm9kdWN0aW9uXCIsXHJcbiAgICAgIHNhbWVTaXRlOiBcInN0cmljdFwiLFxyXG4gICAgfSk7XHJcblxyXG4gICAgLy8gVmVyaWZ5IHRoZSBjb2RlIHdhcyBzdG9yZWRcclxuICAgIGNvbnN0IHN0b3JlZENvZGUgPSBjb29raWVzT2JqLmdldChcImF1dGhvcml6YXRpb25fY29kZVwiKT8udmFsdWU7XHJcblxyXG4gICAgaWYgKHN0b3JlZENvZGUpIHtcclxuICAgICAgY29uc29sZS5sb2coXHJcbiAgICAgICAgYEF1dGhvcml6YXRpb24gY29kZSBzdG9yZWQgc3VjY2Vzc2Z1bGx5OiAke3N0b3JlZENvZGUuc3Vic3RyaW5nKFxyXG4gICAgICAgICAgMCxcclxuICAgICAgICAgIDEwXHJcbiAgICAgICAgKX0uLi5gXHJcbiAgICAgICk7XHJcbiAgICAgIHJldHVybiB0cnVlO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgY29uc29sZS5lcnJvcihcIkZhaWxlZCB0byBzdG9yZSBhdXRob3JpemF0aW9uIGNvZGUgaW4gY29va2llc1wiKTtcclxuICAgICAgcmV0dXJuIGZhbHNlO1xyXG4gICAgfVxyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKFwiRXJyb3Igc3RvcmluZyBhdXRob3JpemF0aW9uIGNvZGU6XCIsIGVycm9yKTtcclxuICAgIHJldHVybiBmYWxzZTtcclxuICB9XHJcbn1cclxuXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiByZXRyaWV2ZUF1dGhvcml6YXRpb25Db2RlKCkge1xyXG4gIHRyeSB7XHJcbiAgICBjb25zdCBjb29raWVzT2JqID0gYXdhaXQgY29va2llcygpO1xyXG4gICAgY29uc3QgYXV0aGNvZGUgPSBjb29raWVzT2JqLmdldChcImF1dGhvcml6YXRpb25fY29kZVwiKT8udmFsdWU7XHJcblxyXG4gICAgaWYgKCFhdXRoY29kZSkge1xyXG4gICAgICBjb25zb2xlLmxvZyhcIk5vIGF1dGhvcml6YXRpb24gY29kZSBmb3VuZCBpbiBjb29raWVzXCIpO1xyXG4gICAgICByZXR1cm4gbnVsbDtcclxuICAgIH1cclxuXHJcbiAgICBjb25zb2xlLmxvZyhcclxuICAgICAgYFJldHJpZXZlZCBhdXRob3JpemF0aW9uIGNvZGUgZnJvbSBjb29raWVzOiAke2F1dGhjb2RlLnN1YnN0cmluZyhcclxuICAgICAgICAwLFxyXG4gICAgICAgIDEwXHJcbiAgICAgICl9Li4uYFxyXG4gICAgKTtcclxuICAgIHJldHVybiBhdXRoY29kZTtcclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgY29uc29sZS5lcnJvcihcIkVycm9yIGluIHJldHJpZXZlQXV0aG9yaXphdGlvbkNvZGU6XCIsIGVycm9yKTtcclxuICAgIHJldHVybiBudWxsO1xyXG4gIH1cclxufVxyXG5cclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHN0b3JlQWNjZXNzVG9rZW4odG9rZW4pIHtcclxuICB0cnkge1xyXG4gICAgaWYgKCF0b2tlbiB8fCAhdG9rZW4uYWNjZXNzX3Rva2VuKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJObyBhY2Nlc3MgdG9rZW4gcHJvdmlkZWQgb3IgaW52YWxpZCB0b2tlbiBmb3JtYXRcIik7XHJcbiAgICAgIHJldHVybiBmYWxzZTtcclxuICAgIH1cclxuXHJcbiAgICBjb25zb2xlLmxvZyhcclxuICAgICAgXCJTdG9yaW5nIGFjY2VzcyB0b2tlbjpcIixcclxuICAgICAgdG9rZW4uYWNjZXNzX3Rva2VuLnN1YnN0cmluZygwLCAxMCkgKyBcIi4uLlwiXHJcbiAgICApO1xyXG4gICAgY29uc3QgY29va2llc09iaiA9IGF3YWl0IGNvb2tpZXMoKTtcclxuICAgIGNvb2tpZXNPYmouc2V0KFwiYWNjZXNzX3Rva2VuXCIsIHRva2VuLmFjY2Vzc190b2tlbiwge1xyXG4gICAgICBtYXhBZ2U6IDE4MDAsXHJcbiAgICAgIHBhdGg6IFwiL1wiLFxyXG4gICAgICBodHRwT25seTogdHJ1ZSxcclxuICAgICAgc2VjdXJlOiBwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gXCJwcm9kdWN0aW9uXCIsXHJcbiAgICAgIHNhbWVTaXRlOiBcInN0cmljdFwiLFxyXG4gICAgfSk7XHJcbiAgICByZXR1cm4gdHJ1ZTtcclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgY29uc29sZS5lcnJvcihcIkVycm9yIHN0b3JpbmcgYWNjZXNzIHRva2VuOlwiLCBlcnJvcik7XHJcbiAgICByZXR1cm4gZmFsc2U7XHJcbiAgfVxyXG59XHJcbi8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vL1xyXG4vL3RoaXMgaXMgYSBkdXBsaWNhdGUgY29kZSwgdW50aWwgd2UgZmluZCB0aGUgc29sdXRpb24gZm9yIHRoZSBhY2Nlc3NUb2tlbiBwcm9ibGVtIGluIHRoZSBBY2Nlc3NUb2tlbiBmdW5jdGlvblxyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gc3RvcmVBY2Nlc3NUb2tlbkZpcnN0TG9naW4odG9rZW4pIHtcclxuICB0cnkge1xyXG4gICAgaWYgKCF0b2tlbikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKFwiTm8gYWNjZXNzIHRva2VuIHByb3ZpZGVkXCIpO1xyXG4gICAgICByZXR1cm4gZmFsc2U7XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgY29va2llc09iaiA9IGF3YWl0IGNvb2tpZXMoKTtcclxuICAgIGNvb2tpZXNPYmouc2V0KFwiYWNjZXNzX3Rva2VuXCIsIHRva2VuLCB7XHJcbiAgICAgIG1heEFnZTogMTgwMCxcclxuICAgICAgcGF0aDogXCIvXCIsXHJcbiAgICAgIGh0dHBPbmx5OiB0cnVlLFxyXG4gICAgICBzZWN1cmU6IHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSBcInByb2R1Y3Rpb25cIixcclxuICAgICAgc2FtZVNpdGU6IFwic3RyaWN0XCIsXHJcbiAgICB9KTtcclxuICAgIHJldHVybiB0cnVlO1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKFwiRXJyb3Igc3RvcmluZyBmaXJzdCBsb2dpbiBhY2Nlc3MgdG9rZW46XCIsIGVycm9yKTtcclxuICAgIHJldHVybiBmYWxzZTtcclxuICB9XHJcbn1cclxuLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vXHJcblxyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gcmV0cmlldmVBY2Nlc3NUb2tlbihza2lwUmVmcmVzaCA9IGZhbHNlKSB7XHJcbiAgdHJ5IHtcclxuICAgIGNvbnN0IGNvb2tpZXNPYmogPSBhd2FpdCBjb29raWVzKCk7XHJcbiAgICBsZXQgYWNjZXNzVG9rZW4gPSBjb29raWVzT2JqLmdldChcImFjY2Vzc190b2tlblwiKT8udmFsdWU7XHJcblxyXG4gICAgLy8gSWYgd2UgaGF2ZSBhbiBhY2Nlc3MgdG9rZW4gb3Igd2UncmUgc2tpcHBpbmcgcmVmcmVzaCwgcmV0dXJuIGl0IChvciBudWxsKVxyXG4gICAgaWYgKGFjY2Vzc1Rva2VuIHx8IHNraXBSZWZyZXNoKSB7XHJcbiAgICAgIHJldHVybiBhY2Nlc3NUb2tlbiB8fCBudWxsO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIENoZWNrIGlmIHdlIGhhdmUgYSByZWZyZXNoIHRva2VuIGJlZm9yZSBhdHRlbXB0aW5nIHRvIHJlZnJlc2hcclxuICAgIGNvbnN0IHJlZnJlc2hUb2tlblZhbHVlID0gYXdhaXQgcmV0cmlldmVSZWZyZXNoVG9rZW4odHJ1ZSk7IC8vIHN1cHByZXNzTG9nID0gdHJ1ZVxyXG4gICAgaWYgKCFyZWZyZXNoVG9rZW5WYWx1ZSkge1xyXG4gICAgICBjb25zb2xlLmxvZyhcIk5vIHJlZnJlc2ggdG9rZW4gYXZhaWxhYmxlLCBza2lwcGluZyByZWZyZXNoIGF0dGVtcHRcIik7XHJcbiAgICAgIHJldHVybiBudWxsO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIE9ubHkgYXR0ZW1wdCB0byByZWZyZXNoIGlmIHdlIGhhdmUgYm90aCBubyBhY2Nlc3MgdG9rZW4gYW5kIGEgdmFsaWQgcmVmcmVzaCB0b2tlblxyXG4gICAgY29uc29sZS5sb2coXCJObyBhY2Nlc3MgdG9rZW4gZm91bmQsIGF0dGVtcHRpbmcgdG8gcmVmcmVzaFwiKTtcclxuICAgIHRyeSB7XHJcbiAgICAgIC8vIEFjY2VzcyB0b2tlbiBpcyBub3QgZm91bmQsIHRyeSB0byByZWZyZXNoXHJcbiAgICAgIGNvbnN0IHJlZnJlc2hSZXN1bHQgPSBhd2FpdCByZWZyZXNoVG9rZW4oKTtcclxuICAgICAgaWYgKHJlZnJlc2hSZXN1bHQpIHtcclxuICAgICAgICAvLyBUcnkgdG8gZ2V0IHRoZSBhY2Nlc3MgdG9rZW4gYWdhaW4gYWZ0ZXIgcmVmcmVzaFxyXG4gICAgICAgIGNvbnN0IG5ld0Nvb2tpZXNPYmogPSBhd2FpdCBjb29raWVzKCk7XHJcbiAgICAgICAgYWNjZXNzVG9rZW4gPSBuZXdDb29raWVzT2JqLmdldChcImFjY2Vzc190b2tlblwiKT8udmFsdWU7XHJcblxyXG4gICAgICAgIGlmIChhY2Nlc3NUb2tlbikge1xyXG4gICAgICAgICAgY29uc29sZS5sb2coXCJTdWNjZXNzZnVsbHkgcmVmcmVzaGVkIGFuZCByZXRyaWV2ZWQgYWNjZXNzIHRva2VuXCIpO1xyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICBjb25zb2xlLmxvZyhcIlJlZnJlc2ggc3VjY2VlZGVkIGJ1dCBubyBhY2Nlc3MgdG9rZW4gZm91bmRcIik7XHJcbiAgICAgICAgfVxyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKFwiVG9rZW4gcmVmcmVzaCBmYWlsZWRcIik7XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciByZWZyZXNoaW5nIHRva2VuOlwiLCBlcnJvci5tZXNzYWdlKTtcclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gYWNjZXNzVG9rZW4gfHwgbnVsbDtcclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgY29uc29sZS5lcnJvcihcIkVycm9yIGluIHJldHJpZXZlQWNjZXNzVG9rZW46XCIsIGVycm9yKTtcclxuICAgIHJldHVybiBudWxsO1xyXG4gIH1cclxufVxyXG5cclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHN0b3JlUmVmcmVzaFRva2VuKHRva2VuKSB7XHJcbiAgdHJ5IHtcclxuICAgIGlmICghdG9rZW4pIHtcclxuICAgICAgY29uc29sZS5lcnJvcihcIk5vIHJlZnJlc2ggdG9rZW4gcHJvdmlkZWRcIik7XHJcbiAgICAgIHJldHVybiBmYWxzZTtcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBjb29raWVzT2JqID0gYXdhaXQgY29va2llcygpO1xyXG4gICAgY29va2llc09iai5zZXQoXCJyZWZyZXNoX3Rva2VuXCIsIHRva2VuLCB7XHJcbiAgICAgIG1heEFnZTogNjA0ODAwLFxyXG4gICAgICBwYXRoOiBcIi9cIixcclxuICAgICAgaHR0cE9ubHk6IHRydWUsXHJcbiAgICAgIHNlY3VyZTogcHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09IFwicHJvZHVjdGlvblwiLFxyXG4gICAgICBzYW1lU2l0ZTogXCJzdHJpY3RcIixcclxuICAgIH0pO1xyXG4gICAgcmV0dXJuIHRydWU7XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBzdG9yaW5nIHJlZnJlc2ggdG9rZW46XCIsIGVycm9yKTtcclxuICAgIHJldHVybiBmYWxzZTtcclxuICB9XHJcbn1cclxuXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiByZXRyaWV2ZVJlZnJlc2hUb2tlbihzdXBwcmVzc0xvZyA9IGZhbHNlKSB7XHJcbiAgdHJ5IHtcclxuICAgIGNvbnN0IGNvb2tpZXNPYmogPSBhd2FpdCBjb29raWVzKCk7XHJcbiAgICBjb25zdCByZWZyZXNoVG9rZW5WYWx1ZSA9IGNvb2tpZXNPYmouZ2V0KFwicmVmcmVzaF90b2tlblwiKT8udmFsdWU7XHJcbiAgICBpZiAoIXJlZnJlc2hUb2tlblZhbHVlICYmICFzdXBwcmVzc0xvZykge1xyXG4gICAgICBjb25zb2xlLmxvZyhcIk5vIHJlZnJlc2ggdG9rZW4gZm91bmRcIik7XHJcbiAgICB9XHJcbiAgICByZXR1cm4gcmVmcmVzaFRva2VuVmFsdWUgfHwgbnVsbDtcclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgY29uc29sZS5lcnJvcihcIkVycm9yIGluIHJldHJpZXZlUmVmcmVzaFRva2VuOlwiLCBlcnJvcik7XHJcbiAgICByZXR1cm4gbnVsbDtcclxuICB9XHJcbn1cclxuXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBzdG9yZUNvcnJlbElkKCkge1xyXG4gIHRyeSB7XHJcbiAgICBjb25zdCB1c2VyUHJlZmVyZW5jZSA9IGF3YWl0IGdldFVzZXJQcmVmZXJlbmNlKCk7XHJcbiAgICBjb25zdCBzY2h3YWJDbGllbnRDb3JyZWxJZCA9XHJcbiAgICAgIHVzZXJQcmVmZXJlbmNlLnN0cmVhbWVySW5mbz8uWzBdPy5zY2h3YWJDbGllbnRDb3JyZWxJZDtcclxuXHJcbiAgICBpZiAoIXNjaHdhYkNsaWVudENvcnJlbElkKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJObyBjb3JyZWxhdGlvbiBJRCBmb3VuZCBpbiB1c2VyIHByZWZlcmVuY2VzXCIpO1xyXG4gICAgICByZXR1cm4gZmFsc2U7XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgY29va2llc09iaiA9IGF3YWl0IGNvb2tpZXMoKTtcclxuICAgIGNvb2tpZXNPYmouc2V0KFwiY2xpZW50X2NvcnJlbElkXCIsIHNjaHdhYkNsaWVudENvcnJlbElkLCB7XHJcbiAgICAgIG1heEFnZTogODY0MDAsXHJcbiAgICAgIHBhdGg6IFwiL1wiLFxyXG4gICAgICBodHRwT25seTogdHJ1ZSxcclxuICAgICAgc2VjdXJlOiBwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gXCJwcm9kdWN0aW9uXCIsXHJcbiAgICAgIHNhbWVTaXRlOiBcInN0cmljdFwiLFxyXG4gICAgfSk7XHJcbiAgICByZXR1cm4gdHJ1ZTtcclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgY29uc29sZS5lcnJvcihcIkVycm9yIHN0b3JpbmcgY29ycmVsYXRpb24gSUQ6XCIsIGVycm9yKTtcclxuICAgIHJldHVybiBmYWxzZTtcclxuICB9XHJcbn1cclxuXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiByZXRyaWV2ZUNvcnJlbElkKHN1cHByZXNzTG9nID0gZmFsc2UpIHtcclxuICB0cnkge1xyXG4gICAgY29uc3QgY29va2llc09iaiA9IGF3YWl0IGNvb2tpZXMoKTtcclxuICAgIGNvbnN0IGNvcnJlbElkID0gY29va2llc09iai5nZXQoXCJjbGllbnRfY29ycmVsSWRcIik/LnZhbHVlO1xyXG4gICAgaWYgKCFjb3JyZWxJZCAmJiAhc3VwcHJlc3NMb2cpIHtcclxuICAgICAgY29uc29sZS5sb2coXCJObyBjbGllbnQgY29ycmVsSWQgZm91bmRcIik7XHJcbiAgICB9XHJcbiAgICByZXR1cm4gY29ycmVsSWQgfHwgbnVsbDtcclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgY29uc29sZS5lcnJvcihcIkVycm9yIGluIHJldHJpZXZlQ29ycmVsSWQ6XCIsIGVycm9yKTtcclxuICAgIHJldHVybiBudWxsO1xyXG4gIH1cclxufVxyXG5cclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHN0b3JlQ3VzdG9tZXJJZCgpIHtcclxuICB0cnkge1xyXG4gICAgY29uc3QgdXNlclByZWZlcmVuY2UgPSBhd2FpdCBnZXRVc2VyUHJlZmVyZW5jZSgpO1xyXG4gICAgY29uc3Qgc2Nod2FiQ2xpZW50Q3VzdG9tZXJJZCA9XHJcbiAgICAgIHVzZXJQcmVmZXJlbmNlLnN0cmVhbWVySW5mbz8uWzBdPy5zY2h3YWJDbGllbnRDdXN0b21lcklkO1xyXG5cclxuICAgIGlmICghc2Nod2FiQ2xpZW50Q3VzdG9tZXJJZCkge1xyXG4gICAgICBjb25zb2xlLmVycm9yKFwiTm8gY3VzdG9tZXIgSUQgZm91bmQgaW4gdXNlciBwcmVmZXJlbmNlc1wiKTtcclxuICAgICAgcmV0dXJuIGZhbHNlO1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IGNvb2tpZXNPYmogPSBhd2FpdCBjb29raWVzKCk7XHJcbiAgICBjb29raWVzT2JqLnNldChcImNsaWVudF9jdXN0b21lcklkXCIsIHNjaHdhYkNsaWVudEN1c3RvbWVySWQsIHtcclxuICAgICAgbWF4QWdlOiA4NjQwMCxcclxuICAgICAgcGF0aDogXCIvXCIsXHJcbiAgICAgIGh0dHBPbmx5OiB0cnVlLFxyXG4gICAgICBzZWN1cmU6IHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSBcInByb2R1Y3Rpb25cIixcclxuICAgICAgc2FtZVNpdGU6IFwic3RyaWN0XCIsXHJcbiAgICB9KTtcclxuICAgIHJldHVybiB0cnVlO1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKFwiRXJyb3Igc3RvcmluZyBjdXN0b21lciBJRDpcIiwgZXJyb3IpO1xyXG4gICAgcmV0dXJuIGZhbHNlO1xyXG4gIH1cclxufVxyXG5cclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHJldHJpZXZlQ3VzdG9tZXJJZChzdXBwcmVzc0xvZyA9IGZhbHNlKSB7XHJcbiAgdHJ5IHtcclxuICAgIGNvbnN0IGNvb2tpZXNPYmogPSBhd2FpdCBjb29raWVzKCk7XHJcbiAgICBjb25zdCBjdXN0b21lcklkID0gY29va2llc09iai5nZXQoXCJjbGllbnRfY3VzdG9tZXJJZFwiKT8udmFsdWU7XHJcbiAgICBpZiAoIWN1c3RvbWVySWQgJiYgIXN1cHByZXNzTG9nKSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKFwiTm8gY2xpZW50IGN1c3RvbWVySWQgZm91bmRcIik7XHJcbiAgICB9XHJcbiAgICByZXR1cm4gY3VzdG9tZXJJZCB8fCBudWxsO1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgaW4gcmV0cmlldmVDdXN0b21lcklkOlwiLCBlcnJvcik7XHJcbiAgICByZXR1cm4gbnVsbDtcclxuICB9XHJcbn1cclxuXHJcbi8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vL1xyXG4vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy9cclxuXHJcbi8vRVhBTVBMRSBBUEkgQ0FMTCBXSVRIIFRPS0VOIC0gR2VuZXJpYyBoZWxwZXIgZnVuY3Rpb25cclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIG1ha2VBcGlDYWxsKFxyXG4gIGVuZHBvaW50LFxyXG4gIG1ldGhvZCA9IFwiR0VUXCIsXHJcbiAgYm9keSA9IG51bGwsXHJcbiAgc2tpcFJlZnJlc2ggPSBmYWxzZVxyXG4pIHtcclxuICB0cnkge1xyXG4gICAgY29uc3QgYWNjZXNzVG9rZW4gPSBhd2FpdCByZXRyaWV2ZUFjY2Vzc1Rva2VuKHNraXBSZWZyZXNoKTsgLy8gUmV0cmlldmUgdGhlIHN0b3JlZCBhY2Nlc3MgdG9rZW5cclxuXHJcbiAgICBpZiAoIWFjY2Vzc1Rva2VuKSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKFwiTm8gYWNjZXNzIHRva2VuIGF2YWlsYWJsZSBmb3IgQVBJIGNhbGxcIik7XHJcbiAgICAgIHJldHVybiB7IGVycm9yOiBcIkF1dGhlbnRpY2F0aW9uIHJlcXVpcmVkXCIgfTtcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBoZWFkZXJzID0ge1xyXG4gICAgICBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7YWNjZXNzVG9rZW59YCxcclxuICAgICAgXCJDb250ZW50LVR5cGVcIjogXCJhcHBsaWNhdGlvbi9qc29uXCIsXHJcbiAgICB9O1xyXG5cclxuICAgIGNvbnN0IG9wdGlvbnMgPSB7XHJcbiAgICAgIG1ldGhvZCxcclxuICAgICAgaGVhZGVycyxcclxuICAgIH07XHJcblxyXG4gICAgaWYgKGJvZHkgJiYgKG1ldGhvZCA9PT0gXCJQT1NUXCIgfHwgbWV0aG9kID09PSBcIlBVVFwiIHx8IG1ldGhvZCA9PT0gXCJQQVRDSFwiKSkge1xyXG4gICAgICBvcHRpb25zLmJvZHkgPSBKU09OLnN0cmluZ2lmeShib2R5KTtcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGVuZHBvaW50LCBvcHRpb25zKTtcclxuXHJcbiAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XHJcbiAgICAgIGNvbnN0IGVycm9yVGV4dCA9IGF3YWl0IHJlc3BvbnNlLnRleHQoKTtcclxuICAgICAgY29uc29sZS5lcnJvcihcclxuICAgICAgICBgQVBJIGNhbGwgZmFpbGVkOiAke3Jlc3BvbnNlLnN0YXR1c30gJHtyZXNwb25zZS5zdGF0dXNUZXh0fWAsXHJcbiAgICAgICAgZXJyb3JUZXh0XHJcbiAgICAgICk7XHJcbiAgICAgIHJldHVybiB7XHJcbiAgICAgICAgZXJyb3I6IFwiQVBJIGNhbGwgZmFpbGVkXCIsXHJcbiAgICAgICAgc3RhdHVzOiByZXNwb25zZS5zdGF0dXMsXHJcbiAgICAgICAgc3RhdHVzVGV4dDogcmVzcG9uc2Uuc3RhdHVzVGV4dCxcclxuICAgICAgICBkZXRhaWxzOiBlcnJvclRleHQsXHJcbiAgICAgIH07XHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgY29uc29sZS5lcnJvcihcIkVycm9yIGluIG1ha2VBcGlDYWxsOlwiLCBlcnJvcik7XHJcbiAgICByZXR1cm4geyBlcnJvcjogZXJyb3IubWVzc2FnZSB8fCBcIlVua25vd24gZXJyb3JcIiB9O1xyXG4gIH1cclxufVxyXG5cclxuLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vXHJcblxyXG5leHBvcnQgY29uc3QgZ2V0QWNjb3VudHMgPSBhc3luYyAoc2tpcFJlZnJlc2ggPSBmYWxzZSkgPT4ge1xyXG4gIHRyeSB7XHJcbiAgICBsZXQgYWNjZXNzVG9rZW4gPSBhd2FpdCByZXRyaWV2ZUFjY2Vzc1Rva2VuKHNraXBSZWZyZXNoKTtcclxuICAgIGNvbnNvbGUubG9nKFxyXG4gICAgICBcIlVzaW5nIGFjY2VzcyB0b2tlbiBmb3IgYWNjb3VudHMgQVBJIGNhbGw6XCIsXHJcbiAgICAgIGFjY2Vzc1Rva2VuID8gXCJZZXNcIiA6IFwiTm9cIlxyXG4gICAgKTtcclxuXHJcbiAgICBpZiAoIWFjY2Vzc1Rva2VuKSB7XHJcbiAgICAgIHJldHVybiB7XHJcbiAgICAgICAgZXJyb3I6IFwiTm90IGNvbm5lY3RlZCB0byBTY2h3YWJcIixcclxuICAgICAgICBtZXNzYWdlOiBcIlBsZWFzZSBjb25uZWN0IHlvdXIgU2Nod2FiIGFjY291bnQgZmlyc3RcIixcclxuICAgICAgICBuZWVkc0Nvbm5lY3Rpb246IHRydWUsXHJcbiAgICAgIH07XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBheGlvcy5nZXQoXHJcbiAgICAgIFwiaHR0cHM6Ly9hcGkuc2Nod2FiYXBpLmNvbS90cmFkZXIvdjEvYWNjb3VudHNcIixcclxuICAgICAge1xyXG4gICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgIEF1dGhvcml6YXRpb246IGBCZWFyZXIgJHthY2Nlc3NUb2tlbn1gLFxyXG4gICAgICAgIH0sXHJcbiAgICAgIH1cclxuICAgICk7XHJcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgY29uc29sZS5lcnJvcihcIkVycm9yIGZldGNoaW5nIGFjY291bnRzOlwiLCBlcnJvcik7XHJcblxyXG4gICAgLy8gQ2hlY2sgaWYgaXQncyBhbiBhdXRoZW50aWNhdGlvbiBlcnJvclxyXG4gICAgaWYgKFxyXG4gICAgICBlcnJvci5yZXNwb25zZSAmJlxyXG4gICAgICAoZXJyb3IucmVzcG9uc2Uuc3RhdHVzID09PSA0MDEgfHwgZXJyb3IucmVzcG9uc2Uuc3RhdHVzID09PSA0MDMpXHJcbiAgICApIHtcclxuICAgICAgcmV0dXJuIHtcclxuICAgICAgICBlcnJvcjogXCJBdXRoZW50aWNhdGlvbiBmYWlsZWRcIixcclxuICAgICAgICBtZXNzYWdlOiBcIllvdXIgU2Nod2FiIGNvbm5lY3Rpb24gaGFzIGV4cGlyZWQuIFBsZWFzZSByZWNvbm5lY3QuXCIsXHJcbiAgICAgICAgbmVlZHNDb25uZWN0aW9uOiB0cnVlLFxyXG4gICAgICB9O1xyXG4gICAgfVxyXG5cclxuICAgIHJldHVybiB7XHJcbiAgICAgIGVycm9yOiBcIkZhaWxlZCB0byBmZXRjaCBhY2NvdW50c1wiLFxyXG4gICAgICBtZXNzYWdlOiBlcnJvci5tZXNzYWdlIHx8IFwiQW4gdW5rbm93biBlcnJvciBvY2N1cnJlZFwiLFxyXG4gICAgICBuZWVkc0Nvbm5lY3Rpb246IGZhbHNlLFxyXG4gICAgfTtcclxuICB9XHJcbn07XHJcblxyXG5leHBvcnQgY29uc3QgZ2V0TWFya2V0RGF0YSA9IGFzeW5jIChzeW1ib2wsIHNraXBSZWZyZXNoID0gZmFsc2UpID0+IHtcclxuICB0cnkge1xyXG4gICAgbGV0IGFjY2Vzc1Rva2VuID0gYXdhaXQgcmV0cmlldmVBY2Nlc3NUb2tlbihza2lwUmVmcmVzaCk7XHJcbiAgICBjb25zb2xlLmxvZyhcclxuICAgICAgXCJVc2luZyBhY2Nlc3MgdG9rZW4gZm9yIG1hcmtldCBkYXRhIEFQSSBjYWxsOlwiLFxyXG4gICAgICBhY2Nlc3NUb2tlbiA/IFwiWWVzXCIgOiBcIk5vXCJcclxuICAgICk7XHJcblxyXG4gICAgaWYgKCFhY2Nlc3NUb2tlbikge1xyXG4gICAgICByZXR1cm4ge1xyXG4gICAgICAgIGVycm9yOiBcIk5vdCBjb25uZWN0ZWQgdG8gU2Nod2FiXCIsXHJcbiAgICAgICAgbWVzc2FnZTogXCJQbGVhc2UgY29ubmVjdCB5b3VyIFNjaHdhYiBhY2NvdW50IGZpcnN0XCIsXHJcbiAgICAgICAgbmVlZHNDb25uZWN0aW9uOiB0cnVlLFxyXG4gICAgICB9O1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXhpb3MuZ2V0KFxyXG4gICAgICBgaHR0cHM6Ly9hcGkuc2Nod2FiYXBpLmNvbS92MS9tYXJrZXRzL2RhdGEvJHtzeW1ib2x9YCxcclxuICAgICAge1xyXG4gICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgIEF1dGhvcml6YXRpb246IGBCZWFyZXIgJHthY2Nlc3NUb2tlbn1gLFxyXG4gICAgICAgIH0sXHJcbiAgICAgIH1cclxuICAgICk7XHJcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgY29uc29sZS5lcnJvcihcIkVycm9yIGZldGNoaW5nIG1hcmtldCBkYXRhOlwiLCBlcnJvcik7XHJcblxyXG4gICAgLy8gQ2hlY2sgaWYgaXQncyBhbiBhdXRoZW50aWNhdGlvbiBlcnJvclxyXG4gICAgaWYgKFxyXG4gICAgICBlcnJvci5yZXNwb25zZSAmJlxyXG4gICAgICAoZXJyb3IucmVzcG9uc2Uuc3RhdHVzID09PSA0MDEgfHwgZXJyb3IucmVzcG9uc2Uuc3RhdHVzID09PSA0MDMpXHJcbiAgICApIHtcclxuICAgICAgcmV0dXJuIHtcclxuICAgICAgICBlcnJvcjogXCJBdXRoZW50aWNhdGlvbiBmYWlsZWRcIixcclxuICAgICAgICBtZXNzYWdlOiBcIllvdXIgU2Nod2FiIGNvbm5lY3Rpb24gaGFzIGV4cGlyZWQuIFBsZWFzZSByZWNvbm5lY3QuXCIsXHJcbiAgICAgICAgbmVlZHNDb25uZWN0aW9uOiB0cnVlLFxyXG4gICAgICB9O1xyXG4gICAgfVxyXG5cclxuICAgIHJldHVybiB7XHJcbiAgICAgIGVycm9yOiBcIkZhaWxlZCB0byBmZXRjaCBtYXJrZXQgZGF0YVwiLFxyXG4gICAgICBtZXNzYWdlOiBlcnJvci5tZXNzYWdlIHx8IFwiQW4gdW5rbm93biBlcnJvciBvY2N1cnJlZFwiLFxyXG4gICAgICBuZWVkc0Nvbm5lY3Rpb246IGZhbHNlLFxyXG4gICAgfTtcclxuICB9XHJcbn07XHJcblxyXG4vLyBleHBvcnQge1xyXG4vLyAgIGdldEFjY291bnRzLFxyXG4vLyAgIGdldE1hcmtldERhdGEsXHJcbi8vICAgZ2V0QXV0aG9yaXphdGlvbkNvZGVVUkwsXHJcbi8vICAgZ2V0QXV0aG9yaXphdGlvbkNvZGVDYWxsYmFja0hhbmRsZXIsXHJcbi8vICAgZ2V0QWNjZXNzVG9rZW4sXHJcbi8vICAgc3RvcmVBdXRob3JpemF0aW9uQ29kZSxcclxuLy8gICByZXRyaWV2ZUF1dGhvcml6YXRpb25Db2RlLFxyXG4vLyAgIHN0b3JlQWNjZXNzVG9rZW4sXHJcbi8vICAgcmV0cmlldmVBY2Nlc3NUb2tlbixcclxuLy8gICBzdG9yZVJlZnJlc2hUb2tlbixcclxuLy8gICByZXRyaWV2ZVJlZnJlc2hUb2tlbixcclxuLy8gICByZWZyZXNoVG9rZW4sXHJcbi8vIH07XHJcbiJdLCJuYW1lcyI6WyJnZXRBdXRob3JpemF0aW9uQ29kZVVSTCIsImdldEF1dGhvcml6YXRpb25Db2RlQ2FsbGJhY2tIYW5kbGVyIiwiZ2V0QWNjZXNzVG9rZW4iLCJyZWZyZXNoVG9rZW4iLCJzdG9yZUF1dGhvcml6YXRpb25Db2RlIiwicmV0cmlldmVBdXRob3JpemF0aW9uQ29kZSIsInN0b3JlQWNjZXNzVG9rZW4iLCJzdG9yZUFjY2Vzc1Rva2VuRmlyc3RMb2dpbiIsInJldHJpZXZlQWNjZXNzVG9rZW4iLCJzdG9yZVJlZnJlc2hUb2tlbiIsInJldHJpZXZlUmVmcmVzaFRva2VuIiwic3RvcmVDb3JyZWxJZCIsInJldHJpZXZlQ29ycmVsSWQiLCJzdG9yZUN1c3RvbWVySWQiLCJyZXRyaWV2ZUN1c3RvbWVySWQiLCJtYWtlQXBpQ2FsbCIsImdldEFjY291bnRzIiwiZ2V0TWFya2V0RGF0YSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./actions/schwabAccess.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/oauth/schwab/page.js":
/*!**********************************!*\
  !*** ./app/oauth/schwab/page.js ***!
  \**********************************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SchwabCallback)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/actions/schwabAccess */ \"(app-pages-browser)/./actions/schwabAccess.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction SchwabCallback() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasProcessed, setHasProcessed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SchwabCallback.useEffect\": ()=>{\n            // Only run this effect once and prevent reprocessing\n            if (isProcessing || hasProcessed) return;\n            async function processCallback() {\n                setIsProcessing(true);\n                try {\n                    console.log(\"OAuth callback page loaded\");\n                    // Check if we have a code in the URL\n                    const url = new URL(window.location.href);\n                    const code = url.searchParams.get(\"code\");\n                    if (!code) {\n                        console.error(\"Missing code parameter\");\n                        setError(\"Missing authorization code\");\n                        setHasProcessed(true);\n                        return;\n                    }\n                    console.log(\"Processing OAuth callback with code\");\n                    // For now, let's bypass the state parameter check since we're having issues with it\n                    // We'll just use the code to get the access token\n                    const result = await (0,_actions_schwabAccess__WEBPACK_IMPORTED_MODULE_3__.getAuthorizationCodeCallbackHandler)(code, \"bypass_state_check\");\n                    if (result.success) {\n                        console.log(\"OAuth callback processed successfully\");\n                        setSuccess(true);\n                        // Redirect to home page after a short delay\n                        setTimeout({\n                            \"SchwabCallback.useEffect.processCallback\": ()=>{\n                                router.push(\"/\");\n                            }\n                        }[\"SchwabCallback.useEffect.processCallback\"], 2000);\n                    } else {\n                        console.error(\"OAuth callback processing failed:\", result.error);\n                        setError(result.error || \"Failed to connect to Schwab\");\n                    }\n                } catch (error) {\n                    console.error(\"Error in OAuth callback processing:\", error);\n                    setError(\"An error occurred while processing the callback\");\n                } finally{\n                    setIsProcessing(false);\n                    setHasProcessed(true);\n                }\n            }\n            // Execute the callback processing\n            processCallback();\n        }\n    }[\"SchwabCallback.useEffect\"], [\n        isProcessing,\n        router,\n        hasProcessed\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center justify-center min-h-screen p-4 bg-gray-50 dark:bg-gray-900\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full max-w-md p-6 bg-white dark:bg-gray-800 rounded-lg shadow-md dark:shadow-gray-900\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold text-center mb-6 text-gray-900 dark:text-gray-100\",\n                    children: success ? \"Connected to Schwab\" : \"Connecting to Schwab\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabElectron\\\\SchwabElectron\\\\app\\\\oauth\\\\schwab\\\\page.js\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this),\n                error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-300 px-4 py-3 rounded mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-medium\",\n                            children: \"Error\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabElectron\\\\SchwabElectron\\\\app\\\\oauth\\\\schwab\\\\page.js\",\n                            lineNumber: 75,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabElectron\\\\SchwabElectron\\\\app\\\\oauth\\\\schwab\\\\page.js\",\n                            lineNumber: 76,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push(\"/\"),\n                                className: \"bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800\",\n                                children: \"Return to Home\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabElectron\\\\SchwabElectron\\\\app\\\\oauth\\\\schwab\\\\page.js\",\n                                lineNumber: 78,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabElectron\\\\SchwabElectron\\\\app\\\\oauth\\\\schwab\\\\page.js\",\n                            lineNumber: 77,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabElectron\\\\SchwabElectron\\\\app\\\\oauth\\\\schwab\\\\page.js\",\n                    lineNumber: 74,\n                    columnNumber: 11\n                }, this) : success ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-800 text-green-700 dark:text-green-300 px-4 py-3 rounded mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-medium\",\n                            children: \"Success!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabElectron\\\\SchwabElectron\\\\app\\\\oauth\\\\schwab\\\\page.js\",\n                            lineNumber: 88,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Your Schwab account has been connected successfully.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabElectron\\\\SchwabElectron\\\\app\\\\oauth\\\\schwab\\\\page.js\",\n                            lineNumber: 89,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2\",\n                            children: \"Redirecting to Home page...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabElectron\\\\SchwabElectron\\\\app\\\\oauth\\\\schwab\\\\page.js\",\n                            lineNumber: 90,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabElectron\\\\SchwabElectron\\\\app\\\\oauth\\\\schwab\\\\page.js\",\n                    lineNumber: 87,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 dark:border-blue-400 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabElectron\\\\SchwabElectron\\\\app\\\\oauth\\\\schwab\\\\page.js\",\n                            lineNumber: 94,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 dark:text-gray-300 text-center\",\n                            children: \"Please wait while we connect your Schwab account...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabElectron\\\\SchwabElectron\\\\app\\\\oauth\\\\schwab\\\\page.js\",\n                            lineNumber: 95,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabElectron\\\\SchwabElectron\\\\app\\\\oauth\\\\schwab\\\\page.js\",\n                    lineNumber: 93,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabElectron\\\\SchwabElectron\\\\app\\\\oauth\\\\schwab\\\\page.js\",\n            lineNumber: 68,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabElectron\\\\SchwabElectron\\\\app\\\\oauth\\\\schwab\\\\page.js\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, this);\n}\n_s(SchwabCallback, \"m4zci8YO66dlkfz2tyujZMPPq3g=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = SchwabCallback;\nvar _c;\n$RefreshReg$(_c, \"SchwabCallback\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/oauth/schwab/page.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/api/navigation.js":
/*!**************************************************!*\
  !*** ./node_modules/next/dist/api/navigation.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/components/navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\");\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_components_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n//# sourceMappingURL=navigation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL25hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdEOztBQUVoRCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxFbGxlblxcT25lRHJpdmVcXERlc2t0b3BcXERhc2hib2FyZFxcU2Nod2FiRWxlY3Ryb25cXFNjaHdhYkVsZWN0cm9uXFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXGFwaVxcbmF2aWdhdGlvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tICcuLi9jbGllbnQvY29tcG9uZW50cy9uYXZpZ2F0aW9uJztcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bmF2aWdhdGlvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/navigation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CEllen%5C%5COneDrive%5C%5CDesktop%5C%5CDashboard%5C%5CSchwabElectron%5C%5CSchwabElectron%5C%5Capp%5C%5Coauth%5C%5Cschwab%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CEllen%5C%5COneDrive%5C%5CDesktop%5C%5CDashboard%5C%5CSchwabElectron%5C%5CSchwabElectron%5C%5Capp%5C%5Coauth%5C%5Cschwab%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/oauth/schwab/page.js */ \"(app-pages-browser)/./app/oauth/schwab/page.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDRWxsZW4lNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNEYXNoYm9hcmQlNUMlNUNTY2h3YWJFbGVjdHJvbiU1QyU1Q1NjaHdhYkVsZWN0cm9uJTVDJTVDYXBwJTVDJTVDb2F1dGglNUMlNUNzY2h3YWIlNUMlNUNwYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj1mYWxzZSEiLCJtYXBwaW5ncyI6IkFBQUEsOEtBQWdKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxFbGxlblxcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXERhc2hib2FyZFxcXFxTY2h3YWJFbGVjdHJvblxcXFxTY2h3YWJFbGVjdHJvblxcXFxhcHBcXFxcb2F1dGhcXFxcc2Nod2FiXFxcXHBhZ2UuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CEllen%5C%5COneDrive%5C%5CDesktop%5C%5CDashboard%5C%5CSchwabElectron%5C%5CSchwabElectron%5C%5Capp%5C%5Coauth%5C%5Cschwab%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("// This file must be bundled in the app's client layer, it shouldn't be directly\n// imported by the server.\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    callServer: function() {\n        return _appcallserver.callServer;\n    },\n    createServerReference: function() {\n        return createServerReference;\n    },\n    findSourceMapURL: function() {\n        return _appfindsourcemapurl.findSourceMapURL;\n    }\n});\nconst _appcallserver = __webpack_require__(/*! next/dist/client/app-call-server */ \"(app-pages-browser)/./node_modules/next/dist/client/app-call-server.js\");\nconst _appfindsourcemapurl = __webpack_require__(/*! next/dist/client/app-find-source-map-url */ \"(app-pages-browser)/./node_modules/next/dist/client/app-find-source-map-url.js\");\nconst createServerReference = ( false ? 0 : __webpack_require__(/*! react-server-dom-webpack/client */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/client.js\")).createServerReference;\n\n//# sourceMappingURL=action-client-wrapper.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcRWxsZW5cXE9uZURyaXZlXFxEZXNrdG9wXFxEYXNoYm9hcmRcXFNjaHdhYkVsZWN0cm9uXFxTY2h3YWJFbGVjdHJvblxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxjb21waWxlZFxccmVhY3RcXGpzeC1kZXYtcnVudGltZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24uanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLmRldmVsb3BtZW50LmpzJyk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CEllen%5C%5COneDrive%5C%5CDesktop%5C%5CDashboard%5C%5CSchwabElectron%5C%5CSchwabElectron%5C%5Capp%5C%5Coauth%5C%5Cschwab%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);