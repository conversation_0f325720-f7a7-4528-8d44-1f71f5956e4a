import React from 'react';
import { useEndpointAppContext } from '@/components/EndpointAppContext';

import { formatNumber } from '@/utils/formatNumber';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import DateRangeSelector from '@/components/DateRangeSelector';

const TransactionsCard = () => {
  const {
    transactionsData,
    transactionsDateRange,
    updateTransactionsDateRange
  } = useEndpointAppContext();



  return (
    <div className="overflow-x-auto">
      {/* Date Range Selector - Always visible */}
      <div className="mb-3 p-2 bg-purple-50 dark:bg-purple-900/20 rounded-lg flex items-center gap-2">
        <span className="text-xs font-medium text-purple-800 dark:text-purple-200">
          Date Range:
        </span>
        <DateRangeSelector
          selectedRange={transactionsDateRange}
          onRangeChange={updateTransactionsDateRange}
        />
      </div>

      {/* Content area */}
      {!transactionsData ? (
        <div className="flex items-center justify-center h-full w-full">
          <div className="text-center text-gray-500 dark:text-gray-400 p-4">No transactions data</div>
        </div>
      ) : transactionsData.length === 0 ? (
        <div className="flex items-center justify-center h-full w-full">
          <div className="text-center text-gray-500 dark:text-gray-400 p-4">No transactions data</div>
        </div>
      ) : (

      <TooltipProvider delayDuration={20}>
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-purple-50 dark:bg-purple-900">
              <th className="border-b-2 border-purple-200 dark:border-purple-700 py-2 px-2 text-left text-sm font-medium text-purple-800 dark:text-purple-100 w-24">Account #</th>
              <th className="border-b-2 border-purple-200 dark:border-purple-700 py-2 px-2 text-left text-sm font-medium text-purple-800 dark:text-purple-100 w-24">Type</th>
              <th className="border-b-2 border-purple-200 dark:border-purple-700 py-2 px-2 text-left text-sm font-medium text-purple-800 dark:text-purple-100 w-48">Description</th>
              <th className="border-b-2 border-purple-200 dark:border-purple-700 py-2 px-2 text-left text-sm font-medium text-purple-800 dark:text-purple-100 w-20">Symbol</th>
              <th className="border-b-2 border-purple-200 dark:border-purple-700 py-2 px-2 text-left text-sm font-medium text-purple-800 dark:text-purple-100 w-20">Quantity</th>
              <th className="border-b-2 border-purple-200 dark:border-purple-700 py-2 px-2 text-left text-sm font-medium text-purple-800 dark:text-purple-100 w-20">Price</th>
              <th className="border-b-2 border-purple-200 dark:border-purple-700 py-2 px-2 text-left text-sm font-medium text-purple-800 dark:text-purple-100 w-24">Amount</th>
              <th className="border-b-2 border-purple-200 dark:border-purple-700 py-2 px-2 text-left text-sm font-medium text-purple-800 dark:text-purple-100 w-28">Date</th>
            </tr>
          </thead>
          <tbody>
            {transactionsData.map((transaction, index) => {
              const accountNumber = transaction.accountNumber || transaction.account_id || transaction.account || '--';
              const fields = [
                accountNumber,
                transaction.type ?? '--',
                transaction.description ?? '--',
                transaction.transactionItem?.instrument?.symbol ?? '--',
                formatNumber(transaction.transactionItem?.amount, 0),
                formatNumber(transaction.transactionItem?.price, 2, true),
                formatNumber(transaction.netAmount, 2, true),
                new Date(transaction.transactionDate).toLocaleDateString() ?? '--',
              ];
              return (
                <tr key={index} className="hover:bg-purple-50 dark:hover:bg-purple-900/30 transition-colors">
                  {fields.map((val, i) => (
                    <td key={i} className="border-b border-purple-100 dark:border-purple-800 py-2 px-2 text-sm dark:text-gray-200 max-w-[10rem] truncate">
                      <TooltipProvider delayDuration={20}>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate cursor-help">{val ?? '--'}</span>
                          </TooltipTrigger>
                          <TooltipContent side="top">
                            <span className="break-all">{val ?? '--'}</span>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                  ))}
                </tr>
              );
            })}
          </tbody>
        </table>
      </TooltipProvider>
      )}
    </div>
  );
};



export default TransactionsCard;
