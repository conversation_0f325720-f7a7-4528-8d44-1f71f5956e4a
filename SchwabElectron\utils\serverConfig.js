/**
 * Get the server URL based on the current environment
 * @returns {string} The appropriate server URL
 */
export function getServerUrl() {
  const isDevelopment = process.env.NODE_ENV === "development";
  return isDevelopment ? "https://localhost:3001" : "https://zap1.dev";
}

/**
 * Get the WebSocket URL based on the current environment
 * @returns {string} The appropriate WebSocket URL
 */
export function getWebSocketUrl() {
  const isDevelopment = process.env.NODE_ENV === "development";
  return isDevelopment ? "wss://localhost:3001" : "wss://zap1.dev";
}
