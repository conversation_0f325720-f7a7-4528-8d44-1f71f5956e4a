import React, { useEffect, useState } from 'react';
import { useEndpointAppContext } from '@/components/EndpointAppContext';
import { accountObserver } from '@/utils/Observer';
import { formatNumber } from '@/utils/formatNumber';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

const AccountBalancesCard = () => {
  const { accountData: initialAccountData } = useEndpointAppContext();
  const [accountData, setAccountData] = useState(initialAccountData);

  useEffect(() => {
    // Funzione chiamata quando ci sono aggiornamenti
    const handleAccountUpdate = (newData) => {
      setAccountData(newData);
    };

    // Subscribing all'osservatore
    accountObserver.subscribe(handleAccountUpdate);

    // Cleanup della subscription
    return () => {
      accountObserver.unsubscribe(handleAccountUpdate);
    };
  }, []);

  // Check if we have valid account data
  if (!accountData || accountData.length === 0) {
    return (
         <div className="flex items-center justify-center h-full w-full">
        <div className="text-center text-gray-500 dark:text-gray-400 p-4">No balances data</div>
      </div>
    );
  }

  // Filter out accounts with zero available funds
  const validAccounts = accountData.filter(account =>
    account.securitiesAccount.currentBalances.availableFunds !== 0
  );

  // If no valid accounts after filtering
  if (validAccounts.length === 0) {
    return (

      <div className="flex items-center justify-center h-full w-full">
        <div className="text-center text-gray-500 dark:text-gray-400 p-4">No balances data</div>


      </div>
    );
  }

  return (
    <div className="overflow-x-auto">
      <TooltipProvider delayDuration={20}>
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-blue-50 dark:bg-blue-900">
              <th className="border-b-2 border-blue-200 dark:border-blue-700 py-2 px-1 text-left text-xs font-medium text-blue-800 dark:text-blue-100 w-28">Account #</th>
              <th className="border-b-2 border-blue-200 dark:border-blue-700 py-2 px-1 text-left text-xs font-medium text-blue-800 dark:text-blue-100 w-28">Available Funds</th>
              <th className="border-b-2 border-blue-200 dark:border-blue-700 py-2 px-1 text-left text-xs font-medium text-blue-800 dark:text-blue-100 w-28">Non-Margin Funds</th>
              <th className="border-b-2 border-blue-200 dark:border-blue-700 py-2 px-1 text-left text-xs font-medium text-blue-800 dark:text-blue-100 w-28">Buying Power</th>
              <th className="border-b-2 border-blue-200 dark:border-blue-700 py-2 px-1 text-left text-xs font-medium text-blue-800 dark:text-blue-100 w-28">Cash Balance</th>
              <th className="border-b-2 border-blue-200 dark:border-blue-700 py-2 px-1 text-left text-xs font-medium text-blue-800 dark:text-blue-100 w-28">Day Trading BP</th>
              <th className="border-b-2 border-blue-200 dark:border-blue-700 py-2 px-1 text-left text-xs font-medium text-blue-800 dark:text-blue-100 w-32">Liquidation Value</th>
              <th className="border-b-2 border-blue-200 dark:border-blue-700 py-2 px-1 text-left text-xs font-medium text-blue-800 dark:text-blue-100 w-32">Long Market Value</th>
              <th className="border-b-2 border-blue-200 dark:border-blue-700 py-2 px-1 text-left text-xs font-medium text-blue-800 dark:text-blue-100 w-28">Money Market</th>
              <th className="border-b-2 border-blue-200 dark:border-blue-700 py-2 px-1 text-left text-xs font-medium text-blue-800 dark:text-blue-100 w-28">Short Balance</th>
              <th className="border-b-2 border-blue-200 dark:border-blue-700 py-2 px-1 text-left text-xs font-medium text-blue-800 dark:text-blue-100 w-28">Short Margin</th>
            </tr>
          </thead>
          <tbody>
            {validAccounts.map((account, index) => {
              const balances = account.securitiesAccount.currentBalances;
              const accountNumber = account.securitiesAccount.accountNumber || '--';
              const fields = [
                accountNumber,
                formatNumber(balances.availableFunds),
                formatNumber(balances.availableFundsNonMarginableTrade),
                formatNumber(balances.buyingPower),
                formatNumber(balances.cashBalance),
                formatNumber(balances.dayTradingBuyingPower),
                formatNumber(balances.liquidationValue),
                formatNumber(balances.longMarketValue),
                formatNumber(balances.moneyMarketFund),
                formatNumber(balances.shortBalance),
                formatNumber(balances.shortMarginValue),
              ];
              return (
                <tr key={index} className="hover:bg-blue-50 dark:hover:bg-blue-900/30 transition-colors">
                  {fields.map((val, i) => (
                    <td key={i} className="border-b border-blue-100 dark:border-blue-800 py-2 px-1 text-xs dark:text-gray-200 w-28 max-w-[8rem] truncate">
                      <TooltipProvider delayDuration={20}>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate cursor-help">{val ?? '--'}</span>
                          </TooltipTrigger>
                          <TooltipContent side="top">
                            <span className="break-all">{val ?? '--'}</span>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                  ))}
                </tr>
              );
            })}
          </tbody>
        </table>
      </TooltipProvider>
    </div>
  );
};



export default AccountBalancesCard;
