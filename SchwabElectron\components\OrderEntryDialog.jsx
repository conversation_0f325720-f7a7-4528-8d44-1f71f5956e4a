"use client";
import React, { useState, useRef, useEffect } from "react";
import {
  Dialog,
  DialogContent,
  DialogTrigger,
  DialogClose,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import ProtectedRoute from "./ProtectedRoute";

import { useEndpointAppContext } from "@/components/EndpointAppContext";
import { useMarketData } from "@/app/testingWebsocket/MarketDataContext";
import { formatNumber } from "@/utils/formatNumber";

// Accepts optional open, setOpen, and prefill props for programmatic control
export default function OrderEntryDialog({
  children,
  open: controlledOpen,
  setOpen: setControlledOpen,
  prefill,
}) {
  const [internalOpen, setInternalOpen] = useState(false);
  const open = controlledOpen !== undefined ? controlledOpen : internalOpen;
  const setOpen =
    setControlledOpen !== undefined ? setControlledOpen : setInternalOpen;

  // If prefill changes and dialog is opening, update form
  const [form, setForm] = useState({
    symbol: "",
    side: "buy",
    quantity: "",
    orderType: "limit", // Default to limit
    price: "",
    tif: "DAY", // Time in Force
    account: "",
    stopPrice: "",
    trailingAmount: "",
    note: "",
  });
  const [submitting, setSubmitting] = useState(false);
  const [result, setResult] = useState(null);
  const [validationError, setValidationError] = useState("");
  const symbolInputRef = useRef(null);

  // Contexts
  const { accountData } = useEndpointAppContext();
  const { filteredData: marketData } = useMarketData();

  // Prefill effect
  React.useEffect(() => {
    if (open && prefill) {
      setForm((f) => ({
        ...f,
        symbol: prefill.symbol || "",
        side: prefill.side || "buy",
        quantity: prefill.quantity || "",
        orderType: prefill.orderType || "limit",
        price: prefill.price !== undefined ? prefill.price : "",
        // If prefill.account is set, use it, otherwise keep previous or blank
        account:
          prefill.account !== undefined ? prefill.account : f.account || "",
      }));
    } else if (open && !prefill) {
      setForm({
        symbol: "",
        side: "buy",
        quantity: "",
        orderType: "limit",
        price: "",
        tif: "DAY",
        account: "",
        stopPrice: "",
        trailingAmount: "",
        note: "",
      });
    }
    if (open && symbolInputRef.current) {
      setTimeout(() => symbolInputRef.current?.focus(), 100);
    }
    if (!open) {
      setResult(null);
      setValidationError("");
    }
  }, [open, prefill]);

  // Keyboard usability: Enter to submit, Esc to close
  useEffect(() => {
    if (!open) return;
    const handler = (e) => {
      if (e.key === "Enter" && !submitting) {
        e.preventDefault();
        document
          .getElementById("order-entry-form")
          ?.dispatchEvent(
            new Event("submit", { cancelable: true, bubbles: true })
          );
      } else if (e.key === "Escape") {
        setOpen(false);
      }
    };
    window.addEventListener("keydown", handler);
    return () => window.removeEventListener("keydown", handler);
  }, [open, submitting, setOpen]);

  function handleChange(e) {
    const { name, value } = e.target;
    setForm((prev) => ({ ...prev, [name]: value }));
    setValidationError("");
  }

  function validateForm() {
    if (!form.symbol.trim()) return "Symbol is required.";
    if (!/^[A-Z0-9.]+$/.test(form.symbol.trim().toUpperCase()))
      return "Invalid symbol format.";
    if (!form.quantity || isNaN(form.quantity) || Number(form.quantity) <= 0)
      return "Quantity must be a positive number.";
    if (
      ["limit", "stop-limit"].includes(form.orderType) &&
      (!form.price || isNaN(form.price) || Number(form.price) <= 0)
    )
      return "Price required for limit/stop-limit orders.";
    if (
      ["stop", "stop-limit"].includes(form.orderType) &&
      (!form.stopPrice || isNaN(form.stopPrice) || Number(form.stopPrice) <= 0)
    )
      return "Stop price required.";
    if (
      form.orderType === "trailing-stop" &&
      (!form.trailingAmount ||
        isNaN(form.trailingAmount) ||
        Number(form.trailingAmount) <= 0)
    )
      return "Trailing amount required.";
    if (accountOptions.length > 0 && !form.account) return "Select an account.";
    // TODO: Add more validation (funds, shares, etc.)
    return "";
  }

  async function handleSubmit(e) {
    e.preventDefault();
    setValidationError("");
    const err = validateForm();
    if (err) {
      setValidationError(err);
      return;
    }
    setSubmitting(true);
    setResult(null);
    // TODO: Replace with real order API call
    setTimeout(() => {
      setResult({ success: true, message: "Order submitted! (mock)" });
      setSubmitting(false);
    }, 1000);
  }

  // Account options
  const accountOptions = Array.isArray(accountData)
    ? accountData.map((acc) => ({
        value: acc.securitiesAccount.accountNumber,
        label: acc.securitiesAccount.accountNumber,
      }))
    : [];

  // Market data for symbol
  const symbol = form.symbol.trim().toUpperCase();
  const md =
    symbol && marketData && marketData[symbol] ? marketData[symbol] : null;
  const estimatedCost =
    form.quantity && form.price && !isNaN(form.quantity) && !isNaN(form.price)
      ? Number(form.quantity) * Number(form.price)
      : null;

  // Advanced order type fields
  const showStop = ["stop", "stop-limit"].includes(form.orderType);
  const showLimit = ["limit", "stop-limit"].includes(form.orderType);
  const showTrailing = form.orderType === "trailing-stop";

  return (
    <ProtectedRoute>
      <Dialog open={open} onOpenChange={setOpen}>
        {children && <DialogTrigger asChild>{children}</DialogTrigger>}
        <DialogContent className='max-w-2xl w-full p-8 rounded-lg'>
          <DialogHeader>
            <DialogTitle>Order Entry</DialogTitle>
          </DialogHeader>
          <form
            id='order-entry-form'
            onSubmit={handleSubmit}
            className='space-y-4'
          >
            <div className='flex gap-4'>
              <div className='flex-1'>
                <label className='block text-sm font-medium mb-1'>Symbol</label>
                <input
                  name='symbol'
                  value={form.symbol}
                  onChange={handleChange}
                  required
                  className='w-full border rounded px-3 py-2'
                  placeholder='Symbol'
                  ref={symbolInputRef}
                  autoFocus
                />
                {md && (
                  <div className='text-xs mt-1 text-gray-600 dark:text-gray-300'>
                    Bid:{" "}
                    <span className='font-mono'>
                      {formatNumber(md.bid_prc, 2, true)}
                    </span>{" "}
                    | Ask:{" "}
                    <span className='font-mono'>
                      {formatNumber(md.ask_prc, 2, true)}
                    </span>{" "}
                    | Last:{" "}
                    <span className='font-mono'>
                      {formatNumber(md.last_prc, 2, true)}
                    </span>
                  </div>
                )}
              </div>
              <div className='flex-1'>
                <label className='block text-sm font-medium mb-1'>Side</label>
                <select
                  name='side'
                  value={form.side}
                  onChange={handleChange}
                  className='w-full border rounded px-3 py-2'
                >
                  <option value='buy'>Buy</option>
                  <option value='sell'>Sell</option>
                </select>
              </div>
            </div>
            <div className='flex gap-4'>
              <div className='flex-1'>
                <label className='block text-sm font-medium mb-1'>
                  Quantity
                </label>
                <input
                  name='quantity'
                  value={form.quantity}
                  onChange={handleChange}
                  required
                  type='number'
                  min='1'
                  className='w-full border rounded px-3 py-2'
                  placeholder='100'
                />
              </div>
              <div className='flex-1'>
                <label className='block text-sm font-medium mb-1'>
                  Order Type
                </label>
                <select
                  name='orderType'
                  value={form.orderType}
                  onChange={handleChange}
                  className='w-full border rounded px-3 py-2'
                >
                  <option value='market'>Market</option>
                  <option value='limit'>Limit</option>
                  <option value='stop'>Stop</option>
                  <option value='stop-limit'>Stop-Limit</option>
                  <option value='trailing-stop'>Trailing Stop</option>
                </select>
              </div>
            </div>
            <div className='flex gap-4'>
              <div className='flex-1'>
                <label className='block text-sm font-medium mb-1'>
                  Time in Force
                </label>
                <select
                  name='tif'
                  value={form.tif}
                  onChange={handleChange}
                  className='w-full border rounded px-3 py-2'
                >
                  <option value='DAY'>Day</option>
                  <option value='GTC'>GTC</option>
                  <option value='IOC'>IOC</option>
                  <option value='FOK'>FOK</option>
                </select>
              </div>
              {accountOptions.length > 1 && (
                <div className='flex-1'>
                  <label className='block text-sm font-medium mb-1'>
                    Account
                  </label>
                  <select
                    name='account'
                    value={form.account}
                    onChange={handleChange}
                    className='w-full border rounded px-3 py-2'
                  >
                    <option value=''>Select Account</option>
                    {accountOptions.map((opt) => (
                      <option key={opt.value} value={opt.value}>
                        {opt.label}
                      </option>
                    ))}
                  </select>
                </div>
              )}
            </div>
            {showStop && (
              <div>
                <label className='block text-sm font-medium mb-1'>
                  Stop Price
                </label>
                <input
                  name='stopPrice'
                  value={form.stopPrice}
                  onChange={handleChange}
                  required={showStop}
                  type='number'
                  step='0.01'
                  className='w-full border rounded px-3 py-2'
                  placeholder='Stop Price'
                />
              </div>
            )}
            {showLimit && (
              <div>
                <label className='block text-sm font-medium mb-1'>
                  Limit Price
                </label>
                <input
                  name='price'
                  value={form.price}
                  onChange={handleChange}
                  required={showLimit}
                  type='number'
                  step='0.01'
                  className='w-full border rounded px-3 py-2'
                  placeholder='Limit Price'
                />
              </div>
            )}
            {showTrailing && (
              <div>
                <label className='block text-sm font-medium mb-1'>
                  Trailing Amount
                </label>
                <input
                  name='trailingAmount'
                  value={form.trailingAmount}
                  onChange={handleChange}
                  required={showTrailing}
                  type='number'
                  step='0.01'
                  className='w-full border rounded px-3 py-2'
                  placeholder='Trailing Amount'
                />
              </div>
            )}
            <div>
              <label className='block text-sm font-medium mb-1'>
                Order Note (optional)
              </label>
              <input
                name='note'
                value={form.note}
                onChange={handleChange}
                className='w-full border rounded px-3 py-2'
                placeholder='Add a note or memo...'
                maxLength={100}
              />
            </div>
            {/* Order Preview Summary */}
            <div className='bg-gray-50 dark:bg-gray-800 rounded p-4 mt-2 text-sm'>
              <div className='font-semibold mb-1'>Order Preview</div>
              <div>
                Symbol: <span className='font-mono'>{symbol || "--"}</span>
              </div>
              <div>
                Side: <span className='font-mono'>{form.side}</span>
              </div>
              <div>
                Quantity:{" "}
                <span className='font-mono'>{form.quantity || "--"}</span>
              </div>
              <div>
                Order Type:{" "}
                <span className='font-mono'>
                  {form.orderType.replace("-", " ")}
                </span>
              </div>
              <div>
                Time in Force: <span className='font-mono'>{form.tif}</span>
              </div>
              {accountOptions.length > 1 && (
                <div>
                  Account:{" "}
                  <span className='font-mono'>{form.account || "--"}</span>
                </div>
              )}
              {showStop && (
                <div>
                  Stop Price:{" "}
                  <span className='font-mono'>{form.stopPrice || "--"}</span>
                </div>
              )}
              {showLimit && (
                <div>
                  Limit Price:{" "}
                  <span className='font-mono'>{form.price || "--"}</span>
                </div>
              )}
              {showTrailing && (
                <div>
                  Trailing Amount:{" "}
                  <span className='font-mono'>
                    {form.trailingAmount || "--"}
                  </span>
                </div>
              )}
              {form.note && (
                <div>
                  Note: <span className='font-mono'>{form.note}</span>
                </div>
              )}
              <div>
                Estimated Cost/Proceeds:{" "}
                <span className='font-mono'>
                  {estimatedCost !== null
                    ? formatNumber(estimatedCost, 2, true)
                    : "--"}
                </span>
              </div>
            </div>
            {validationError && (
              <div className='mt-2 text-center text-red-600'>
                {validationError}
              </div>
            )}
            {result && (
              <div
                className={`mt-2 text-center ${
                  result.success ? "text-green-600" : "text-red-600"
                }`}
              >
                {result.message}
              </div>
            )}
            <div className='flex justify-end gap-2 mt-6'>
              <DialogClose asChild>
                <button
                  type='button'
                  className='px-4 py-2 rounded bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600'
                >
                  Cancel
                </button>
              </DialogClose>
              <button
                type='submit'
                disabled={submitting}
                className='px-4 py-2 rounded bg-blue-600 text-white font-semibold hover:bg-blue-700 disabled:opacity-60'
              >
                {submitting ? "Submitting..." : "Submit Order"}
              </button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </ProtectedRoute>
  );
}
