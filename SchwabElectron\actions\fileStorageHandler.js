const fs = require("fs");

const filePath = "./src/actions/symbolList.txt"; // Replace with the actual filePath

export async function readFile(filePath) {
  try {
    // const data = await fs.promises.readFile(filePath, "utf8");
    const data = await readFileAsync(filePath, "utf8");

    //if TXT split on new line
    let symbolList = [];
    symbolList = data.toString().split(/\r?\n/);

    console.log("Data read from file:", symbolList);

    return data;
  } catch (error) {
    console.error("Error reading file:", error);
    return null;
  }
}

export async function readFileJSON(filePath) {
  try {
    //const data = await fs.promises.readFile(filePath, "utf8");
    const data = await readFileAsync(filePath, "utf8");
    const jsonData = JSON.parse(data); // Parse JSON string to object
    console.log("JSON data read from file:", jsonData);
    return jsonData;
  } catch (error) {
    console.error("Error reading JSON file:", error);
    return null;
  }
}

export async function writeFile(filePath, data) {
  fs.writeFile(filePath, data, (err) => {
    if (err) {
      console.error("Error writing to file:", err);
      return;
    }
    console.log("Successfully wrote to file.");
  });
}

export async function writeFileJSON(filePath, data) {
  const jsonData = {
    name: "John Doe",
    age: 30,
    city: "New York",
  };

  const jsonString = JSON.stringify(jsonData, null, 2); // Convert object to JSON string with indentation

  fs.writeFile(filePath, jsonString, (err) => {
    if (err) {
      console.error("Error writing JSON to file:", err);
      return;
    }
    console.log("Successfully wrote JSON to file.");
  });
}
