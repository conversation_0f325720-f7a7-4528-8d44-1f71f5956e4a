import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * Validate customer ID format
 */
function validateCustomerId(customerId) {
  if (!customerId || typeof customerId !== 'string') {
    throw new Error('Invalid customer ID: must be a string');
  }
  if (customerId.length < 3 || customerId.length > 100) {
    throw new Error('Invalid customer ID: length must be between 3-100 characters');
  }
  if (!/^[a-zA-Z0-9_-]+$/.test(customerId)) {
    throw new Error('Invalid customer ID: only alphanumeric, underscore, and dash allowed');
  }
}

/**
 * Validate symbols format
 */
function validateSymbols(symbols) {
  if (!symbols || typeof symbols !== 'string') {
    throw new Error('Invalid symbols: must be a string');
  }
  if (symbols.length > 2000) {
    throw new Error('Invalid symbols: too long (max 2000 characters)');
  }
  // Basic symbol format validation (letters, numbers, commas, spaces)
  if (!/^[A-Z0-9,\s.-]+$/i.test(symbols)) {
    throw new Error('Invalid symbols: contains invalid characters');
  }
}

/**
 * Check if user exists by email (from NextAuth session)
 */
export async function verifyUserExists(email) {
  try {
    const user = await prisma.users.findUnique({
      where: { email },
      select: { id: true, email: true, name: true }
    });
    
    if (!user) {
      throw new Error('User not found');
    }
    
    return user;
  } catch (error) {
    console.error('Error verifying user:', error.message);
    throw error;
  }
}

/**
 * Get symbols for a user by customer ID
 */
export async function getUserSymbols(customerId) {
  try {
    validateCustomerId(customerId);
    
    const userSymbols = await prisma.userSymbols.findUnique({
      where: { customerId },
      include: {
        user: {
          select: { id: true, email: true, name: true }
        }
      }
    });
    
    if (!userSymbols) {
      console.log(`No symbols found for customer ${customerId}, using defaults`);
      return "AAPL,GOOG,NVDA,FVRR"; // Default symbols
    }
    
    validateSymbols(userSymbols.symbols);
    return userSymbols.symbols;
  } catch (error) {
    console.error(`Error getting symbols for customer: ${error.message}`);
    return "AAPL,GOOG,NVDA,FVRR"; // Safe fallback
  }
}

/**
 * Set symbols for a user by customer ID and user email
 */
export async function setUserSymbols(customerId, symbols, userEmail) {
  try {
    validateCustomerId(customerId);
    validateSymbols(symbols);
    
    // Verify user exists
    const user = await verifyUserExists(userEmail);
    
    // Upsert user symbols (create or update)
    const userSymbols = await prisma.userSymbols.upsert({
      where: { customerId },
      update: {
        symbols,
        updatedAt: new Date()
      },
      create: {
        userId: user.id,
        customerId,
        symbols
      },
      include: {
        user: {
          select: { id: true, email: true, name: true }
        }
      }
    });
    
    console.log(`Symbols updated in database for user ${user.email}`);
    return userSymbols;
  } catch (error) {
    console.error(`Error setting symbols: ${error.message}`);
    throw error;
  }
}

/**
 * Get symbols by user email (for API routes)
 */
export async function getUserSymbolsByEmail(userEmail) {
  try {
    const user = await verifyUserExists(userEmail);
    
    const userSymbols = await prisma.userSymbols.findFirst({
      where: { userId: user.id },
      include: {
        user: {
          select: { id: true, email: true, name: true }
        }
      }
    });
    
    if (!userSymbols) {
      return {
        symbols: "AAPL,GOOG,NVDA,FVRR",
        customerId: null,
        isDefault: true
      };
    }
    
    return {
      symbols: userSymbols.symbols,
      customerId: userSymbols.customerId,
      isDefault: false,
      updatedAt: userSymbols.updatedAt
    };
  } catch (error) {
    console.error(`Error getting symbols by email: ${error.message}`);
    return {
      symbols: "AAPL,GOOG,NVDA,FVRR",
      customerId: null,
      isDefault: true
    };
  }
}

/**
 * Delete symbols for a user
 */
export async function deleteUserSymbols(customerId) {
  try {
    validateCustomerId(customerId);
    
    await prisma.userSymbols.delete({
      where: { customerId }
    });
    
    console.log(`Symbols deleted for customer ${customerId}`);
    return true;
  } catch (error) {
    if (error.code === 'P2025') {
      // Record not found, already deleted
      return true;
    }
    console.error(`Error deleting symbols: ${error.message}`);
    throw error;
  }
}

// Cleanup function
export async function cleanup() {
  await prisma.$disconnect();
}
