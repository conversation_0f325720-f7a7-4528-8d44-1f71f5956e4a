"use client";

import { getSession, SessionProvider } from "next-auth/react";
import { ThemeProvider } from "next-themes";
import { AppProvider } from "./AppContext";
import { Toaster } from "@/components/toaster.jsx";
import { useRouter } from "next/router";

export function Providers({ children }) {
  // const session = await getSession();

  return (
    // <SessionProvider session={session}>
    <SessionProvider>
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
    >
      <AppProvider>{children}</AppProvider>
      <Toaster />
    </ThemeProvider>
    // </SessionProvider>
  );
}
