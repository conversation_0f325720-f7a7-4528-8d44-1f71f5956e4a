import { useEffect, useState } from "react";
import { io } from "socket.io-client";
import { getServerUrl } from "@/utils/serverConfig";

export default function useSocket() {
  const [socket, setSocket] = useState(null);

  useEffect(() => {
    const serverUrl = getServerUrl();
    const socketInstance = io(serverUrl); // <PERSON><PERSON><PERSON> al server WebSocket

    setSocket(socketInstance);

    return () => {
      socketInstance.disconnect();
    };
  }, []);

  return socket;
}
