"use client";
import { createContext, useContext, useState, useEffect } from "react";

const ExcelDataContext = createContext({
  shortOpenTableData: [],
  shortLoadedTableData: [],
  longOpenTableData: [],
  longLoadedTableData: [],
  shortClosedTableData: [],
  longClosedTableData: [],
  setShortOpenTableData: () => {},
  setShortLoadedTableData: () => {},
  setLongOpenTableData: () => {},
  setLongLoadedTableData: () => {},
  setShortClosedTableData: () => {},
  setLongClosedTableData: () => {},
  updateLongStatus: () => {},
  updateShortStatus: () => {},
});

export function ExcelDataProvider({ children }) {
  const [shortOpenTableData, setShortOpenTableData] = useState(() => {
    if (typeof window !== "undefined") {
      const data = localStorage.getItem("shortOpenTableData");
      return data ? JSON.parse(data) : [];
    }
    return [];
  });

  const [shortLoadedTableData, setShortLoadedTableData] = useState(() => {
    if (typeof window !== "undefined") {
      const data = localStorage.getItem("shortLoadedTableData");
      return data ? JSON.parse(data) : [];
    }
    return [];
  });

  const [longOpenTableData, setLongOpenTableData] = useState(() => {
    if (typeof window !== "undefined") {
      const data = localStorage.getItem("longOpenTableData");
      return data ? JSON.parse(data) : [];
    }
    return [];
  });

  const [longLoadedTableData, setLongLoadedTableData] = useState(() => {
    if (typeof window !== "undefined") {
      const data = localStorage.getItem("longLoadedTableData");
      return data ? JSON.parse(data) : [];
    }
    return [];
  });
  const [shortClosedTableData, setShortClosedTableData] = useState(() => {
    if (typeof window !== "undefined") {
      const data = localStorage.getItem("shortClosedTableData");
      return data ? JSON.parse(data) : [];
    }
    return [];
  });

  const [longClosedTableData, setLongClosedTableData] = useState(() => {
    if (typeof window !== "undefined") {
      const data = localStorage.getItem("longClosedTableData");
      return data ? JSON.parse(data) : [];
    }
    return [];
  });

  useEffect(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem(
        "shortOpenTableData",
        JSON.stringify(shortOpenTableData)
      );
      localStorage.setItem(
        "shortLoadedTableData",
        JSON.stringify(shortLoadedTableData)
      );
      localStorage.setItem(
        "longOpenTableData",
        JSON.stringify(longOpenTableData)
      );
      localStorage.setItem(
        "longLoadedTableData",
        JSON.stringify(longLoadedTableData)
      );
      localStorage.setItem(
        "shortClosedTableData",
        JSON.stringify(shortClosedTableData)
      );
      localStorage.setItem(
        "longClosedTableData",
        JSON.stringify(longClosedTableData)
      );
    }
  }, [
    shortOpenTableData,
    shortLoadedTableData,
    shortClosedTableData,
    longOpenTableData,
    longLoadedTableData,
    longClosedTableData,
  ]);

  // Move a long row from any table to any other table, like the old system but for all tables
  const updateLongStatus = (rowId, newStatus) => {
    // Try to find the row in all tables
    let row =
      longLoadedTableData.find((r) => r.id === rowId) ||
      longOpenTableData.find((r) => r.id === rowId) ||
      longClosedTableData.find((r) => r.id === rowId);
    if (!row) return;
    const updated = { ...row, status: newStatus };
    // Remove from all tables
    setLongLoadedTableData((prev) => prev.filter((r) => r.id !== rowId));
    setLongOpenTableData((prev) => prev.filter((r) => r.id !== rowId));
    setLongClosedTableData((prev) => prev.filter((r) => r.id !== rowId));
    // Add to the right table
    if (newStatus === "WB_LoadedPairs") {
      setLongLoadedTableData((prev) => [...prev, updated]);
    } else if (newStatus === "WB_OpenPositions") {
      setLongOpenTableData((prev) => [...prev, updated]);
    } else if (newStatus === "WB_ClosedPositions") {
      setLongClosedTableData((prev) => [...prev, updated]);
    }
  };

  // Move a short row from any table to any other table, like the old system but for all tables
  const updateShortStatus = (rowId, newStatus) => {
    let row =
      shortLoadedTableData.find((r) => r.id === rowId) ||
      shortOpenTableData.find((r) => r.id === rowId) ||
      shortClosedTableData.find((r) => r.id === rowId);
    if (!row) return;
    const updated = { ...row, status: newStatus };
    setShortLoadedTableData((prev) => prev.filter((r) => r.id !== rowId));
    setShortOpenTableData((prev) => prev.filter((r) => r.id !== rowId));
    setShortClosedTableData((prev) => prev.filter((r) => r.id !== rowId));
    if (newStatus === "WB_LoadedPairs") {
      setShortLoadedTableData((prev) => [...prev, updated]);
    } else if (newStatus === "WB_OpenPositions") {
      setShortOpenTableData((prev) => [...prev, updated]);
    } else if (newStatus === "WB_ClosedPositions") {
      setShortClosedTableData((prev) => [...prev, updated]);
    }
  };

  // Function to load Excel data from database
  const loadExcelDataFromDatabase = async () => {
    try {
      console.log("Loading Excel data from database...");

      const response = await fetch("/api/mongodb/excel", {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (response.ok) {
        const data = await response.json();
        console.log("Excel data loaded from database:", data);

        if (data.success && data.excelData) {
          // Update all Excel data states
          setShortOpenTableData(data.excelData.shortOpenTableData || []);
          setShortLoadedTableData(data.excelData.shortLoadedTableData || []);
          setLongOpenTableData(data.excelData.longOpenTableData || []);
          setLongLoadedTableData(data.excelData.longLoadedTableData || []);
          setShortClosedTableData(data.excelData.shortClosedTableData || []);
          setLongClosedTableData(data.excelData.longClosedTableData || []);

          console.log("Excel data successfully loaded and set");
          return { success: true, message: "Excel data loaded successfully" };
        } else {
          console.log("No Excel data found in database");
          return { success: false, message: "No Excel data found in database" };
        }
      } else {
        const errorData = await response.json();
        console.error("Failed to load Excel data:", errorData);
        return {
          success: false,
          message: errorData.error || "Failed to load Excel data",
        };
      }
    } catch (error) {
      console.error("Error loading Excel data from database:", error);
      return {
        success: false,
        message: error.message || "Unknown error loading Excel data",
      };
    }
  };

  // Function to clear all user data
  const clearAllUserData = () => {
    console.log("Clearing all Excel data for user logout");

    // Clear all state
    setShortOpenTableData([]);
    setShortLoadedTableData([]);
    setLongOpenTableData([]);
    setLongLoadedTableData([]);
    setShortClosedTableData([]);
    setLongClosedTableData([]);

    // Clear localStorage
    if (typeof window !== "undefined") {
      localStorage.removeItem("shortOpenTableData");
      localStorage.removeItem("shortLoadedTableData");
      localStorage.removeItem("longOpenTableData");
      localStorage.removeItem("longLoadedTableData");
      localStorage.removeItem("shortClosedTableData");
      localStorage.removeItem("longClosedTableData");

      // Clear any other trading-related localStorage items
      localStorage.removeItem("schwabLoggedIn");
      localStorage.removeItem("schwabLoginAttempt");

      console.log("Cleared all localStorage data");
    }
  };
  const updateShortClosedStatus = (rowId, newStatus) => {
    setShortOpenTableData((prev) => {
      const row = prev.find((r) => r.id === rowId);
      if (!row) return prev;
      const updatedRow = { ...row, status: newStatus };
      setShortClosedTableData((closedPrev) => {
        if (closedPrev.some((r) => r.id === rowId)) {
          return closedPrev;
        }
        return [...closedPrev, updatedRow];
      });
      return prev.filter((r) => r.id !== rowId);
    });
  };
  const updateLongClosedStatus = (rowId, newStatus) => {
    setLongOpenTableData((prev) => {
      const row = prev.find((r) => r.id === rowId);
      if (!row) return prev;
      const updatedRow = { ...row, status: newStatus };
      setLongClosedTableData((closedPrev) => {
        if (closedPrev.some((r) => r.id === rowId)) {
          return closedPrev;
        }
        return [...closedPrev, updatedRow];
      });
      return prev.filter((r) => r.id !== rowId);
    });
  };

  return (
    <ExcelDataContext.Provider
      value={{
        shortOpenTableData,
        shortLoadedTableData,
        longOpenTableData,
        longLoadedTableData,
        shortClosedTableData,
        longClosedTableData,
        setShortOpenTableData,
        setShortLoadedTableData,
        setLongOpenTableData,
        setLongLoadedTableData,
        setShortClosedTableData,
        setLongClosedTableData,
        updateLongStatus,
        updateShortStatus,
        updateShortClosedStatus,
        updateLongClosedStatus,
        loadExcelDataFromDatabase,
        clearAllUserData,
      }}
    >
      {children}
    </ExcelDataContext.Provider>
  );
}

export function useExcelData() {
  return useContext(ExcelDataContext);
}
