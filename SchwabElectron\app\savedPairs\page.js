"use client";
import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";

export default function SavedPairsPage() {
  const { data: session, status } = useSession();
  const [pairs, setPairs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filter, setFilter] = useState("all");

  useEffect(() => {
    const fetchPairs = async () => {
      if (status !== "authenticated") return;

      try {
        setLoading(true);
        const url =
          filter === "all"
            ? "/api/mongodb/pairs"
            : `/api/mongodb/pairs?status=${filter}`;

        const response = await fetch(url);

        if (!response.ok) {
          throw new Error("Failed to fetch pairs");
        }

        const data = await response.json();
        // Ensure data.pairs is always an array, even if it's null or undefined
        setPairs(Array.isArray(data.pairs) ? data.pairs : []);
      } catch (err) {
        console.error("Error fetching pairs:", err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchPairs();
  }, [status, filter]);

  if (status === "loading") {
    return <div className='p-4 dark:text-gray-200'>Loading session...</div>;
  }

  if (status === "unauthenticated") {
    return (
      <div className='p-4 dark:text-gray-200'>
        Please sign in to view your saved pairs.
      </div>
    );
  }

  return (
    <div className='min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-blue-900/20 dark:to-indigo-900/20'>
      {/* Professional Hero Section */}
      <div className='relative overflow-hidden bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 dark:from-black dark:via-slate-900 dark:to-blue-900'>
        {/* Subtle Background Pattern */}
        <div className='absolute inset-0 bg-black/20'>
          <div
            className='absolute inset-0'
            style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Cpath d='M20 20c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm10 0c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z'/%3E%3C/g%3E%3C/svg%3E")`,
            }}
          ></div>
        </div>

        <div className='relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16'>
          <div className='text-center'>
            <h1 className='text-4xl md:text-5xl font-bold text-white mb-6 leading-tight'>
              Saved Trading
              <span className='bg-gradient-to-r from-emerald-400 to-cyan-400 bg-clip-text text-transparent'>
                {" "}
                Pairs
              </span>
            </h1>
          </div>
        </div>

        {/* Subtle Geometric Elements */}
        <div className='absolute top-1/4 left-8 w-24 h-24 border border-white/10 rounded-full'></div>
        <div className='absolute bottom-1/4 right-8 w-16 h-16 border border-emerald-400/20 rounded-lg rotate-45'></div>
        <div className='absolute top-1/2 right-1/4 w-12 h-12 bg-gradient-to-br from-emerald-500/10 to-cyan-500/10 rounded-full'></div>
      </div>

      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8'>
        {/* Professional Filter Section */}
        <div className='mb-8'>
          <div className='bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden'>
            <div className='bg-gradient-to-r from-slate-700 to-slate-800 p-6'>
              <div className='flex items-center justify-between'>
                <div className='flex items-center'>
                  <div className='w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center mr-3 backdrop-blur-sm border border-white/20'>
                    <svg
                      xmlns='http://www.w3.org/2000/svg'
                      className='h-5 w-5 text-white'
                      fill='none'
                      viewBox='0 0 24 24'
                      stroke='currentColor'
                    >
                      <path
                        strokeLinecap='round'
                        strokeLinejoin='round'
                        strokeWidth={2}
                        d='M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z'
                      />
                    </svg>
                  </div>
                  <div>
                    <h2 className='text-lg font-semibold text-white'>
                      Portfolio Filter
                    </h2>
                    <p className='text-slate-300 text-sm'>
                      Refine your view by trading pair status
                    </p>
                  </div>
                </div>

                <div className='flex items-center space-x-3'>
                  <label
                    htmlFor='filter'
                    className='text-sm font-medium text-white'
                  >
                    Status:
                  </label>
                  <select
                    id='filter'
                    value={filter}
                    onChange={(e) => setFilter(e.target.value)}
                    className='bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-2 text-white text-sm font-medium focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-200'
                  >
                    <option value='all' className='text-gray-900'>
                      All Pairs
                    </option>
                    <option value='WB_LoadedPairs' className='text-gray-900'>
                      Loaded Pairs
                    </option>
                    <option value='WB_OpenPositions' className='text-gray-900'>
                      Open Positions
                    </option>
                    <option
                      value='WB_ClosedPositions'
                      className='text-gray-900'
                    >
                      Closed Pairs
                    </option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        </div>

        {loading ? (
          <div className='max-w-2xl mx-auto'>
            <div className='bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden'>
              <div className='bg-gradient-to-r from-blue-600 to-indigo-600 p-6'>
                <div className='flex items-center justify-center'>
                  <div className='w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mr-4 backdrop-blur-sm'>
                    <svg
                      xmlns='http://www.w3.org/2000/svg'
                      className='h-6 w-6 text-white animate-pulse'
                      fill='none'
                      viewBox='0 0 24 24'
                      stroke='currentColor'
                    >
                      <path
                        strokeLinecap='round'
                        strokeLinejoin='round'
                        strokeWidth={2}
                        d='M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15'
                      />
                    </svg>
                  </div>
                  <div>
                    <h2 className='text-xl font-semibold text-white'>
                      Loading Portfolio Data
                    </h2>
                    <p className='text-blue-100 text-sm'>
                      Retrieving your trading pairs...
                    </p>
                  </div>
                </div>
              </div>
              <div className='p-12 text-center'>
                <div className='relative'>
                  <div className='animate-spin rounded-full h-16 w-16 border-4 border-blue-200 border-t-blue-600 mx-auto mb-6'></div>
                  <div className='absolute inset-0 flex items-center justify-center'>
                    <div className='w-8 h-8 bg-blue-600 rounded-full animate-pulse'></div>
                  </div>
                </div>
                <p className='text-gray-600 dark:text-gray-300 font-medium'>
                  Processing data...
                </p>
              </div>
            </div>
          </div>
        ) : error ? (
          <div className='max-w-2xl mx-auto'>
            <div className='bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-red-200 dark:border-red-800 overflow-hidden'>
              <div className='bg-gradient-to-r from-red-600 to-pink-600 p-6'>
                <div className='flex items-center justify-center'>
                  <div className='w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mr-4 backdrop-blur-sm'>
                    <svg
                      xmlns='http://www.w3.org/2000/svg'
                      className='h-6 w-6 text-white'
                      fill='none'
                      viewBox='0 0 24 24'
                      stroke='currentColor'
                    >
                      <path
                        strokeLinecap='round'
                        strokeLinejoin='round'
                        strokeWidth={2}
                        d='M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                      />
                    </svg>
                  </div>
                  <div>
                    <h2 className='text-xl font-semibold text-white'>
                      Data Loading Error
                    </h2>
                    <p className='text-red-100 text-sm'>
                      Unable to retrieve portfolio data
                    </p>
                  </div>
                </div>
              </div>
              <div className='p-8 text-center'>
                <div className='w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-4'>
                  <svg
                    xmlns='http://www.w3.org/2000/svg'
                    className='h-8 w-8 text-red-600 dark:text-red-400'
                    fill='none'
                    viewBox='0 0 24 24'
                    stroke='currentColor'
                  >
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                      d='M6 18L18 6M6 6l12 12'
                    />
                  </svg>
                </div>
                <p className='text-lg font-semibold text-red-600 dark:text-red-400 mb-2'>
                  Connection Failed
                </p>
                <p className='text-gray-600 dark:text-gray-300'>{error}</p>
                <button
                  onClick={() => window.location.reload()}
                  className='mt-4 px-6 py-2 bg-red-600 hover:bg-red-700 text-white font-medium rounded-lg transition-colors'
                >
                  Retry
                </button>
              </div>
            </div>
          </div>
        ) : !pairs || pairs.length === 0 ? (
          <div className='max-w-2xl mx-auto'>
            <div className='bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-amber-200 dark:border-amber-800 overflow-hidden'>
              <div className='bg-gradient-to-r from-amber-600 to-orange-600 p-6'>
                <div className='flex items-center justify-center'>
                  <div className='w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mr-4 backdrop-blur-sm'>
                    <svg
                      xmlns='http://www.w3.org/2000/svg'
                      className='h-6 w-6 text-white'
                      fill='none'
                      viewBox='0 0 24 24'
                      stroke='currentColor'
                    >
                      <path
                        strokeLinecap='round'
                        strokeLinejoin='round'
                        strokeWidth={2}
                        d='M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
                      />
                    </svg>
                  </div>
                  <div>
                    <h2 className='text-xl font-semibold text-white'>
                      Portfolio Empty
                    </h2>
                    <p className='text-amber-100 text-sm'>
                      No trading pairs found
                    </p>
                  </div>
                </div>
              </div>
              <div className='p-12 text-center'>
                <div className='w-20 h-20 bg-amber-100 dark:bg-amber-900/30 rounded-full flex items-center justify-center mx-auto mb-6'>
                  <svg
                    xmlns='http://www.w3.org/2000/svg'
                    className='h-10 w-10 text-amber-600 dark:text-amber-400'
                    fill='none'
                    viewBox='0 0 24 24'
                    stroke='currentColor'
                  >
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={1.5}
                      d='M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10'
                    />
                  </svg>
                </div>
                <h3 className='text-xl font-semibold text-gray-700 dark:text-gray-200 mb-3'>
                  No Trading Pairs Available
                </h3>
                <p className='text-gray-600 dark:text-gray-400 mb-2'>
                  Your portfolio is currently empty.
                </p>
                <p className='text-gray-600 dark:text-gray-400 mb-6'>
                  Create and save trading pairs in the WB Configuration to see
                  them here.
                </p>
                <a
                  href='/Strategies/WB/configuration'
                  className='inline-flex items-center px-6 py-3 bg-amber-600 hover:bg-amber-700 text-white font-semibold rounded-lg transition-all duration-200 transform hover:scale-105'
                >
                  <svg
                    xmlns='http://www.w3.org/2000/svg'
                    className='h-5 w-5 mr-2'
                    fill='none'
                    viewBox='0 0 24 24'
                    stroke='currentColor'
                  >
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                      d='M12 6v6m0 0v6m0-6h6m-6 0H6'
                    />
                  </svg>
                  Create Trading Pairs
                </a>
              </div>
            </div>
          </div>
        ) : (
          <div>
            {/* Portfolio Summary Header */}
            <div className='mb-8'>
              <div className='bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden'>
                <div className='bg-gradient-to-r from-emerald-600 to-teal-600 p-6'>
                  <div className='flex items-center justify-between'>
                    <div className='flex items-center'>
                      <div className='w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mr-4 backdrop-blur-sm border border-white/20'>
                        <svg
                          xmlns='http://www.w3.org/2000/svg'
                          className='h-6 w-6 text-white'
                          fill='none'
                          viewBox='0 0 24 24'
                          stroke='currentColor'
                        >
                          <path
                            strokeLinecap='round'
                            strokeLinejoin='round'
                            strokeWidth={2}
                            d='M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z'
                          />
                        </svg>
                      </div>
                      <div>
                        <h2 className='text-xl font-semibold text-white'>
                          Portfolio Overview
                        </h2>
                        <p className='text-emerald-100 text-sm'>
                          {pairs.length} trading pairs in your collection
                        </p>
                      </div>
                    </div>
                    <div className='text-right'>
                      <div className='text-2xl font-bold text-white'>
                        {pairs.length}
                      </div>
                      <div className='text-emerald-100 text-sm'>
                        Total Pairs
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Professional Trading Pairs Grid */}
            <div className='grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6'>
              {pairs.map((pair, index) => (
                <div
                  key={pair._id || pair.id || `pair-${index}`}
                  className='group relative overflow-hidden rounded-xl bg-white dark:bg-gray-800 shadow-lg border border-gray-200 dark:border-gray-700 hover:shadow-xl hover:-translate-y-1 transition-all duration-300'
                >
                  {/* Status Badge */}
                  <div className='absolute top-4 right-4 z-10'>
                    <span
                      className={`px-3 py-1 rounded-full text-xs font-semibold backdrop-blur-sm border ${
                        pair.status === "WB_LoadedPairs"
                          ? "bg-blue-500/90 text-white border-blue-400/50"
                          : pair.status === "WB_ClosedPositions"
                          ? "bg-red-500/90 text-white border-red-400/50"
                          : "bg-emerald-500/90 text-white border-emerald-400/50"
                      }`}
                    >
                      {pair.status === "WB_LoadedPairs"
                        ? "Loaded"
                        : pair.status === "WB_ClosedPositions" ||
                          pair.status === "closed"
                        ? "Closed"
                        : "Open"}
                    </span>
                  </div>

                  {/* Pair Header */}
                  <div className='bg-gradient-to-r from-slate-700 to-slate-800 p-6 pb-4'>
                    <div className='flex items-center justify-between'>
                      <div>
                        <h3 className='text-xl font-bold text-white mb-1'>
                          {pair.shortComponent?.ticker} /{" "}
                          {pair.longComponent?.ticker}
                        </h3>
                        <p className='text-slate-300 text-sm'>Trading Pair</p>
                      </div>
                      <div className='w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center backdrop-blur-sm border border-white/20'>
                        <svg
                          xmlns='http://www.w3.org/2000/svg'
                          className='h-5 w-5 text-white'
                          fill='none'
                          viewBox='0 0 24 24'
                          stroke='currentColor'
                        >
                          <path
                            strokeLinecap='round'
                            strokeLinejoin='round'
                            strokeWidth={2}
                            d='M13 7h8m0 0v8m0-8l-8 8-4-4-6 6'
                          />
                        </svg>
                      </div>
                    </div>
                  </div>

                  {/* Card Content */}
                  <div className='p-6'>
                    {/* Components Grid */}
                    <div className='grid grid-cols-2 gap-4 mb-6'>
                      {/* Short Component */}
                      <div className='bg-gradient-to-br from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 rounded-lg p-4 border border-red-200 dark:border-red-800'>
                        <div className='flex items-center mb-3'>
                          <div className='w-6 h-6 bg-red-500 rounded-full flex items-center justify-center mr-2'>
                            <svg
                              xmlns='http://www.w3.org/2000/svg'
                              className='h-3 w-3 text-white'
                              fill='none'
                              viewBox='0 0 24 24'
                              stroke='currentColor'
                            >
                              <path
                                strokeLinecap='round'
                                strokeLinejoin='round'
                                strokeWidth={3}
                                d='M19 14l-7 7m0 0l-7-7m7 7V3'
                              />
                            </svg>
                          </div>
                          <h4 className='font-semibold text-red-700 dark:text-red-400 text-sm'>
                            SHORT
                          </h4>
                        </div>
                        <div className='space-y-2'>
                          <div className='flex justify-between items-center'>
                            <span className='text-xs text-red-600 dark:text-red-400 font-medium'>
                              Ticker
                            </span>
                            <span className='text-sm font-bold text-red-800 dark:text-red-200'>
                              {pair.shortComponent?.ticker || "N/A"}
                            </span>
                          </div>
                          <div className='flex justify-between items-center'>
                            <span className='text-xs text-gray-600 dark:text-gray-400'>
                              Cost
                            </span>
                            <span className='text-sm font-medium text-gray-800 dark:text-gray-200'>
                              {pair.shortComponent?.formattedCost || "N/A"}
                            </span>
                          </div>
                          <div className='flex justify-between items-center'>
                            <span className='text-xs text-gray-600 dark:text-gray-400'>
                              Shares
                            </span>
                            <span className='text-sm font-medium text-gray-800 dark:text-gray-200'>
                              {pair.shortComponent?.formattedAmt || "N/A"}
                            </span>
                          </div>
                          <div className='flex justify-between items-center'>
                            <span className='text-xs text-gray-600 dark:text-gray-400'>
                              Last
                            </span>
                            <span className='text-sm font-medium text-gray-800 dark:text-gray-200'>
                              {pair.shortComponent?.formattedLast || "N/A"}
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Long Component */}
                      <div className='bg-gradient-to-br from-emerald-50 to-green-50 dark:from-emerald-900/20 dark:to-green-900/20 rounded-lg p-4 border border-emerald-200 dark:border-emerald-800'>
                        <div className='flex items-center mb-3'>
                          <div className='w-6 h-6 bg-emerald-500 rounded-full flex items-center justify-center mr-2'>
                            <svg
                              xmlns='http://www.w3.org/2000/svg'
                              className='h-3 w-3 text-white'
                              fill='none'
                              viewBox='0 0 24 24'
                              stroke='currentColor'
                            >
                              <path
                                strokeLinecap='round'
                                strokeLinejoin='round'
                                strokeWidth={3}
                                d='M5 10l7-7m0 0l7 7m-7-7v18'
                              />
                            </svg>
                          </div>
                          <h4 className='font-semibold text-emerald-700 dark:text-emerald-400 text-sm'>
                            LONG
                          </h4>
                        </div>
                        <div className='space-y-2'>
                          <div className='flex justify-between items-center'>
                            <span className='text-xs text-emerald-600 dark:text-emerald-400 font-medium'>
                              Ticker
                            </span>
                            <span className='text-sm font-bold text-emerald-800 dark:text-emerald-200'>
                              {pair.longComponent?.ticker || "N/A"}
                            </span>
                          </div>
                          <div className='flex justify-between items-center'>
                            <span className='text-xs text-gray-600 dark:text-gray-400'>
                              Cost
                            </span>
                            <span className='text-sm font-medium text-gray-800 dark:text-gray-200'>
                              {pair.longComponent?.formattedCost || "N/A"}
                            </span>
                          </div>
                          <div className='flex justify-between items-center'>
                            <span className='text-xs text-gray-600 dark:text-gray-400'>
                              Shares
                            </span>
                            <span className='text-sm font-medium text-gray-800 dark:text-gray-200'>
                              {pair.longComponent?.formattedAmt || "N/A"}
                            </span>
                          </div>
                          <div className='flex justify-between items-center'>
                            <span className='text-xs text-gray-600 dark:text-gray-400'>
                              Last
                            </span>
                            <span className='text-sm font-medium text-gray-800 dark:text-gray-200'>
                              {pair.longComponent?.formattedLast || "N/A"}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Performance & Metadata */}
                    <div className='bg-gradient-to-r from-slate-50 to-gray-50 dark:from-slate-800/50 dark:to-gray-800/50 rounded-lg p-4 border border-slate-200 dark:border-slate-700'>
                      <div className='flex justify-between items-center mb-3'>
                        <div className='flex items-center'>
                          <div className='w-6 h-6 bg-slate-500 rounded-full flex items-center justify-center mr-2'>
                            <svg
                              xmlns='http://www.w3.org/2000/svg'
                              className='h-3 w-3 text-white'
                              fill='none'
                              viewBox='0 0 24 24'
                              stroke='currentColor'
                            >
                              <path
                                strokeLinecap='round'
                                strokeLinejoin='round'
                                strokeWidth={2}
                                d='M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1'
                              />
                            </svg>
                          </div>
                          <span className='text-sm font-medium text-slate-700 dark:text-slate-300'>
                            Performance
                          </span>
                        </div>
                        <div
                          className={`px-3 py-1 rounded-full text-sm font-bold ${
                            parseFloat(pair.combinedPNL) > 0
                              ? "bg-emerald-100 text-emerald-800 dark:bg-emerald-900/50 dark:text-emerald-200"
                              : parseFloat(pair.combinedPNL) < 0
                              ? "bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-200"
                              : "bg-gray-100 text-gray-800 dark:bg-gray-900/50 dark:text-gray-200"
                          }`}
                        >
                          {pair.combinedPNL || "N/A"}
                        </div>
                      </div>
                      <div className='text-xs text-gray-500 dark:text-gray-400 flex items-center'>
                        <svg
                          xmlns='http://www.w3.org/2000/svg'
                          className='h-3 w-3 mr-1'
                          fill='none'
                          viewBox='0 0 24 24'
                          stroke='currentColor'
                        >
                          <path
                            strokeLinecap='round'
                            strokeLinejoin='round'
                            strokeWidth={2}
                            d='M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z'
                          />
                        </svg>
                        Created:{" "}
                        {pair.createdAt
                          ? new Date(pair.createdAt).toLocaleDateString()
                          : "N/A"}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
