#!/usr/bin/env node

/**
 * Admin User Creation Script
 * Creates an admin user for the application
 */

import { PrismaClient } from "@prisma/client";
import bcrypt from "bcryptjs";
import { config } from "dotenv";

// Load environment variables
config();

const prisma = new PrismaClient();

async function createAdminUser() {
  console.log("👤 Creating admin user...\n");

  try {
    // Check if admin user already exists
    const existingAdmin = await prisma.user.findFirst({
      where: { role: "ADMIN" },
    });

    if (existingAdmin) {
      console.log("✅ Admin user already exists!");
      console.log(`   Email: ${existingAdmin.email}`);
      console.log(`   Name: ${existingAdmin.name}`);
      return;
    }

    // Default admin credentials
    const adminEmail = "<EMAIL>";
    const adminPassword = "admin123";
    const adminName = "Administrator";

    // Hash the password
    const hashedPassword = await bcrypt.hash(adminPassword, 12);

    // Create admin user
    const adminUser = await prisma.user.create({
      data: {
        email: adminEmail,
        name: admin<PERSON><PERSON>,
        password: hashedPassword,
        role: "ADMIN",
        emailVerified: new Date(),
      },
    });

    console.log("🎉 Admin user created successfully!");
    console.log(`   Email: ${adminUser.email}`);
    console.log(`   Password: ${adminPassword}`);
    console.log(`   Role: ${adminUser.role}`);
    console.log(
      "\n⚠️  IMPORTANT: Change the default password after first login!"
    );
  } catch (error) {
    console.error("❌ Failed to create admin user:", error.message);

    if (error.code === "P2002") {
      console.log("   User with this email already exists.");
    } else {
      console.log("   Make sure your database is running and accessible.");
    }
  } finally {
    await prisma.$disconnect();
  }
}

createAdminUser();
