import axios from 'axios';

// Create an Axios instance with custom configuration
const axiosInstance = axios.create({
  timeout: 30000, // 30 seconds timeout
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  }
});

// Add request interceptor
axiosInstance.interceptors.request.use(
  config => {
    // You can modify the request config here
    console.log(`Making ${config.method.toUpperCase()} request to: ${config.url}`);
    return config;
  },
  error => {
    console.error('Request error:', error);
    return Promise.reject(error);
  }
);

// Add response interceptor
axiosInstance.interceptors.response.use(
  response => {
    // You can modify the response here
    return response;
  },
  error => {
    // Handle network errors
    if (error.message === 'Network Error') {
      console.error('Network error detected. Please check your internet connection.');
      // You can add custom handling here
    }
    
    // Handle timeout errors
    if (error.code === 'ECONNABORTED') {
      console.error('Request timeout. The server took too long to respond.');
    }
    
    // Handle server errors
    if (error.response) {
      console.error(`Server error: ${error.response.status} - ${error.response.statusText}`);
      console.error('Error data:', error.response.data);
    }
    
    return Promise.reject(error);
  }
);

export default axiosInstance;
