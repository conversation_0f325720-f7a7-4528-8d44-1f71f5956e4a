"use client";
import { useState, useEffect } from "react";
import { DndContext, closestCenter } from "@dnd-kit/core";
import { useRouter } from "next/navigation";
import {
  arrayMove,
  SortableContext,
  verticalListSortingStrategy,
  useSortable,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { usePairArray } from "@/app/pairArray/PairArray";
import { useExcelData } from "../configuration/page.js";

function ShortSortableRow({ id, item }) {
  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({ id: id.toString() });
  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    cursor: "grab",
  };

  // Determine color for change value
  const changeValue = parseFloat(item.formattedChange);
  const changeColor = changeValue > 0 ? "text-green-500" : changeValue < 0 ? "text-red-500" : "";

  // Determine color for amount value
  const amtValue = parseFloat(item.formattedAmt);
  const amtColor = amtValue < 0 ? "text-red-500" : "text-green-500";

  return (
    <tr
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className="border-b transition-colors hover:bg-muted/50"
    >
      <td className="px-0 py-0 h-8 text-sm text-right font-medium">{item.formattedCost || "-"}</td>
      <td className={`px-0 py-0 h-8 text-sm text-right font-medium ${amtColor}`}>{item.formattedAmt || "-"}</td>
      <td className="px-0 py-0 h-8 text-sm text-right font-medium text-yellow-500">{item.ticker || "-"}</td>
      <td className="px-0 py-0 h-8 text-sm text-right font-medium">{item.formattedBid || "-"}</td>
      <td className="px-0 py-0 h-8 text-sm text-right font-medium">{item.formattedAsk || "-"}</td>
      <td className="px-0 py-0 h-8 text-sm text-right font-medium text-yellow-500">{item.formattedLast || "-"}</td>
      <td className={`px-0 py-0 h-8 text-sm text-right font-medium ${changeColor}`}>{item.formattedChange || "-"}</td>
      <td className="px-0 py-0 h-8 text-sm text-right text-xs">{item.formattedVolume || "-"}</td>
      <td className="px-0 py-0 h-8 text-sm text-right text-xs">{item.formattedDividend || "-"}</td>
      <td className="px-0 py-0 h-8 text-sm text-right text-xs bg-slate-900 text-white">{item.formattedUserDividend || "-"}</td>
      <td className="px-0 py-0 h-8 text-sm text-right font-medium bg-slate-900 text-white">{item.formattedSpreadUser || "-"}</td>
      <td className="px-0 py-0 h-8 text-sm text-right font-medium">{item.dollarCost || "-"}</td>
    </tr>
  );
}

function LongSortableRow({ id, item, handleMoveToOpen  }) {
  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({ id: id.toString() });
  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    cursor: "grab",
  };

  // Determine color for change value
  const changeValue = parseFloat(item.formattedChange);
  const changeColor = changeValue > 0 ? "text-green-500" : changeValue < 0 ? "text-red-500" : "";

  // Determine color for amount value
  const amtValue = parseFloat(item.formattedAmt);
  const amtColor = amtValue < 0 ? "text-red-500" : "text-green-500";

  // Determine color for PNL
  const pnlValue = parseFloat(item.pnl);
  const pnlColor = pnlValue > 0 ? "text-green-500" : pnlValue < 0 ? "text-red-500" : "";

  return (
    <tr
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className="border-b transition-colors hover:bg-muted/50"
    >
      <td className="px-0 py-0 h-8 text-sm text-right font-medium">{item.expectedQuantity || "-"}</td>
      <td className="px-0 py-0 h-8 text-sm text-right text-xs bg-slate-900 text-white">{item.formattedUserDividend || "-"}</td>
      <td className="px-0 py-0 h-8 text-sm text-right font-medium">{item.formattedCost || "-"}</td>
      <td className={`px-0 py-0 h-8 text-sm text-right font-medium ${amtColor}`}>{item.formattedAmt || "-"}</td>
      <td className="px-0 py-0 h-8 text-sm text-right font-medium text-yellow-500">{item.ticker || "-"}</td>
      <td className="px-0 py-0 h-8 text-sm text-right font-medium">{item.formattedBid || "-"}</td>
      <td className="px-0 py-0 h-8 text-sm text-right font-medium">{item.formattedAsk || "-"}</td>
      <td className="px-0 py-0 h-8 text-sm text-right font-medium text-yellow-500">{item.formattedLast || "-"}</td>
      <td className={`px-0 py-0 h-8 text-sm text-right font-medium ${changeColor}`}>{item.formattedChange || "-"}</td>
      <td className="px-0 py-0 h-8 text-sm text-right text-xs">{item.formattedVolume || "-"}</td>
      <td className="px-0 py-0 h-8 text-sm text-right text-xs">{item.formattedDividend || "-"}</td>
      <td className="px-0 py-0 h-8 text-sm text-right font-medium bg-slate-900 text-white">{item.formattedSpreadUser || "-"}</td>
      <td className="px-0 py-0 h-8 text-sm text-right text-xs">0</td> {/* Short Div placeholder */}
      <td className="px-0 py-0 h-8 text-sm text-right text-xs">0</td> {/* Long Div placeholder */}
      <td className="px-0 py-0 h-8 text-sm text-right font-medium">{item.dollarCost || "-"}</td>
      <td className={`px-0 py-0 h-8 text-sm text-right font-medium ${pnlColor}`}>{item.pnl || "-"}</td>
      <td className="px-0 py-0 h-8 text-sm text-right">0</td> {/* Short placeholder */}
      <td className="px-0 py-0 h-8 text-sm text-right">{item.formattedAmt || "-"}</td> {/* Long placeholder */}
    </tr>
  );
}

export function PairArrayTables() {
  const router = useRouter();
  const { pairArray, setPairArray, savePairsToDatabase, isSaving, saveStatus } = usePairArray();
  const {
    shortOpenTableData, shortLoadedTableData, longOpenTableData, longLoadedTableData, setShortOpenTableData, setShortLoadedTableData, setLongOpenTableData, setLongLoadedTableData, updateLongStatus, updateShortStatus
  } = useExcelData();
  const [loadedShortItems, setLoadedShortItems] = useState([]);
  const [loadedLongItems, setLoadedLongItems] = useState([]);
  const [openShortItems, setOpenShortItems] = useState([]);
  const [openLongItems, setOpenLongItems] = useState([]);

  const loadedPairs = pairArray ? pairArray.filter(pair => pair.status === "WB_LoadedPairs") : [];
  const openPairs = pairArray ? pairArray.filter(pair => pair.status === "WB_OpenPositions") : [];

  const handleGetPairArray = () => {
    console.log("Pair Array Data:", pairArray);
  }

  const handleRefresh = () => {
    // Refresh the page to get the latest data
    router.refresh();
  }

  // Effect to update loadedShortItems and loadedLongItems when pairArray changes
  useEffect(() => {
    const newLoadedShort = loadedPairs.map(pair => ({
      ...pair.shortComponent,
      pairKey: pair.key
    }));
    const newLoadedLong = loadedPairs.map(pair => ({
      ...pair.longComponent,
      pairKey: pair.key
    }));
    if (JSON.stringify(newLoadedShort) !== JSON.stringify(loadedShortItems)) {
      setLoadedShortItems(newLoadedShort);
    }
    if (JSON.stringify(newLoadedLong) !== JSON.stringify(loadedLongItems)) {
      setLoadedLongItems(newLoadedLong);
    }
  }, [loadedPairs, loadedShortItems, loadedLongItems]);

  // Effect to update pairArray when shortLoadedTableData or longLoadedTableData changes
  useEffect(() => {
    if (shortLoadedTableData.length === 0 || longLoadedTableData.length === 0) return;

    console.log('Excel data changed, updating pairs...');

    // Create pairs from shortLoadedTableData and longLoadedTableData
    const minLength = Math.min(shortLoadedTableData.length, longLoadedTableData.length);
    let newPairs = [];

    for (let i = 0; i < minLength; i++) {
      const shortComponent = shortLoadedTableData[i];
      const longComponent = longLoadedTableData[i];

      if (!shortComponent || !longComponent) continue;

      const status = "WB_LoadedPairs";
      const key = `${shortComponent.ticker}-${longComponent.ticker}-${i}`;

      newPairs.push({
        id: i + 1,
        key,
        longComponent,
        shortComponent,
        combinedPNL: "0.00",
        status,
      });
    }

    if (newPairs.length > 0) {
      console.log('Setting new pairs from Excel data:', newPairs.length);
      setPairArray(newPairs);
    }
  }, [shortLoadedTableData, longLoadedTableData, setPairArray]);

  // Effect to update openShortItems and openLongItems when openPairs changes
  useEffect(() => {
    const newOpenShort = openPairs.map(pair => ({
      ...pair.shortComponent,
      pairKey: pair.key
    }));
    const newOpenLong = openPairs.map(pair => ({
      ...pair.longComponent,
      pairKey: pair.key
    }));
    if (JSON.stringify(newOpenShort) !== JSON.stringify(openShortItems)) {
      setOpenShortItems(newOpenShort);
    }
    if (JSON.stringify(newOpenLong) !== JSON.stringify(openLongItems)) {
      setOpenLongItems(newOpenLong);
    }
  }, [openPairs, openShortItems, openLongItems]);

  // Effect to update pairArray when shortOpenTableData or longOpenTableData changes
  useEffect(() => {
    if (shortOpenTableData.length === 0 || longOpenTableData.length === 0) return;

    console.log('Open Excel data changed, updating open pairs...');

    // Create pairs from shortOpenTableData and longOpenTableData
    const minLength = Math.min(shortOpenTableData.length, longOpenTableData.length);
    let newOpenPairs = [];

    for (let i = 0; i < minLength; i++) {
      const shortComponent = shortOpenTableData[i];
      const longComponent = longOpenTableData[i];

      if (!shortComponent || !longComponent) continue;

      const status = "WB_OpenPositions";
      const key = `${shortComponent.ticker}-${longComponent.ticker}-${i}-open`;

      newOpenPairs.push({
        id: i + 1,
        key,
        longComponent,
        shortComponent,
        combinedPNL: "0.00",
        status,
      });
    }

    if (newOpenPairs.length > 0) {
      console.log('Setting new open pairs from Excel data:', newOpenPairs.length);
      // Update pairArray by adding open pairs
      setPairArray(prev => {
        // Filter out existing open pairs
        const loadedPairs = prev.filter(pair => pair.status !== "WB_OpenPositions");
        return [...loadedPairs, ...newOpenPairs];
      });
    }
  }, [shortOpenTableData, longOpenTableData, setPairArray]);

  const extractShortFields = (component) => ({
    ticker: component.ticker || "",
    shares: component.expectedQuantity || "",
    sector: component.sectorValue || "",
    spread: component.formattedSpreadUser || "",
    volume: component.formattedLoadedVolume || "",
    id: component.id || "",
    status: component.statusValue || "",
  });

  const extractLongFields = (component) => ({
    ticker: component.ticker || "",
    shares: component.expectedQuantity || "",
    sector: component.sectorValue || "",
    spread: component.formattedSpreadUser || "",
    volume: component.formattedLoadedVolume || "",
    status: component.statusValue || "",
    id: component.id || ""
  });

  const handleDragEndLoadedShort = (event) => {
    const { active, over } = event;
    if (!over || active.id === over.id) return;
    const oldIndex = Number(active.id);
    const newIndex = Number(over.id);
    const newOrder = arrayMove(loadedShortItems, oldIndex, newIndex);
    setLoadedShortItems(newOrder);
    setShortLoadedTableData(newOrder.map(extractShortFields));
  };

  const handleDragEndLoadedLong = (event) => {
    const { active, over } = event;
    if (!over || active.id === over.id) return;
    const oldIndex = Number(active.id);
    const newIndex = Number(over.id);
    const newOrder = arrayMove(loadedLongItems, oldIndex, newIndex);
    setLoadedLongItems(newOrder);
    setLongLoadedTableData(newOrder.map(extractLongFields));
  };

  const handleDragEndOpenShort = (event) => {
    const { active, over } = event;
    if (!over || active.id === over.id) return;
    const oldIndex = openShortItems.findIndex((_, idx) => idx.toString() === active.id);
    const newIndex = openShortItems.findIndex((_, idx) => idx.toString() === over.id);
    if (oldIndex === -1 || newIndex === -1) return;
    const newOrder = arrayMove(openShortItems, oldIndex, newIndex);
    setOpenShortItems(newOrder);
    setShortOpenTableData(newOrder.map(extractShortFields));
  };

  const handleDragEndOpenLong = (event) => {
    const { active, over } = event;
    if (!over || active.id === over.id) return;
    const oldIndex = openLongItems.findIndex((_, idx) => idx.toString() === active.id);
    const newIndex = openLongItems.findIndex((_, idx) => idx.toString() === over.id);
    if (oldIndex === -1 || newIndex === -1) return;
    const newOrder = arrayMove(openLongItems, oldIndex, newIndex);
    setOpenLongItems(newOrder);
    setLongOpenTableData(newOrder.map(extractLongFields));
  };

  function handleMoveToOpen(e, longId, idx) {
    e.stopPropagation();
    // Get the long item by ID
    const longItem = longLoadedTableData.find(item => item.id === longId);
    if (!longItem) {
      console.error(`Long item with ID ${longId} not found`);
      return;
    }

    // Find the corresponding short item by index
    // This assumes that the short and long items are in the same order in the loaded tables
    const shortItem = shortLoadedTableData[idx];
    if (!shortItem) {
      console.error(`Short item at index ${idx} not found`);
      return;
    }

    console.log(`Moving pair to open positions: Long ID=${longId}, Short ID=${shortItem.id}`);

    // Update both statuses
    updateLongStatus(longId, "WB_OpenPositions");
    updateShortStatus(shortItem.id, "WB_OpenPositions");
  }

  return (
    <div className="w-full">
      <div className="flex flex-wrap gap-2 mb-6">
        <button className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded" onClick={handleGetPairArray}>
          Get Pair Array
        </button>
        <button
          className={`${isSaving ? 'bg-gray-500' : 'bg-green-500 hover:bg-green-700'} text-white font-bold py-1 px-2 rounded`}
          onClick={savePairsToDatabase}
          disabled={isSaving}
        >
          {isSaving ? 'Saving...' : 'Save to Database'}
        </button>
        <button className="bg-purple-500 hover:bg-purple-700 text-white font-bold py-1 px-2 rounded" onClick={handleRefresh}>
          Refresh Data
        </button>
        {saveStatus && (
          <div className={`py-1 px-2 rounded ${saveStatus.includes('Error') ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'}`}>
            {saveStatus}
          </div>
        )}
      </div>

      {/* Loaded Pairs Section */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4 pb-2 border-b">Loaded Pairs</h2>
        <div className="flex flex-nowrap gap-0">
          <div className="flex-auto min-w-0">
            <h3 className="text-lg font-medium mb-2 text-red-700">Loaded SHORT</h3>
            <DndContext collisionDetection={closestCenter} onDragEnd={handleDragEndLoadedShort}>
              <SortableContext items={loadedShortItems.map((_, idx) => idx.toString())} strategy={verticalListSortingStrategy}>
                <div className="bg-white overflow-hidden">
                  <table className="w-full caption-bottom text-sm table-fixed">
                    <thead className="[&_tr]:border-b">
                      <tr style={{ background: "#f8d7da" }}>
                        <th className="border px-1 py-0 h-10 text-sm text-right">Cost</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Shares</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Sym</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Bid</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Ask</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Last</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Chg</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Vol/1k</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Div</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Xd</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">S</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Orders</th>
                      </tr>
                    </thead>
                    <tbody>
                      {loadedShortItems.map((item, idx) => (
                        <ShortSortableRow key={idx} id={idx} item={item} />
                      ))}
                    </tbody>
                  </table>
                </div>
              </SortableContext>
            </DndContext>
          </div>
          <div className="flex-auto min-w-0">
            <h3 className="text-lg font-medium mb-2 text-green-700">Loaded LONG</h3>
            <DndContext collisionDetection={closestCenter} onDragEnd={handleDragEndLoadedLong}>
              <SortableContext items={loadedLongItems.map((_, idx) => idx.toString())} strategy={verticalListSortingStrategy}>
                <div className="bg-white overflow-hidden">
                  <table className="w-full caption-bottom text-sm table-fixed">
                    <thead className="[&_tr]:border-b">
                      <tr style={{ background: "#d4edda" }}>
                        <th className="border px-0 py-0 h-10 text-sm text-right">S</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Xd</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Cost</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Shares</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Sym</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Bid</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Ask</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Last</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Chg</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Vol/1k</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Div</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">S</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Short Div</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Long Div</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">L</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">P/L</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Short</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Long</th>
                      </tr>
                    </thead>
                    <tbody>
                      {loadedLongItems.map((item, idx) => (
                        <LongSortableRow key={idx} id={idx} item={item} handleMoveToOpen={handleMoveToOpen} />
                      ))}
                    </tbody>
                  </table>
                </div>
              </SortableContext>
            </DndContext>
          </div>
          <div className="w-16">
            <h3 className="text-base font-medium mb-2 text-gray-700">Act</h3>
            <div className="bg-white overflow-hidden">
              <table className="w-full caption-bottom text-sm table-fixed">
                <thead className="[&_tr]:border-b">
                  <tr style={{ background: "#e2e3e5" }}>
                    <th className="border px-0 py-0 h-10 text-sm text-center">Act</th>
                  </tr>
                </thead>
                <tbody>
                  {loadedLongItems.map((item, idx) => (
                    <tr key={idx}>
                      <td>
                        <button
                          className="bg-green-500 hover:bg-green-700 text-white text-sm py-0.5 px-1.5 rounded"
                          onClick={(e) => handleMoveToOpen(e, item.id, idx)}
                        >
                          Open
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      {/* Open Pairs Section */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4 pb-2 border-b">Open Pairs</h2>
        <div className="flex flex-nowrap gap-0">
          <div className="flex-auto min-w-0">
            <h3 className="text-lg font-medium mb-2 text-red-700">Open SHORT</h3>
            <DndContext collisionDetection={closestCenter} onDragEnd={handleDragEndOpenShort}>
              <SortableContext items={openShortItems.map((_, idx) => idx.toString())} strategy={verticalListSortingStrategy}>
                <div className="bg-white overflow-hidden">
                  <table className="w-full caption-bottom text-sm table-fixed">
                    <thead className="[&_tr]:border-b">
                      <tr style={{ background: "#f8d7da" }}>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Cost</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Shares</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Sym</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Bid</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Ask</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Last</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Chg</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Vol/1k</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Div</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Xd</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">S</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Orders</th>
                      </tr>
                    </thead>
                    <tbody>
                      {openShortItems.map((item, idx) => (
                        <ShortSortableRow key={idx} id={idx} item={item} />
                      ))}
                    </tbody>
                  </table>
                </div>
              </SortableContext>
            </DndContext>
          </div>
          <div className="flex-auto min-w-0">
            <h3 className="text-lg font-medium mb-2 text-green-700">Open LONG</h3>
            <DndContext collisionDetection={closestCenter} onDragEnd={handleDragEndOpenLong}>
              <SortableContext items={openLongItems.map((_, idx) => idx.toString())} strategy={verticalListSortingStrategy}>
                <div className="bg-white overflow-hidden">
                  <table className="w-full caption-bottom text-sm table-fixed">
                    <thead className="[&_tr]:border-b">
                      <tr style={{ background: "#d4edda" }}>
                        <th className="border px-0 py-0 h-10 text-sm text-right">S</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Xd</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Cost</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Shares</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Sym</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Bid</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Ask</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Last</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Chg</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Vol/1k</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Div</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">S</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Short Div</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Long Div</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">L</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">P/L</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Short</th>
                        <th className="border px-0 py-0 h-10 text-sm text-right">Long</th>
                      </tr>
                    </thead>
                    <tbody>
                      {openLongItems.map((item, idx) => (
                        <LongSortableRow key={idx} id={idx} item={item} handleMoveToOpen={handleMoveToOpen} />
                      ))}
                    </tbody>
                  </table>
                </div>
              </SortableContext>
            </DndContext>
          </div>
        </div>
      </div>
    </div>
  );
}
