"use client";
import { createContext, useContext, useState, useEffect } from "react";
import { useComponentArray } from "./PairComponent";
import { useExcelData } from "../Strategies/WB/ExcelDataContext";

const PairArrayContext = createContext(null);

export function PairArrayProvider({ children }) {
  const { componentArray } = useComponentArray();
  const {
    shortOpenTableData,
    shortLoadedTableData,
    longOpenTableData,
    longLoadedTableData,
    shortClosedTableData,
    longClosedTableData,
  } = useExcelData();
  const [pairArray, setPairArray] = useState(() => {
    if (typeof window !== "undefined") {
      const data = localStorage.getItem("pairArray");
      return data ? JSON.parse(data) : [];
    }
    return [];
  });
  const [pairStatus, setPairStatus] = useState(() => {
    if (typeof window !== "undefined") {
      const data = localStorage.getItem("pairStatus");
      return data ? JSON.parse(data) : {};
    }
    return {};
  });

  // Generate pairs from component array
  useEffect(() => {
    if (
      !componentArray ||
      !Array.isArray(componentArray.longComponents) ||
      !Array.isArray(componentArray.shortComponents)
    )
      return;

    const { longComponents, shortComponents } = componentArray;
    const minLength = Math.min(longComponents.length, shortComponents.length);
    let pairs = [];
    for (let i = 0; i < minLength; i++) {
      const longComponent = longComponents[i];
      const shortComponent = shortComponents[i];
      const status = longComponent.statusValue;
      const pnlLong = parseFloat(longComponent.formattedPnl || 0);
      const pnlShort = parseFloat(shortComponent.formattedPnl || 0);
      const combinedPNL = (pnlLong + pnlShort).toFixed(2);
      const key = `${shortComponent.ticker}-${longComponent.ticker}-${i}`;
      pairs.push({
        id: i + 1,
        key,
        longComponent,
        shortComponent,
        combinedPNL,
        status,
      });
    }
    setPairArray(pairs);
  }, [componentArray]);

  // Save pairArray to localStorage whenever it changes
  useEffect(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem("pairArray", JSON.stringify(pairArray));
    }
  }, [pairArray]);

  // Save pairStatus to localStorage whenever it changes
  useEffect(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem("pairStatus", JSON.stringify(pairStatus));
    }
  }, [pairStatus]);

  // Variables for manual saving
  const [isSaving, setIsSaving] = useState(false);
  const [saveStatus, setSaveStatus] = useState("");

  // Variables for manual loading
  const [isLoading, setIsLoading] = useState(false);
  const [loadStatus, setLoadStatus] = useState("");

  // Function to manually save pairs and Excel data to database
  const savePairsToDatabase = async () => {
    // Skip if already saving
    if (isSaving) {
      setSaveStatus("Already saving data");
      return;
    }

    setIsSaving(true);
    setSaveStatus("Saving data to database...");

    try {
      // Get the current state of pairArray to ensure we're using the most up-to-date version
      console.log("Current pairArray state:", pairArray);
      console.log(
        "pairArray keys:",
        pairArray.map((pair) => pair.key)
      );

      // Save pairs to database
      if (pairArray.length > 0) {
        console.log("Saving pairs to database:", pairArray.length);

        // Create a deep copy of the pairArray to avoid any reference issues
        const pairsToSave = JSON.parse(JSON.stringify(pairArray));

        // Transform the pairs to match the expected schema
        const transformedPairs = pairsToSave.map((pair) => ({
          pairKey: pair.key || null, // Map key to pairKey
          status: pair.status || "",
          shortComponent: pair.shortComponent || {},
          longComponent: pair.longComponent || {},
          combinedPNL: pair.combinedPNL || "0",
        }));

        console.log("Transformed pairs to save:", transformedPairs.length);
        console.log("Sample pair:", transformedPairs[0]);

        const pairsResponse = await fetch("/api/mongodb/pairs", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ pairs: transformedPairs }),
        });

        const pairsData = await pairsResponse.json();

        if (!pairsResponse.ok) {
          console.error("Failed to save pairs:", pairsData);
          setSaveStatus(
            `Error saving pairs: ${pairsData.error || "Unknown error"}`
          );
          setIsSaving(false);
          return;
        }

        console.log("Pairs saved successfully:", pairsData);
      } else {
        console.log("No pairs to save, clearing database");
        // Even if there are no pairs, we should still make the API call to clear the database
        const pairsResponse = await fetch("/api/mongodb/pairs", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ pairs: [] }),
        });

        const pairsData = await pairsResponse.json();

        if (!pairsResponse.ok) {
          console.error("Failed to clear pairs:", pairsData);
          setSaveStatus(
            `Error clearing pairs: ${pairsData.error || "Unknown error"}`
          );
          setIsSaving(false);
          return;
        }

        console.log("Database cleared successfully:", pairsData);
      }

      // Save Excel data to database
      const excelData = {
        shortOpenTableData,
        shortLoadedTableData,
        longOpenTableData,
        longLoadedTableData,
        shortClosedTableData,
        longClosedTableData,
      };

      console.log("Saving Excel data to database");
      const excelResponse = await fetch("/api/mongodb/excel", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(excelData),
      });

      const excelResponseData = await excelResponse.json();

      if (!excelResponse.ok) {
        console.error("Failed to save Excel data:", excelResponseData);
        setSaveStatus(
          `Error saving Excel data: ${
            excelResponseData.error || "Unknown error"
          }`
        );
      } else {
        console.log("Excel data saved successfully:", excelResponseData);
        setSaveStatus("All data saved successfully to database");
      }
    } catch (error) {
      console.error("Error saving data to database:", error);
      setSaveStatus(`Error: ${error.message || "Unknown error"}`);
    } finally {
      setIsSaving(false);
    }
  };

  // Function to load pairs from database
  const loadPairsFromDatabase = async () => {
    // Skip if already loading
    if (isLoading) {
      setLoadStatus("Already loading data");
      return;
    }

    setIsLoading(true);
    setLoadStatus("Loading pairs from database...");

    try {
      // Load pairs from database
      const pairsResponse = await fetch("/api/mongodb/pairs", {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!pairsResponse.ok) {
        const errorData = await pairsResponse.json();
        throw new Error(errorData.error || "Failed to load pairs");
      }

      const pairsData = await pairsResponse.json();
      console.log("Loaded pairs from database:", pairsData);

      if (pairsData.pairs && Array.isArray(pairsData.pairs)) {
        // Transform the loaded pairs back to the expected format
        const transformedPairs = pairsData.pairs.map((pair, index) => ({
          id: index + 1,
          key: pair.pairKey || `pair-${index}`,
          longComponent: pair.longComponent || {},
          shortComponent: pair.shortComponent || {},
          combinedPNL: pair.combinedPNL || "0",
          status: pair.status || "",
        }));

        setPairArray(transformedPairs);
        setLoadStatus(
          `Successfully loaded ${transformedPairs.length} pairs from database`
        );
        console.log("Pairs loaded and set:", transformedPairs.length);
      } else {
        setPairArray([]);
        setLoadStatus("No pairs found in database");
      }

      // Clear status after 3 seconds
      setTimeout(() => setLoadStatus(""), 3000);
    } catch (error) {
      console.error("Error loading pairs from database:", error);
      setLoadStatus(`Error: ${error.message || "Unknown error"}`);
      setTimeout(() => setLoadStatus(""), 5000);
    } finally {
      setIsLoading(false);
    }
  };

  // Function to clear all user data
  const clearAllUserData = () => {
    console.log("Clearing all pair array data for user logout");
    setPairArray([]);
    setPairStatus({});
    setSaveStatus("");
    setIsSaving(false);
    setLoadStatus("");
    setIsLoading(false);

    // Clear localStorage
    if (typeof window !== "undefined") {
      localStorage.removeItem("pairArray");
      localStorage.removeItem("pairStatus");
    }
  };

  return (
    <PairArrayContext.Provider
      value={{
        pairArray,
        setPairArray,
        pairStatus,
        setPairStatus,
        savePairsToDatabase,
        isSaving,
        saveStatus,
        loadPairsFromDatabase,
        isLoading,
        loadStatus,
        clearAllUserData,
      }}
    >
      {children}
    </PairArrayContext.Provider>
  );
}

export function usePairArray() {
  return useContext(PairArrayContext);
}
