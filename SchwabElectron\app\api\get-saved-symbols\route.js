import { NextResponse } from "next/server";
import {
  retrieveAccessToken,
  retrieveCustomerId,
  retrieveCorrelId,
} from "@/actions/schwabAccess";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth-options";
import { getServerUrl } from "@/utils/serverConfig";

export async function GET() {
  try {
    // In development, ignore SSL certificate errors for self-signed certs
    if (process.env.NODE_ENV === "development") {
      process.env.NODE_TLS_REJECT_UNAUTHORIZED = "0";
    }

    // Get NextAuth session for user email
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: "User not authenticated" },
        { status: 401 }
      );
    }

    // Get auth data - skip refresh to avoid unnecessary token refresh attempts
    const accessToken = await retrieveAccessToken(true); // skipRefresh = true
    const customerId = await retrieveCustomerId();
    const correlId = await retrieveCorrelId();

    if (!accessToken || !customerId || !correlId) {
      console.log(
        "Missing Schwab authentication data - user may not be signed in to Schwab"
      );
      return NextResponse.json(
        { error: "Not authenticated with Schwab" },
        { status: 401 }
      );
    }

    // Call server endpoint to get saved symbols
    const serverUrl = getServerUrl();
    const response = await fetch(`${serverUrl}/api/get-user-symbols`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "x-client-customerid": customerId,
        "x-client-correlid": correlId,
        "x-user-email": session.user.email,
        authorization: `Bearer ${accessToken}`,
      },
    });

    if (!response.ok) {
      return NextResponse.json(
        { error: "Failed to get saved symbols" },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error("Error getting saved symbols:", error);
    return NextResponse.json(
      { error: "Failed to get saved symbols" },
      { status: 500 }
    );
  }
}
