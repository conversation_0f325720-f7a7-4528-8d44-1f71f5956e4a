export async function getAccounts() {
  console.log("*** API TEST CALL: ACCOUNTS ***");

  const res = await axios({
    method: "GET",
    url: "https://api.schwabapi.com/trader/v1/accounts?fields=positions",
    contentType: "application/json",
    headers: {
      "Accept-Encoding": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  });

  console.log(res.data);
}

export async function getAccountBalance() {
  return "/accounts/balance";
}

export async function getPositions() {
  return "/accounts/positions";
}

export async function getMarketData(symbol) {
  return `/market-data/${symbol}`;
}

export async function placeTrade(order) {
  return (
    "/trading/orders",
    {
      method: "POST",
      body: JSON.stringify(order),
    }
  );
}
