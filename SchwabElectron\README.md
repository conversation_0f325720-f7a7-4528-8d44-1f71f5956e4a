# Schwab Dashboard

**Description:**
This is a Next.js project built with Tailwind CSS and Prisma, designed to interact with the Schwab API for account and market data. The application features secure authentication, robust admin/user management, and real-time data streaming via WebSockets.

## 🚀 Quick Setup (Recommended)

For new developers or fresh installations, use our automated setup script:

```bash
# Clone the repository
git clone https://github.com/SavZappone/SchwabDashboardProject.git
cd SchwabDashboardProject

# Run the automated setup (this does everything for you!)
npm run setup
```

The setup script will:

- Install all dependencies
- Generate SSL certificates for HTTPS
- Set up the database schema (npx prisma generate)
- Create an admin user (commented out)
- Provide next steps

After setup completes, start the development server:

```bash
npm run dev
```

Then open [https://localhost:3002](https://localhost:3002) in your browser.

## 📋 Manual Setup (Advanced Users)

If you prefer to set up manually or the automated setup fails:

### Prerequisites

- **Node.js** (v18 or higher)
- **npm** or **yarn**
- **OpenSSL** (for certificate generation)
- **Database** (MongoDB connection string)

### Step-by-Step Setup

1. **Clone and Install**

   ```bash
   git clone https://github.com/SavZappone/SchwabDashboardProject.git
   cd SchwabDashboardProject
   npm install
   ```

2. **Environment Configuration**

   Copy the example environment file and configure it:

   ```bash
   cp .env.example .env
   ```

   Then edit `.env` with your actual values:

   - **NEXTAUTH_SECRET**: Generate with `openssl rand -base64 32`
   - **DATABASE_URL**: Your MongoDB connection string
   - **SCHWAB API keys**: From your Schwab Developer Account
   - **ACCOUNT_NUMBER**: Your encrypted Schwab account number

3. **Generate SSL Certificates**

   HTTPS is required for Schwab API integration:

   ```bash
   npm run generate-certs
   ```

   This creates self-signed certificates in the `certificates/` folder.

4. **Database Setup**

   ```bash
   npx prisma generate
   npx prisma db push
   ```

5. **Create Admin User**

   ```bash
   npm run create-admin
   ```

   Default credentials: `<EMAIL>` / `admin123`

6. **Start Development Server**
   ```bash
   npm run dev          # Full app with Electron
   npm run dev-web      # Web-only (no Electron)
   ```

### 🔧 Certificate Troubleshooting

If you're having certificate issues:

**Windows:**

```bash
# Install OpenSSL from: https://slproweb.com/products/Win32OpenSSL.html
# Then run:
npm run generate-certs
```

**macOS:**

```bash
brew install openssl
npm run generate-certs
```

**Linux:**

```bash
sudo apt-get install openssl  # Ubuntu/Debian
# or
sudo yum install openssl       # CentOS/RHEL
npm run generate-certs
```

**Manual Certificate Copy:**
If you have certificates from another machine, copy them to:

- `certificates/localhost.pem` (certificate file)
- `certificates/localhost-key.pem` (private key file)

## 📜 Available Scripts

| Script                   | Description                                                          |
| ------------------------ | -------------------------------------------------------------------- |
| `npm run setup`          | **Automated setup** - Installs everything and configures the project |
| `npm run dev`            | Start development server with Electron desktop app                   |
| `npm run dev-web`        | Start development server (web-only, no Electron)                     |
| `npm run build`          | Build desktop application                                            |
| `npm run build-web`      | Build web application only                                           |
| `npm run generate-certs` | Generate SSL certificates for HTTPS                                  |
| `npm run create-admin`   | Create admin user account                                            |
| `npm run server`         | Start the WebSocket server separately                                |
| `npm run lint`           | Run code linting                                                     |

## 🌐 Accessing the Application

After starting the development server:

1. **Web Browser**: Open [https://localhost:3002](https://localhost:3002)
2. **SSL Warning**: Click "Advanced" → "Proceed to localhost" (normal for self-signed certificates)
3. **Login**: Use `<EMAIL>` / `admin123` (change password after first login!)

## 🔧 Common Issues & Solutions

### "Cannot find SSL certificates"

```bash
npm run generate-certs
```

### "Database connection failed"

- Check your `DATABASE_URL` in `.env`
- Ensure your MongoDB cluster is accessible
- Verify username/password are correct

### "Admin login doesn't work"

```bash
npm run create-admin
```

### "Port already in use"

- Kill processes on ports 3000, 3001, 3002
- Or change ports in the configuration

### "OpenSSL not found"

- **Windows**: Install from [OpenSSL website](https://slproweb.com/products/Win32OpenSSL.html)
- **macOS**: `brew install openssl`
- **Linux**: `sudo apt-get install openssl`

## 🏗️ Project Structure

```
├── app/                    # Next.js app directory
│   ├── api/               # API routes
│   ├── Strategies/        # Trading strategy components
│   └── ...
├── certificates/          # SSL certificates (auto-generated)
├── components/            # Reusable React components
├── lib/                   # Utility libraries
├── prisma/               # Database schema
├── scripts/              # Setup and utility scripts
├── server.js             # WebSocket server
├── .env                  # Environment variables (create from .env.example)
└── README.md             # This file
```

**WebSocket Server for Schwab Streaming:**

- The file `server.js` is an Express/Socket.io server that manages the WebSocket connection to Schwab’s streaming API and relays real-time data to the dashboard.
- You must run this server separately from the Next.js app:
  1. In a new terminal, run:  
     `node server.js`
  2. The server will start on [https://localhost:3001](https://localhost:3001) and handle WebSocket and REST endpoints for Schwab data.
- The dashboard will automatically connect to this server for live updates.

**Build and Deployment:**

**For Web Deployment:**

1. To build the Next.js project for web deployment, run:  
   `npm run build` or `yarn build`
2. To deploy, use a platform like Vercel, Netlify, or your preferred Node.js hosting for both the Next.js app and the `server.js` backend.

**For Desktop Application (Electron):**

1. Update Electron dependencies (if needed):

   ```
   npm update electron-builder
   npm install --save-dev node-abi@latest
   ```

2. Build the desktop executable:  
   `npm run build`

   This command will:

   - Build the Next.js application (`next build`)
   - Package it into an Electron executable (`electron-builder`)

3. Locate the executable:

   - After the build completes, the executable will be located in the `dist` folder
   - For Windows: `dist/nextjs-trading-app Setup [version].exe`
   - For macOS: `dist/nextjs-trading-app-[version].dmg`
   - For Linux: `dist/nextjs-trading-app-[version].AppImage`

4. Install and run the application:

   - **Windows**: Double-click the `.exe` file to install, then launch from Start Menu or Desktop
   - **macOS**: Open the `.dmg` file, drag the app to Applications folder, then launch
   - **Linux**: Make the `.AppImage` executable (`chmod +x nextjs-trading-app-[version].AppImage`) and run it

5. The desktop application will automatically start both the Next.js frontend and the required backend services.

**Troubleshooting Build Issues:**

**Windows Symbolic Link Error:**
If you encounter the error "Cannot create symbolic link: A required privilege is not held by the client", try these solutions:

1. **Run as Administrator** (Recommended):

   - Open Command Prompt or PowerShell as Administrator
   - Navigate to your project directory
   - Run `npm run build`

2. **Enable Developer Mode** (Alternative):

   - Open Windows Settings → Update & Security → For developers
   - Enable "Developer Mode"
   - Restart your terminal and try building again

3. **Clear electron-builder cache**:

   ```bash
   npx electron-builder install-app-deps
   npm run build
   ```

4. **If issues persist, try building without code signing**:
   - Add `"win": {"sign": false}` to the build configuration in package.json temporarily

**Application Window Not Appearing:**
If the .exe runs in Task Manager but no window appears, try these solutions:

1. **Check if the application is running in the background**:

   - Look in the system tray (bottom-right corner near the clock)
   - Right-click on the app icon if present and select "Show" or "Open"

2. **Force the window to appear**:

   - Press `Alt + Tab` to cycle through open windows
   - The application window might be minimized or behind other windows

3. **Run from Command Line for debugging**:

   - Open Command Prompt
   - Navigate to where the .exe is installed (usually `C:\Users\<USER>\AppData\Local\Programs\nextjs-trading-app\`)
   - Run: `"nextjs-trading-app.exe"`
   - Use command: start nextjs-trading-app.exe
   - Check for any error messages in the console

4. **Check Windows Display Settings**:

   - The window might be opening on a disconnected monitor
   - Right-click desktop → Display settings → Make sure "Extend" or "Duplicate" is not selected if you only have one monitor

5. **Clear Application Data** (if previously installed):

   - Press `Win + R`, type `%appdata%` and press Enter
   - Look for a folder named after your app and delete it
   - Reinstall the application

6. **Antivirus/Windows Defender**:
   - Check if your antivirus is blocking the application
   - Add the application to your antivirus whitelist

**Prisma Import Error (CommonJS/ES Modules):**
If you see an error like "Named export 'PrismaClient' not found" when running the executable:

1. **This is a known issue with Prisma in packaged Electron apps**
2. **The fix has been applied to the codebase** - rebuild the application:
   ```bash
   npm run build
   ```
3. **If the error persists**, ensure all Prisma imports in your code use the CommonJS-compatible format:
   ```javascript
   // Instead of: import { PrismaClient } from '@prisma/client';
   // Use this:
   import pkg from "@prisma/client";
   const { PrismaClient } = pkg;
   ```

**Security & Best Practices:**

- All authentication and session cookies are set to Secure, HttpOnly, and SameSite=Strict.
- User self-registration is disabled; user management is admin-only.
- Strong HTTP security headers are enforced in both Next.js and Express servers.
- Rate limiting is applied to sensitive endpoints (e.g., login).
- Sensitive tokens are never stored in localStorage.
