import Link from "next/link";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TooltipTrigger } from "@/components/ui/tooltip";
import { usePathname } from "next/navigation";

const ACTIVE_COLOR = "text-blue-400 dark:text-blue-400 stroke-blue-400 dark:stroke-blue-400";
const INACTIVE_COLOR = "text-neutral-400 dark:text-neutral-300 stroke-neutral-400 dark:stroke-neutral-300";

const NavigationLink = ({ children, name, showName, href, onClick }) => {
  const pathname = usePathname();
  // Consider both exact and startsWith for subpages
  const isActive = pathname === href || (href !== "/" && pathname.startsWith(href));
  const colorClass = isActive ? ACTIVE_COLOR : INACTIVE_COLOR;

  return (
    <TooltipProvider delayDuration={20}>
      <Tooltip>
        <TooltipTrigger asChild>
          <Link
            href={href}
            className={`flex p-1 rounded cursor-pointer stroke-[0.75] hover:stroke-neutral-100 dark:hover:stroke-white place-items-center gap-3 hover:bg-neutral-700/30 dark:hover:bg-neutral-600/30 transition-colors duration-100 ${colorClass}`}
            onClick={onClick}
          >
            {/* Clone the icon and apply colorClass to icon as well */}
            {children && typeof children.type === "function"
              ? children.type({ ...children.props, className: `${children.props.className || ''} ${colorClass}` })
              : children}
            {showName && (
              <p className={`font-poppins overflow-clip whitespace-nowrap tracking-wide ${colorClass}`}>
                {name}
              </p>
            )}
          </Link>
        </TooltipTrigger>
        {!showName && (
          <TooltipContent side="right">
            <p>{name}</p>
          </TooltipContent>
        )}
      </Tooltip>
    </TooltipProvider>
  );
};

export default NavigationLink;
