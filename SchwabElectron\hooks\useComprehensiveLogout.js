/**
 * Comprehensive Logout Hook
 * Provides a consistent logout function across the application
 */

import { useSession, signOut } from "next-auth/react";
import { useMarketData } from "../app/testingWebsocket/MarketDataContext";
import { useExcelData } from "../app/Strategies/WB/ExcelDataContext";
import { usePairArray } from "../app/pairArray/PairArray";
import { useEndpointAppContext } from "../components/EndpointAppContext";
import { clearAllUserData } from "../utils/userDataCleaner";

export const useComprehensiveLogout = () => {
  const { data: session } = useSession();

  // Get clear functions from all contexts
  const { clearAllUserData: clearMarketData } = useMarketData() || {};
  const { clearAllUserData: clearExcelData } = useExcelData() || {};
  const { clearAllUserData: clearPairData } = usePairArray() || {};
  const { clearAllUserData: clearEndpointData } = useEndpointAppContext() || {};

  const performLogout = async (redirectUrl = "/generalLogin") => {
    try {
      console.log("🚪 Starting comprehensive logout process...");

      // 1. Clear all user data from contexts
      console.log("Clearing context data...");
      if (clearMarketData) clearMarketData();
      if (clearExcelData) clearExcelData();
      if (clearPairData) clearPairData();
      if (clearEndpointData) clearEndpointData();

      // 2. Call the full logout API to clear all server-side cookies
      console.log("Clearing server-side cookies...");
      try {
        await fetch("/api/full-logout", {
          method: "POST",
          credentials: "include",
        });
      } catch (apiError) {
        console.warn(
          "API logout failed, continuing with client-side cleanup:",
          apiError
        );
      }

      // 3. Use comprehensive data cleaner
      console.log("Running comprehensive data cleanup...");
      await clearAllUserData();

      console.log("✅ All user data cleared, signing out...");

      // 4. Sign out from NextAuth with proper URL
      const fullRedirectUrl =
        typeof window !== "undefined"
          ? `${window.location.origin}${redirectUrl}`
          : redirectUrl;
      console.log("Redirecting to:", fullRedirectUrl);

      await signOut({
        callbackUrl: fullRedirectUrl,
        redirect: true,
      });

      return { success: true };
    } catch (error) {
      console.error("❌ Error during logout:", error);

      // Still try to logout even if there are errors
      try {
        const fullRedirectUrl =
          typeof window !== "undefined"
            ? `${window.location.origin}${redirectUrl}`
            : redirectUrl;
        await signOut({
          callbackUrl: fullRedirectUrl,
          redirect: true,
        });
      } catch (signOutError) {
        console.error("Failed to sign out:", signOutError);
        // Force redirect as last resort
        if (typeof window !== "undefined") {
          window.location.href = redirectUrl;
        }
      }

      return { success: false, error: error.message };
    }
  };

  const performSchwabLogout = async () => {
    try {
      console.log("🔌 Disconnecting from Schwab...");

      // Call the Schwab logout API
      const response = await fetch("/api/schwab-logout", {
        method: "POST",
        credentials: "include",
      });

      const result = await response.json();

      if (result.success) {
        // Clear Schwab-related localStorage
        if (typeof window !== "undefined") {
          localStorage.removeItem("schwabLoggedIn");
          localStorage.removeItem("schwabLoginAttempt");
        }

        // Disconnect from WebSocket if available
        if (clearMarketData) {
          clearMarketData();
        }

        console.log("✅ Successfully disconnected from Schwab");
        return { success: true };
      } else {
        console.error("Schwab logout API failed:", result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error("❌ Error during Schwab logout:", error);
      return { success: false, error: error.message };
    }
  };

  return {
    performLogout,
    performSchwabLogout,
    isLoggedIn: !!session,
  };
};
