"use client";
import { createContext, useContext, useState, useEffect } from "react";
import { useExcelData } from "../Strategies/WB/ExcelDataContext";
import { useMarketData } from "../testingWebsocket/MarketDataContext";
import { useEndpointAppContext } from "../../components/EndpointAppContext";

// Funzione semplice per formattare la data come MM/DD
function formatDate(dateString) {
  if (!dateString) return "";
  try {
    const date = new Date(dateString);
    const month = date.getMonth() + 1; // getMonth() restituisce 0-11
    const day = date.getDate();
    return `${month}/${day}`;
  } catch (error) {
    return dateString; // In caso di errore, restituisci la stringa originale
  }
}

class ComponentArray {
  constructor() {
    this.longComponents = [];
    this.shortComponents = [];
  }

  addLong(component) {
    this.longComponents.push(component);
  }

  addShort(component) {
    this.shortComponents.push(component);
  }

  getLong() {
    return this.longComponents;
  }

  getShort() {
    return this.shortComponents;
  }
  reorderLong(newOrder) {
    this.longComponents = newOrder.map((i) => this.longComponents[i]); //don't use this anymore, using tableData to reorder
  }

  reorderShort(newOrder) {
    this.shortComponents = newOrder.map((i) => this.shortComponents[i]); //don't use this anymore, using tableData to reorder
  }
}

const ComponentArrayContext = createContext(null);

export function ComponentArrayProvider({ children }) {
  const {
    shortLoadedTableData,
    longLoadedTableData,
    shortOpenTableData,
    longOpenTableData,
    shortClosedTableData,
    longClosedTableData,
    setShortLoadedTableData,
    setLongLoadedTableData,
    setLongOpenTableData,
    setShortOpenTableData,
  } = useExcelData();
  const { filteredData } = useMarketData();
  const { accountData } = useEndpointAppContext();
  const [componentArray, setComponentArray] = useState(null);
  const calculateComponent = (row) => {
    // Short ticker in row[0]
    const ticker = row.ticker;
    //COST
    const costValue =
      accountData
        .find((account) =>
          account?.securitiesAccount?.positions?.some(
            (position) => position.instrument.symbol === ticker
          )
        )
        ?.securitiesAccount.positions.find(
          (position) => position.instrument.symbol === ticker
        )?.averagePrice || 0;
    const costAmount = parseFloat(costValue);
    const formattedCost = costAmount.toFixed(2);

    //AMT
    const amtValue =
      accountData
        .flatMap((accountdata) => accountdata)
        .find((account) =>
          account?.securitiesAccount?.positions?.some(
            (position) => position.instrument.symbol === ticker
          )
        )
        ?.securitiesAccount.positions.find(
          (position) => position.instrument.symbol === ticker
        )?.shortQuantity || 0;
    const amountAmt = parseFloat(amtValue);
    const formattedAmt = new Intl.NumberFormat("en-US", {
      style: "decimal",
      minimumIntegerDigits: 1,
      maximumFractionDigits: 0,
    }).format(amountAmt);

    //DOLLAR COST
    const dollarCostValue = formattedCost * formattedAmt;
    const dollarCostAmount = parseFloat(dollarCostValue);

    const dollarCost = new Intl.NumberFormat("en-US", {
      style: "decimal",
      minimumIntegerDigits: 1,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(dollarCostAmount);
    //BID
    const bidValue = filteredData[ticker]?.bid_prc
      ? filteredData[ticker]?.bid_prc
      : 0;

    const bidAmount = parseFloat(bidValue);

    const formattedBid = new Intl.NumberFormat("en-US", {
      style: "decimal",
      minimumIntegerDigits: 1,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(bidAmount);

    //ASK
    const askValue = filteredData[ticker]?.ask_prc
      ? filteredData[ticker]?.ask_prc
      : 0;

    const formattedAsk = new Intl.NumberFormat("en-US", {
      style: "decimal",
      minimumIntegerDigits: 1,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(askValue);

    //LAST
    const lastValue =
      filteredData[ticker] && filteredData[ticker].last_prc
        ? filteredData[ticker].last_prc
        : 0;

    const lastAmount = parseFloat(lastValue);

    const formattedLast = new Intl.NumberFormat("en-US", {
      style: "decimal",
      minimumIntegerDigits: 1,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(lastAmount);

    //CHANGE
    const changeValue =
      filteredData[ticker] && filteredData[ticker].change
        ? filteredData[ticker].change
        : 0;

    const changeAmount = parseFloat(changeValue);

    const formattedChange = new Intl.NumberFormat("en-US", {
      style: "percent",
      minimumFractionDigits: 1,
      maximumFractionDigits: 1,
    }).format(changeAmount / 100);

    //EXPECTEDQUANTITY
    const expectedQuantity = row.shares ? row.shares : 0;

    //VOLUME
    const volumeValue =
      filteredData[ticker] && filteredData[ticker].volume
        ? filteredData[ticker].volume
        : 0;
    const volumeAmount = parseFloat(volumeValue);

    const formattedVolume = new Intl.NumberFormat("en-US", {
      style: "decimal",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(volumeAmount / 1000);

    //VOLUME FROM INPUT
    const loadedVolumeValue = row.volume ? row.volume : 0;
    const loadedVolumeAmount = parseFloat(loadedVolumeValue);

    const formattedLoadedVolume = new Intl.NumberFormat("en-US", {
      style: "decimal",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(loadedVolumeAmount / 1000);
    //DIVIDEND
    const dividendValue =
      filteredData[ticker] && filteredData[ticker].dividend
        ? filteredData[ticker].dividend
        : 0;
    const dividendAmount = parseFloat(dividendValue);

    const formattedDividend = new Intl.NumberFormat("en-US", {
      style: "decimal",
      currency: "USD",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(dividendAmount);
    //DIVIDEND FROM USER
    const dividendUserValue = row.dividend ? row.dividend : 0; //we will have to add this from user input, for now lets leave it like this
    const dividendUserAmount = parseFloat(dividendUserValue);
    const formattedUserDividend = new Intl.NumberFormat("en-US", {
      style: "decimal",
      currency: "USD",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(dividendUserAmount);

    //EXDATE
    const exDateValue =
      filteredData[ticker] && filteredData[ticker].ex_div_date
        ? formatDate(filteredData[ticker].ex_div_date)
        : "";

    //PNL - FIXED: Short PNL = (cost - ask) * shares
    const pnlValue =
      (parseFloat(formattedCost) - parseFloat(askValue)) *
      parseFloat(formattedAmt);
    const formattedPnl = Math.round(pnlValue);

    //SPREAD
    const spreadValue = formattedBid - formattedAsk;
    const spreadAmount = parseFloat(spreadValue);
    const formattedSpread = new Intl.NumberFormat("en-US", {
      style: "decimal",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(spreadAmount);

    //SPREAD FROM USER
    const spreadUserValue = row.spread ? row.spread : 0;
    const spreadUserAmount = parseFloat(spreadUserValue);
    const formattedSpreadUser = new Intl.NumberFormat("en-US", {
      style: "decimal",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(spreadUserAmount);
    //SECTOR
    const sectorValue = row.sector ? row.sector : "";
    //STATUS
    const statusValue = row.status ? row.status : "WB_LoadedPairs";

    //ID
    const id = row.id ? row.id : "";

    return {
      ticker,
      formattedLast,
      formattedCost,
      formattedAmt,
      formattedPnl,
      formattedChange,
      formattedVolume,
      formattedLoadedVolume,
      formattedDividend,
      formattedUserDividend,
      formattedSpreadUser,
      expectedQuantity,
      formattedBid,
      formattedAsk,
      dollarCost,
      formattedSpread,
      sectorValue,
      dividendUserValue,
      spreadUserValue,
      id,
      statusValue,
      exDateValue,
    };
  };
  const calculateLongComponent = (row) => {
    // Short ticker in row[0]
    const ticker = row.ticker;
    //COST
    const costValue =
      accountData
        .find((account) =>
          account?.securitiesAccount?.positions?.some(
            (position) => position.instrument.symbol === ticker
          )
        )
        ?.securitiesAccount.positions.find(
          (position) => position.instrument.symbol === ticker
        )?.averagePrice || 0;
    const costAmount = parseFloat(costValue);
    const formattedCost = costAmount.toFixed(2);

    //AMT
    const amtValue =
      accountData
        .flatMap((accountdata) => accountdata)
        .find((account) =>
          account?.securitiesAccount?.positions?.some(
            (position) => position.instrument.symbol === ticker
          )
        )
        ?.securitiesAccount.positions.find(
          (position) => position.instrument.symbol === ticker
        )?.longQuantity || 0;
    const amountAmt = parseFloat(amtValue);
    const formattedAmt = new Intl.NumberFormat("en-US", {
      style: "decimal",
      minimumIntegerDigits: 1,
      maximumFractionDigits: 0,
    }).format(amountAmt);

    //DOLLAR COST
    const dollarCostValue = formattedCost * formattedAmt;
    const dollarCostAmount = parseFloat(dollarCostValue);

    const dollarCost = new Intl.NumberFormat("en-US", {
      style: "decimal",
      minimumIntegerDigits: 1,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(dollarCostAmount);

    //BID
    const bidValue = filteredData[ticker]?.bid_prc
      ? filteredData[ticker]?.bid_prc
      : 0;

    const bidAmount = parseFloat(bidValue);

    const formattedBid = new Intl.NumberFormat("en-US", {
      style: "decimal",
      minimumIntegerDigits: 1,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(bidAmount);

    //ASK
    const askValue = filteredData[ticker]?.ask_prc
      ? filteredData[ticker]?.ask_prc
      : 0;

    const formattedAsk = new Intl.NumberFormat("en-US", {
      style: "decimal",
      minimumIntegerDigits: 1,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(askValue);

    //LAST
    const lastValue =
      filteredData[ticker] && filteredData[ticker].last_prc
        ? filteredData[ticker].last_prc
        : 0;

    const lastAmount = parseFloat(lastValue);

    const formattedLast = new Intl.NumberFormat("en-US", {
      style: "decimal",
      minimumIntegerDigits: 1,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(lastAmount);

    //CHANGE
    const changeValue =
      filteredData[ticker] && filteredData[ticker].change
        ? filteredData[ticker].change
        : 0;

    const changeAmount = parseFloat(changeValue);

    const formattedChange = new Intl.NumberFormat("en-US", {
      style: "percent",
      minimumFractionDigits: 1,
      maximumFractionDigits: 1,
    }).format(changeAmount / 100);

    //EXPECTEDQUANTITY
    const expectedQuantity = row.shares ? row.shares : 0;

    //VOLUME
    const volumeValue =
      filteredData[ticker] && filteredData[ticker].volume
        ? filteredData[ticker].volume
        : 0;
    const volumeAmount = parseFloat(volumeValue);

    const formattedVolume = new Intl.NumberFormat("en-US", {
      style: "decimal",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(volumeAmount / 1000);

    //VOLUME FROM INPUT
    const loadedVolumeValue = row.volume ? row.volume : 0;
    const loadedVolumeAmount = parseFloat(loadedVolumeValue);

    const formattedLoadedVolume = new Intl.NumberFormat("en-US", {
      style: "decimal",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(loadedVolumeAmount / 1000);
    //DIVIDEND
    const dividendValue =
      filteredData[ticker] && filteredData[ticker].dividend
        ? filteredData[ticker].dividend
        : 0;
    const dividendAmount = parseFloat(dividendValue);

    const formattedDividend = new Intl.NumberFormat("en-US", {
      style: "decimal",
      currency: "USD",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(dividendAmount);
    //DIVIDEND FROM USER
    const dividendUserValue = row.dividend ? row.dividend : 0; //we will have to add this from user input, for now lets leave it like this
    const dividendUserAmount = parseFloat(dividendUserValue);
    const formattedUserDividend = new Intl.NumberFormat("en-US", {
      style: "decimal",
      currency: "USD",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(dividendUserAmount);
    //EXDATE
    const exDateValue =
      filteredData[ticker] && filteredData[ticker].ex_div_date
        ? formatDate(filteredData[ticker].ex_div_date)
        : "";
    //PNL - FIXED: Long PNL = (bid - cost) * shares
    const pnlValue =
      (parseFloat(bidValue) - parseFloat(formattedCost)) *
      parseFloat(formattedAmt);
    const formattedPnl = Math.round(pnlValue);

    //SPREAD
    const spreadValue = formattedBid - formattedAsk;
    const spreadAmount = parseFloat(spreadValue);
    const formattedSpread = new Intl.NumberFormat("en-US", {
      style: "decimal",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(spreadAmount);

    //SPREAD FROM USER
    const spreadUserValue = row.spread ? row.spread : 0;
    const spreadUserAmount = parseFloat(spreadUserValue);
    const formattedSpreadUser = new Intl.NumberFormat("en-US", {
      style: "decimal",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(spreadUserAmount);
    //SECTOR
    const sectorValue = row.sector ? row.sector : "";

    //STATUS
    const statusValue = row.status ? row.status : "WB_LoadedPairs";
    //ID
    const id = row.id ? row.id : "";

    return {
      ticker,
      formattedLast,
      formattedCost,
      formattedAmt,
      formattedPnl,
      formattedChange,
      formattedVolume,
      formattedLoadedVolume,
      formattedDividend,
      formattedUserDividend,
      formattedSpreadUser,
      expectedQuantity,
      formattedBid,
      formattedAsk,
      dollarCost,
      formattedSpread,
      sectorValue,
      dividendUserValue,
      spreadUserValue,
      statusValue,
      id,
      exDateValue,
    };
  };
  useEffect(() => {
    if (
      accountData &&
      Array.isArray(shortLoadedTableData) &&
      Array.isArray(longLoadedTableData) &&
      Array.isArray(shortOpenTableData) &&
      Array.isArray(longOpenTableData) &&
      Array.isArray(shortClosedTableData) &&
      Array.isArray(longClosedTableData)
    ) {
      const newComponentArray = new ComponentArray();
      shortLoadedTableData.forEach((row) => {
        const shortComponent = calculateComponent(row);
        newComponentArray.addShort(shortComponent);
      });
      longLoadedTableData.forEach((row) => {
        const longComponent = calculateLongComponent(row);
        newComponentArray.addLong(longComponent);
      });
      shortOpenTableData.forEach((row) => {
        const shortComponent = calculateComponent(row);
        newComponentArray.addShort(shortComponent);
      });
      longOpenTableData.forEach((row) => {
        const longComponent = calculateLongComponent(row);
        newComponentArray.addLong(longComponent);
      });
      shortClosedTableData.forEach((row) => {
        const shortComponent = calculateComponent(row);
        newComponentArray.addShort(shortComponent);
      });
      longClosedTableData.forEach((row) => {
        const longComponent = calculateLongComponent(row);
        newComponentArray.addLong(longComponent);
      });

      setComponentArray(newComponentArray);
    }
  }, [
    longLoadedTableData,
    shortLoadedTableData,
    shortOpenTableData,
    longOpenTableData,
    accountData,
    filteredData,
  ]);

  /*const updateComponentOrder = (newLongOrder, newShortOrder) => {
    if (componentArray) {
      const newCompArray = new ComponentArray();
      newCompArray.longComponents = newLongOrder.map((i) => componentArray.longComponents[i]);
      newCompArray.shortComponents = newShortOrder.map((i) => componentArray.shortComponents[i]);  NOT USING ANYMORE
      setComponentArray(newCompArray);
    }
  }; */

  return (
    <ComponentArrayContext.Provider
      value={{ componentArray, setComponentArray }}
    >
      {children}
    </ComponentArrayContext.Provider>
  );
}

export function useComponentArray() {
  return useContext(ComponentArrayContext);
}
