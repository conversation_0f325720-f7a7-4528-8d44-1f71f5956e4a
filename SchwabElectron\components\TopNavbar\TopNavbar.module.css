/* TopNavbar Custom Styles */

.navbar {
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  background: rgba(17, 24, 39, 0.95);
  border-bottom: 1px solid rgba(75, 85, 99, 0.3);
}

.navItem {
  position: relative;
  overflow: hidden;
}

.navItem::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

.navItem:hover::before {
  left: 100%;
}

.activeIndicator {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.dropdown {
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  background: rgba(31, 41, 55, 0.95);
  border: 1px solid rgba(75, 85, 99, 0.3);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
}

.logo {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);
}

.profileButton {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1));
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.profileButton:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(139, 92, 246, 0.2));
  border-color: rgba(59, 130, 246, 0.4);
}

.minimizeButton {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.minimizeButton:hover {
  transform: scale(1.05);
  background: rgba(75, 85, 99, 0.3);
}

/* Smooth animations */
.fadeIn {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slideDown {
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .navbar {
    padding: 0 1rem;
  }
  
  .navItem {
    padding: 0.5rem 0.75rem;
  }
}

@media (max-width: 768px) {
  .navbar {
    padding: 0 0.5rem;
  }
  
  .navItem {
    padding: 0.5rem;
  }
  
  .dropdown {
    left: 50% !important;
    transform: translateX(-50%);
    width: 90vw;
    max-width: 300px;
  }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  .navbar {
    background: rgba(17, 24, 39, 0.98);
    border-bottom-color: rgba(75, 85, 99, 0.4);
  }
  
  .dropdown {
    background: rgba(31, 41, 55, 0.98);
    border-color: rgba(75, 85, 99, 0.4);
  }
}

/* Accessibility improvements */
.navItem:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.minimizeButton:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Loading states */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  border: 2px solid transparent;
  border-top-color: #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
