
class Observer {
    constructor() {
      this.subscribers = [];
    }
  
    subscribe(callback) {
      this.subscribers.push(callback);
    }
  
    unsubscribe(callback) {
      this.subscribers = this.subscribers.filter(sub => sub !== callback);
    }
  
    notify(data) {
      this.subscribers.forEach(sub => sub(data));
    }
}
  
export const accountObserver = new Observer();
export const orderObserver = new Observer();
export const transactionObserver = new Observer();
  