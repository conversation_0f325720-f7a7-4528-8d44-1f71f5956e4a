"use client";

import { Card } from "@/components/ui/card";

export function PositionsCard({ positions }) {
  return (
    <Card className="p-6 dark:bg-gray-800 dark:border-gray-700">
      <h2 className="text-xl font-semibold mb-4 dark:text-gray-100">Positions</h2>
      {positions.length > 0 ? (
        <div className="space-y-4">
          {positions.map((position) => (
            <div
              key={position.symbol}
              className="flex justify-between items-center"
            >
              <div>
                <h3 className="font-medium dark:text-gray-200">{position.symbol}</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {position.quantity} shares
                </p>
              </div>
              <div className="text-right">
                <p className="font-medium dark:text-gray-200">
                  ${position.marketValue.toLocaleString()}
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  ${position.currentPrice}
                </p>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <p className="text-gray-500 dark:text-gray-400">No positions found</p>
      )}
    </Card>
  );
}
