#!/usr/bin/env node

/**
 * Development Setup Script
 * Automates the complete setup process for new developers
 */

import { execSync } from "child_process";
import { existsSync } from "fs";
import { join, dirname } from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, "..");

console.log("🚀 Setting up development environment...\n");

function runCommand(command, description) {
  console.log(`📋 ${description}...`);
  try {
    execSync(command, { stdio: "inherit", cwd: projectRoot });
    console.log(`✅ ${description} completed\n`);
  } catch (error) {
    console.error(`❌ ${description} failed:`, error.message);
    process.exit(1);
  }
}

function checkFile(filePath, description) {
  if (existsSync(filePath)) {
    console.log(`✅ ${description} exists`);
    return true;
  } else {
    console.log(`❌ ${description} missing`);
    return false;
  }
}

async function setupDevelopment() {
  console.log("🔍 Checking prerequisites...\n");

  // Check if .env file exists
  const envPath = join(projectRoot, ".env");
  if (!checkFile(envPath, ".env file")) {
    console.log(
      "📝 Please create a .env file with the required environment variables."
    );
    console.log("   See .env.example or README.md for required variables.\n");
    process.exit(1);
  }

  // Install dependencies
  runCommand("npm install", "Installing dependencies");

  // Generate SSL certificates
  runCommand("node scripts/generate-certs.js", "Generating SSL certificates");

  // Generate Prisma client
  runCommand("npx prisma generate", "Generating Prisma client");

  // Push database schema (for development)
  // try {
  //   console.log("📋 Setting up database schema...");
  //   execSync("npx prisma db push", { stdio: "inherit", cwd: projectRoot });
  //   console.log("✅ Database schema setup completed\n");
  // } catch (error) {
  //   console.log(
  //     "⚠️  Database schema setup failed - you may need to configure your database connection"
  //   );
  //   console.log("   Check your DATABASE_URL in .env file\n");
  // }

  // Create admin user
  // try {
  //   console.log("📋 Creating admin user...");
  //   execSync("node scripts/create-admin-user.js", {
  //     stdio: "inherit",
  //     cwd: projectRoot,
  //   });
  // } catch (error) {
  //   console.log(
  //     "⚠️  Admin user creation failed - you can create it manually later"
  //   );
  //   console.log("   Run: npm run create-admin\n");
  // }

  console.log("🎉 Development environment setup complete!");
  console.log("\n📋 Next steps:");
  console.log("   1. Start the development server: npm run dev");
  console.log("   2. Open https://localhost:3002 in your browser");
  console.log(
    "   3. Accept the SSL certificate warning (normal for development)"
  );
  console.log("   4. <NAME_EMAIL> / admin123");
  console.log("   5. Change the default admin password!");
  console.log("\n🔧 Troubleshooting:");
  console.log(
    "   - If certificates don't work, run: node scripts/generate-certs.js"
  );
  console.log("   - If database issues, check your DATABASE_URL in .env");
  console.log("   - If admin login fails, run: npm run create-admin");
}

setupDevelopment();
