import { cookies } from "next/headers";

export async function GET(req) {
  // Controlla la presenza dell'access_token tra i cookie httpOnly
  const cookiesObj = await cookies();
  const accessToken = cookiesObj.get("access_token")?.value;
  if (accessToken) {
    return new Response(JSON.stringify({ loggedIn: true }), {
      status: 200,
      headers: { "Content-Type": "application/json" },
    });
  } else {
    return new Response(JSON.stringify({ loggedIn: false }), {
      status: 200,
      headers: { "Content-Type": "application/json" },
    });
  }
}
