"use client";

import {
    getAccountNumbers,
    getUserPreference,
    getAllOrders,
    getLinkedAccounts,
    getTransactions
} from "../../actions/schwabTraderAPIactions.js";
import { useMarketData } from "../testingWebsocket/MarketDataContext.js";
import { usePairArray } from "../pairArray/PairArray.js";
import {useEndpointAppContext} from "../../components/EndpointAppContext.js";
import { useComponentArray } from "../pairArray/PairComponent.js";

export default function TestingPage() {
    const pairArray = usePairArray();
    const componentArray = useComponentArray();
    const { accountData } = useEndpointAppContext(); 
    const { filteredData } = useMarketData();
    const handleGetAccountsNumbersClick = () => {
        getAccountNumbers().then((accounts) => {
            console.log("Account numbers:", accounts);
            //   setData(accounts);
        });
    }
    const handleGetUserPreference = () => {
        getUserPreference().then((preference) => {
            console.log("User preference:", preference);
        });
    }
    const handleGetOrders = () => {
        getAllOrders("2024-03-01T00:00:00Z", "2024-03-05T23:59:59Z").then((orders) => {
            console.log("Orders:", orders);
        });
    }
    const handleGetLinkedAccounts = () => {
        getLinkedAccounts().then((accounts) => {
            console.log("Linked accounts:", accounts);
        });
    }
    const handleGetTransactions = () => {
        const accountNumber = process.env.NEXT_PUBLIC_ACCOUNT_NUMBER;
        console.log("Account Number:", accountNumber);
        getTransactions(accountNumber, "2024-03-01T00:00:00Z", "2024-03-05T23:59:59Z", "TRADE").then((transactions) => {
            console.log("Transactions:", transactions);
        });
    }
    const handleGetPairArray = () => {
        // Assuming you want to log the pairArray data
        console.log("Pair Array Data:", pairArray);
    }
    const handleGetDataFromUser = () => {
        console.log("data:", accountData);
    }
    const handleGetComponentArray = () => {
        // Assuming you want to log the componentArray data
        console.log("Component Array Data:", componentArray);
    }



    // WebSocket

    return (
        <div>
            <h1>Testing Page</h1>
            <button className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded" onClick={handleGetAccountsNumbersClick}>
                Get Account numbers
            </button>
            <button className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded" onClick={handleGetUserPreference}>
                Get User preference
            </button>
            <button className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded" onClick={handleGetOrders}>
                Get Orders
            </button>
            <button className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded" onClick={() => console.log(filteredData)}>
                Log Market Data
            </button>
            <button className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded" onClick={handleGetLinkedAccounts}>
                Get Linked Accounts
            </button>
            <button className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded" onClick={handleGetTransactions}>
                Get Transactions   
            </button>
            <button className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded" onClick={handleGetPairArray}>
                Get Pair Array
            </button>
            <button className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded" onClick={handleGetDataFromUser}>
                Get Data from User
            </button>
            <button className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded" onClick={handleGetComponentArray}>
                Get Component Array
            </button>
        </div>
    );
}


