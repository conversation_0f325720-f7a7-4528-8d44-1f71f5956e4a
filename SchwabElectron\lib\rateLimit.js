// lib/rateLimit.js
// Simple in-memory rate limiter for development/testing (not for production scale)
const store = new Map();

export function rateLimit(key, limit = 5, windowMs = 60 * 1000) {
  const now = Date.now();
  let entry = store.get(key);
  if (!entry || now - entry.last > windowMs) {
    entry = { count: 1, last: now };
  } else {
    entry.count += 1;
  }
  store.set(key, entry);
  return entry.count > limit;
}
