{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "app/dashboard/page.js", "app/login/page.js", "app/layout.js", "app/page.js", "components/providers.js", "app/api/auth/register/route.js", "app/api/auth/[...nextauth]/route.js", "components/toaster.jsx", "components/dashboard/balance-card.jsx", "components/dashboard/positions-card.js", "components/dashboard/loading.jsx", "hooks/use-toast.js", "lib/schwab-api.js", "components/NavMenu/NavLink.jsx"], "exclude": ["node_modules"]}