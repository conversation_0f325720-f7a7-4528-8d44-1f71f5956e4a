{"name": "nextjs-trading-app", "version": "0.1.0", "private": true, "main": "main/main.js", "author": "ZapTech", "description": "Electron + NextJS example project", "type": "module", "build": {"extraFiles": [{"from": ".env", "to": "./", "filter": ["**/*"]}]}, "scripts": {"dev": "concurrently -k -s first -n \"NEXT,PROXY,ELECTRON\" -c \"yellow,blue,green\" \"next dev\" \"wait-on http://localhost:3000 && local-ssl-proxy --source 3002 --target 3000 --cert ./certificates/localhost.pem --key ./certificates/localhost-key.pem\" \"wait-on https://localhost:3002 && electron .\"", "dev-web": "concurrently -k -s first -n \"NEXT,PROXY\" -c \"yellow,blue\" \"next dev\" \"wait-on http://localhost:3000 && local-ssl-proxy --source 3002 --target 3000 --cert ./certificates/localhost.pem --key ./certificates/localhost-key.pem\"", "build": "next build && electron-builder", "build-web": "next build", "startWithServer": "next start & node server.js", "start": "next start", "server": "node server.js", "lint": "next lint", "setup": "node scripts/setup-dev.js", "generate-certs": "node scripts/generate-certs.js", "create-admin": "node scripts/create-admin-user.js"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emnapi/runtime": "^1.4.5", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.9.0", "@next/swc-wasm-nodejs": "13.5.1", "@prisma/client": "^6.12.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-tooltip": "^1.2.4", "@tanstack/react-table": "^8.20.6", "autoprefixer": "^10.4.19", "axios": "^1.7.9", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cookies-next": "^5.0.2", "cors": "^2.8.5", "dotenv": "^16.4.7", "electron-serve": "^2.1.1", "eslint": "^9.28.0", "express": "^4.21.2", "express-rate-limit": "^7.5.1", "framer-motion": "^11.18.2", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.446.0", "moment": "^2.30.1", "mongodb": "^6.15.0", "motion": "^12.23.12", "next": "^15.1.0", "next-auth": "^4.24.11", "next-csrf": "^0.2.1", "next-themes": "^0.2.1", "postcss": "^8.4.38", "prisma": "^6.12.0", "rate-limiter-flexible": "^7.1.1", "react": "18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "18.2.0", "react-hook-form": "^7.53.0", "react-icons": "^5.5.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "tailwind-merge": "^2.5.2", "tailwindcss": "3.3.3", "tailwindcss-animate": "^1.0.7", "ws": "^8.18.1", "zod": "^3.23.8"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/node": "22.10.1", "@types/react": "^18.2.67", "@types/react-dom": "^18.2.22", "concurrently": "^9.2.0", "electron": "^37.2.4", "electron-builder": "^26.0.12", "local-ssl-proxy": "^2.0.5", "typescript": "5.7.2", "wait-on": "^8.0.4"}}