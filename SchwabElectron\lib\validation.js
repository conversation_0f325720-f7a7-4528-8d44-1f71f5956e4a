import { z } from 'zod';

/**
 * Basic validation schema for login credentials
 */
export const loginSchema = z.object({
  email: z.string().email({ message: "Invalid email address" }),
  password: z.string().min(6, { message: "Password must be at least 6 characters" }),
});

/**
 * Basic validation schema for Excel data
 */
export const excelDataSchema = z.object({
  shortOpenTableData: z.array(z.any()).optional().default([]),
  shortLoadedTableData: z.array(z.any()).optional().default([]),
  longOpenTableData: z.array(z.any()).optional().default([]),
  longLoadedTableData: z.array(z.any()).optional().default([]),
  shortClosedTableData: z.array(z.any()).optional().default([]),
  longClosedTableData: z.array(z.any()).optional().default([])
});

/**
 * Basic validation schema for trading pairs
 */
export const tradingPairsSchema = z.object({
  pairs: z.array(z.object({
    status: z.string().optional().default(""),
    shortComponent: z.any(),
    longComponent: z.any(),
    pairKey: z.any().optional(),
    combinedPNL: z.any().optional().default("0")
  }))
});

/**
 * Generic input validation function
 * @param {object} schema - Zod schema to validate against
 * @param {any} data - Data to validate
 * @returns {object} - Validation result
 */
export function validateInput(schema, data) {
  try {
    const result = schema.safeParse(data);
    if (result.success) {
      return { success: true, data: result.data };
    } else {
      console.log("Validation error:", result.error.errors);
      return {
        success: false,
        errors: result.error.errors.map(err => ({
          path: err.path.join('.'),
          message: err.message
        }))
      };
    }
  } catch (error) {
    console.error("Unexpected validation error:", error);
    return {
      success: false,
      errors: [{ path: "unknown", message: "Unexpected validation error" }]
    };
  }
}

/**
 * Sanitizes an array of data by removing potentially harmful properties
 * @param {Array} array - Array to sanitize
 * @returns {Array} - Sanitized array
 */
export function sanitizeArrayData(array) {
  if (!Array.isArray(array)) return [];

  return array.map(item => {
    if (typeof item !== 'object' || item === null) return item;

    // Create a new object with only safe properties
    const safeItem = {};
    for (const [key, value] of Object.entries(item)) {
      // Skip functions or potentially dangerous properties
      if (typeof value !== 'function' &&
          key !== '__proto__' &&
          key !== 'constructor') {
        safeItem[key] = value;
      }
    }
    return safeItem;
  });
}

/**
 * Sanitizes an object by removing potentially harmful properties
 * @param {object} obj - Object to sanitize
 * @returns {object} - Sanitized object
 */
export function sanitizeObject(obj) {
  if (typeof obj !== 'object' || obj === null) return {};

  const safeObj = {};
  for (const [key, value] of Object.entries(obj)) {
    // Skip functions or potentially dangerous properties
    if (typeof value !== 'function' &&
        key !== '__proto__' &&
        key !== 'constructor') {

      // Handle nested objects
      if (typeof value === 'object' && value !== null) {
        if (Array.isArray(value)) {
          safeObj[key] = sanitizeArrayData(value);
        } else {
          safeObj[key] = sanitizeObject(value);
        }
      } else {
        safeObj[key] = value;
      }
    }
  }

  return safeObj;
}
