"use client";
import { useState, useEffect, useRef } from "react";
import { useSession } from "next-auth/react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import {
  retrieveAccessToken,
  retrieveCustomerId,
  retrieveCorrelId,
} from "@/actions/schwabAccess";
import { getServerUrl } from "@/utils/serverConfig";
import { DndContext, closestCenter } from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  verticalListSortingStrategy,
  useSortable,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { usePairArray } from "../../../pairArray/PairArray";
import { useExcelData } from "../ExcelDataContext";
import ProtectedRoute from "../../../../components/ProtectedRoute";

function ShortSortableRow({ id, item }) {
  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({ id: id.toString() });
  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    cursor: "grab",
  };

  // Determine color for change value
  const changeValue = parseFloat(item.formattedChange);
  const changeColor =
    changeValue > 0
      ? "text-green-500 dark:text-green-400"
      : changeValue < 0
      ? "text-red-500 dark:text-red-400"
      : "";

  // Determine color for amount value
  const amtValue = parseFloat(item.formattedAmt);
  const amtColor =
    amtValue < 0
      ? "text-red-500 dark:text-red-400"
      : "text-green-500 dark:text-green-400";

  // HIGHLIGHT: Check if shares should be highlighted (0 shares)
  const sharesValue =
    item.statusValue === "WB_OpenPositions"
      ? `-${item.formattedAmt}`  
      : item.expectedQuantity;
  const highlightClass = "";

  return (
    <tr
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className='border-b dark:border-gray-700 transition-colors bg-black hover:bg-gray-900'
    >
      <td className='px-0 py-0 h-7 text-base-condensed text-right dark:text-gray-300'>
        {item.formattedCost || "-"}
      </td>
      <td
        className={`px-0 py-0 h-7 text-base-condensed text-right text-red-500 dark:text-red-400 ${highlightClass}`}
      >
        {sharesValue || "-"}
      </td>
      <td className='px-0 py-0 h-7 text-base-condensed text-right text-yellow-500 dark:text-yellow-400'>
        {item.ticker || "-"}
      </td>
      <td className='px-0 py-0 h-7 text-base-condensed text-right dark:text-gray-300'>
        {item.formattedBid || "-"}
      </td>
      <td className='px-0 py-0 h-7 text-base-condensed text-right dark:text-gray-300'>
        {item.formattedAsk || "-"}
      </td>
      <td className='px-0 py-0 h-7 text-base-condensed text-right text-white-500 dark:text-white-400'>
        {item.formattedLast || "-"}
      </td>
      <td
        className={`px-0 py-0 h-7 text-base-condensed text-right ${changeColor}`}
      >
        {item.formattedChange || "-"}
      </td>
      <td className='px-0 py-0 h-7 text-base-condensed text-right text-white-500 dark:text-white-400'>
        {item.formattedVolume || "-"}
      </td>
      <td className='px-0 py-0 h-7 text-base-condensed text-right text-white-500 dark:text-white-400'>
        {item.formattedDividend || "-"}
      </td>
      <td className='px-0 py-0 h-7 text-base-condensed text-right bg-black text-white'>
        {item.exDateValue || "-"}
      </td>
      <td className='px-0 py-0 h-7 text-base-condensed text-right bg-black text-white'>
        {item.statusValue === "WB_OpenPositions"
          ? item.formattedSpread
          : item.formattedSpreadUser || "-"}
      </td>
      <td className='px-0 py-0 h-7 text-base-condensed text-right dark:text-gray-300'>
        {item.dollarCost || "-"}
      </td>
    </tr>
  );
}

function LongSortableRow({ id, item }) {
  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({ id: id.toString() });
  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    cursor: "grab",
  };

  // Determine color for change value
  const changeValue = parseFloat(item.formattedChange);
  const changeColor =
    changeValue > 0
      ? "text-green-500 dark:text-green-400"
      : changeValue < 0
      ? "text-red-500 dark:text-red-400"
      : "";

  // Determine color for amount value
  const amtValue = parseFloat(item.formattedAmt);
  const amtColor =
    amtValue < 0
      ? "text-red-500 dark:text-red-400"
      : "text-green-500 dark:text-green-400";

  // Determine color for PNL
  const pnlValue = parseFloat(item.pnl);
  const pnlColor =
    pnlValue > 0
      ? "text-green-500 dark:text-green-400"
      : pnlValue < 0
      ? "text-red-500 dark:text-red-400"
      : "";

  // HIGHLIGHT: Check if shares should be highlighted (0 shares)
  const sharesValue =
    item.statusValue === "WB_OpenPositions"
      ? item.formattedAmt
      : item.expectedQuantity;
  const highlightClass = "";

  return (
    <tr
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className='border-b dark:border-gray-700 transition-colors bg-black hover:bg-gray-900'
    >
      <td className='px-0 py-0 h-7 text-base-condensed text-right dark:text-gray-300'>
        {item.statusValue === "WB_OpenPositions"
          ? item.formattedSpread
          : item.formattedSpreadUser || "-"}
      </td>
      <td className='px-0 py-0 h-7 text-base-condensed text-right bg-black text-white'>
        {item.exDateValue || "-"}
      </td>
      <td className='px-0 py-0 h-7 text-base-condensed text-right dark:text-gray-300'>
        {item.formattedCost || "-"}
      </td>
      <td
        className={`px-0 py-0 h-7 text-base-condensed text-right ${amtColor} ${highlightClass}`}
      >
        {sharesValue || "-"}
      </td>
      <td className='px-0 py-0 h-7 text-base-condensed text-right text-yellow-500 dark:text-yellow-400'>
        {item.ticker || "-"}
      </td>
      <td className='px-0 py-0 h-7 text-base-condensed text-right dark:text-gray-300'>
        {item.formattedBid || "-"}
      </td>
      <td className='px-0 py-0 h-7 text-base-condensed text-right dark:text-gray-300'>
        {item.formattedAsk || "-"}
      </td>
      <td className='px-0 py-0 h-7 text-base-condensed text-right text-white-500 dark:text-white-400'>
        {item.formattedLast || "-"}
      </td>
      <td
        className={`px-0 py-0 h-7 text-base-condensed text-right ${changeColor}`}
      >
        {item.formattedChange || "-"}
      </td>
      <td className='px-0 py-0 h-7 text-base-condensed text-right text-white-500 dark:text-white-400'>
        {item.formattedVolume || "-"}
      </td>
      <td className='px-0 py-0 h-7 text-base-condensed text-right text-white-500 dark:text-white-400'>
        {item.formattedDividend || "-"}
      </td>
    </tr>
  );
}
// Edit Data Dialog Component
function EditDataDialog({
  editDialog,
  setEditDialog,
  shortOpenTableData,
  shortLoadedTableData,
  shortClosedTableData,
  longOpenTableData,
  longLoadedTableData,
  longClosedTableData,
  setShortOpenTableData,
  setShortLoadedTableData,
  setShortClosedTableData,
  setLongOpenTableData,
  setLongLoadedTableData,
  setLongClosedTableData,
}) {
  const [longFormData, setLongFormData] = useState({});
  const [shortFormData, setShortFormData] = useState({});

  // Update form data when dialog opens
  useEffect(() => {
    if (editDialog.open && editDialog.longData && editDialog.shortData) {
      // Filter out status and id fields
      const filterFields = (data) => {
        const filtered = { ...data };
        delete filtered.status;
        delete filtered.id;
        return filtered;
      };

      setLongFormData(filterFields(editDialog.longData));
      setShortFormData(filterFields(editDialog.shortData));
    }
  }, [editDialog.open, editDialog.longData, editDialog.shortData]);

  const handleLongInputChange = (field, value) => {
    setLongFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleShortInputChange = (field, value) => {
    setShortFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSave = () => {
    if (!editDialog.open) return;

    const { section, idx } = editDialog;

    // Update both Long and Short tables
    let longTableData, shortTableData;
    let setLongTableData, setShortTableData;

    switch (section) {
      case "Open":
        longTableData = longOpenTableData;
        shortTableData = shortOpenTableData;
        setLongTableData = setLongOpenTableData;
        setShortTableData = setShortOpenTableData;
        break;
      case "Loaded":
        longTableData = longLoadedTableData;
        shortTableData = shortLoadedTableData;
        setLongTableData = setLongLoadedTableData;
        setShortTableData = setShortLoadedTableData;
        break;
      case "Closed":
        longTableData = longClosedTableData;
        shortTableData = shortClosedTableData;
        setLongTableData = setLongClosedTableData;
        setShortTableData = setShortClosedTableData;
        break;
      default:
        return;
    }

    // Update both Long and Short table data
    const updatedLongTableData = [...longTableData];
    const updatedShortTableData = [...shortTableData];

    // Preserve original status and id, update other fields
    updatedLongTableData[idx] = {
      ...editDialog.longData, // Keep original data including status and id
      ...longFormData, // Override with form changes
    };
    updatedShortTableData[idx] = {
      ...editDialog.shortData, // Keep original data including status and id
      ...shortFormData, // Override with form changes
    };

    setLongTableData(updatedLongTableData);
    setShortTableData(updatedShortTableData);

    // Close dialog
    setEditDialog({
      open: false,
      section: null,
      idx: null,
      id: null,
      longData: null,
      shortData: null,
      tableType: null,
    });
  };

  const handleCancel = () => {
    setEditDialog({
      open: false,
      section: null,
      idx: null,
      id: null,
      longData: null,
      shortData: null,
      tableType: null,
    });
  };

  if (!editDialog.open) return null;

  return (
    <Dialog open={editDialog.open} onOpenChange={handleCancel}>
      <DialogContent className='max-w-4xl max-h-[80vh] overflow-y-auto'>
        <DialogHeader>
          <DialogTitle>Edit Pair Data - {editDialog.section}</DialogTitle>
        </DialogHeader>

        <div className='grid grid-cols-2 gap-6 py-4'>
          {/* Short Side */}
          <div className='space-y-4'>
            <h3 className='text-lg font-semibold text-red-700 dark:text-red-400 border-b pb-2'>
              Short Side
            </h3>
            <div className='grid grid-cols-1 gap-3'>
              {Object.entries(shortFormData).map(([key, value]) => (
                <div key={key} className='space-y-1'>
                  <label className='text-sm font-medium text-gray-700 dark:text-gray-300 capitalize'>
                    {key.replace(/([A-Z])/g, " $1").trim()}
                  </label>
                  <input
                    type='text'
                    value={value || ""}
                    onChange={(e) =>
                      handleShortInputChange(key, e.target.value)
                    }
                    className='w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-900 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500'
                  />
                </div>
              ))}
            </div>
          </div>
          {/* Long Side */}
          <div className='space-y-4'>
            <h3 className='text-lg font-semibold text-green-700 dark:text-green-400 border-b pb-2'>
              Long Side
            </h3>
            <div className='grid grid-cols-1 gap-3'>
              {Object.entries(longFormData).map(([key, value]) => (
                <div key={key} className='space-y-1'>
                  <label className='text-sm font-medium text-gray-700 dark:text-gray-300 capitalize'>
                    {key.replace(/([A-Z])/g, " $1").trim()}
                  </label>
                  <input
                    type='text'
                    value={value || ""}
                    onChange={(e) => handleLongInputChange(key, e.target.value)}
                    className='w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-900 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-green-500'
                  />
                </div>
              ))}
            </div>
          </div>
        </div>

        <DialogFooter>
          <button
            onClick={handleCancel}
            className='px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors'
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className='px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors'
          >
            Save Changes
          </button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export default function DraggablePairTable() {
  // Dialog state for move action
  const [moveDialog, setMoveDialog] = useState({
    open: false,
    section: null,
    idx: null,
    id: null,
  });

  // Sortable Header Component - shows arrow only when active on the correct side
  const SortableHeader = ({ section, field, side, children, className }) => {
    const currentSort = sortConfig[section];
    const isActive = currentSort?.field === field && currentSort?.side === side;
    const direction = isActive ? currentSort.direction : null;

    return (
      <th
        className={`${className} cursor-pointer hover:bg-opacity-80 transition-colors select-none`}
        onClick={() => handleSort(section, field, side)}
        title={`Sort by ${children} (${side.toUpperCase()} side)`}
      >
        <div className="flex items-center justify-center gap-1">
          <span>{children}</span>
          {isActive && (
            <span className="text-black dark:text-white text-xs font-bold">
              {direction === 'asc' ? '↑' : '↓'}
            </span>
          )}
        </div>
      </th>
    );
  };

  // Dialog state for edit action
  const [editDialog, setEditDialog] = useState({
    open: false,
    section: null,
    idx: null,
    id: null,
    longData: null,
    shortData: null,
    tableType: null, // Keep for reference
  });
  const {
    pairArray,
    setPairArray,
    pairStatus,
    setPairStatus,
    savePairsToDatabase,
    isSaving,
    saveStatus,
    loadPairsFromDatabase,
    isLoading,
    loadStatus,
  } = usePairArray();
  const {
    shortOpenTableData,
    shortLoadedTableData,
    longOpenTableData,
    longLoadedTableData,
    shortClosedTableData,
    longClosedTableData,
    setShortOpenTableData,
    setShortLoadedTableData,
    setShortClosedTableData,
    setLongOpenTableData,
    setLongLoadedTableData,
    setLongClosedTableData,
    updateLongStatus,
    updateShortStatus,
    updateShortClosedStatus,
    updateLongClosedStatus,
    loadExcelDataFromDatabase,
  } = useExcelData();
  const [loadedShortItems, setLoadedShortItems] = useState([]);
  const [loadedLongItems, setLoadedLongItems] = useState([]);
  const [openShortItems, setOpenShortItems] = useState([]);
  const [openLongItems, setOpenLongItems] = useState([]);
  const [closedShortItems, setClosedShortItems] = useState([]);
  const [closedLongItems, setClosedLongItems] = useState([]);

  // Sorting state - now tracks each section and side independently
  const [sortConfig, setSortConfig] = useState({
    open: { field: null, direction: 'asc', side: null }, // side: 'short' or 'long'
    loaded: { field: null, direction: 'asc', side: null },
    closed: { field: null, direction: 'asc', side: null }
  });

  // Session for role-based access control
  const { data: session } = useSession();

  // EDITING: Stato per la sezione di editing dati fantoccio
  const [showEditingSection, setShowEditingSection] = useState(false);
  const [editingPairs, setEditingPairs] = useState([]);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [persistentOverrides, setPersistentOverrides] = useState({}); // Memorizza le modifiche persistenti
  const [controlsExpanded, setControlsExpanded] = useState(false); // Accordion state for controls
  const [updateStatus, setUpdateStatus] = useState(""); // Status for update symbols operation

  // Beams background animation
  const canvasRef = useRef(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    let beams = [];
    let animationFrame;

    const createBeam = (width, height) => ({
      x: Math.random() * width * 1.5 - width * 0.25,
      y: Math.random() * height * 1.5 - height * 0.25,
      width: 30 + Math.random() * 60,
      length: height * 2.5,
      angle: -35 + Math.random() * 10,
      speed: 0.6 + Math.random() * 1.2,
      opacity: 0.12 + Math.random() * 0.16,
      hue: 190 + Math.random() * 70,
      pulse: Math.random() * Math.PI * 2,
      pulseSpeed: 0.02 + Math.random() * 0.03,
    });

    const updateCanvasSize = () => {
      const dpr = window.devicePixelRatio || 1;
      canvas.width = window.innerWidth * dpr;
      canvas.height = window.innerHeight * dpr;
      canvas.style.width = `${window.innerWidth}px`;
      canvas.style.height = `${window.innerHeight}px`;
      ctx.scale(dpr, dpr);

      beams = Array.from({ length: 15 }, () =>
        createBeam(canvas.width, canvas.height)
      );
    };

    const drawBeam = (beam) => {
      ctx.save();
      ctx.translate(beam.x, beam.y);
      ctx.rotate((beam.angle * Math.PI) / 180);

      const pulsingOpacity = beam.opacity * (0.8 + Math.sin(beam.pulse) * 0.2);
      const gradient = ctx.createLinearGradient(0, 0, 0, beam.length);

      gradient.addColorStop(0, `hsla(${beam.hue}, 85%, 65%, 0)`);
      gradient.addColorStop(
        0.1,
        `hsla(${beam.hue}, 85%, 65%, ${pulsingOpacity * 0.5})`
      );
      gradient.addColorStop(
        0.4,
        `hsla(${beam.hue}, 85%, 65%, ${pulsingOpacity})`
      );
      gradient.addColorStop(
        0.6,
        `hsla(${beam.hue}, 85%, 65%, ${pulsingOpacity})`
      );
      gradient.addColorStop(
        0.9,
        `hsla(${beam.hue}, 85%, 65%, ${pulsingOpacity * 0.5})`
      );
      gradient.addColorStop(1, `hsla(${beam.hue}, 85%, 65%, 0)`);

      ctx.fillStyle = gradient;
      ctx.fillRect(-beam.width / 2, 0, beam.width, beam.length);
      ctx.restore();
    };

    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      ctx.filter = "blur(35px)";

      beams.forEach((beam, index) => {
        beam.y -= beam.speed;
        beam.pulse += beam.pulseSpeed;

        if (beam.y + beam.length < -100) {
          beam.y = canvas.height + 100;
          beam.x = Math.random() * canvas.width;
        }

        drawBeam(beam);
      });

      animationFrame = requestAnimationFrame(animate);
    };

    updateCanvasSize();
    animate();

    window.addEventListener("resize", updateCanvasSize);

    return () => {
      window.removeEventListener("resize", updateCanvasSize);
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, []);

  // Funzione per applicare gli override persistenti ai dati
  const applyPersistentOverrides = (pairs) => {
    return pairs.map((pair) => {
      const overrides = persistentOverrides[pair.key];
      if (!overrides) return pair;

      return {
        ...pair,
        shortComponent: {
          ...pair.shortComponent,
          ...overrides.shortComponent,
        },
        longComponent: {
          ...pair.longComponent,
          ...overrides.longComponent,
        },
      };
    });
  };

  const loadedPairs = pairArray
    ? applyPersistentOverrides(
        pairArray.filter((pair) => pair.status === "WB_LoadedPairs")
      )
    : [];
  const openPairs = pairArray
    ? applyPersistentOverrides(
        pairArray.filter((pair) => pair.status === "WB_OpenPositions")
      )
    : [];
  const closedPairs = pairArray
    ? applyPersistentOverrides(
        pairArray.filter((pair) => pair.status === "WB_ClosedPositions")
      )
    : [];

  useEffect(() => {
    const newLoadedShort = loadedPairs.map((pair) => ({
      ...pair.shortComponent,
      pairKey: pair.key,
    }));
    const newLoadedLong = loadedPairs.map((pair) => ({
      ...pair.longComponent,
      pairKey: pair.key,
    }));
    if (JSON.stringify(newLoadedShort) !== JSON.stringify(loadedShortItems)) {
      setLoadedShortItems(newLoadedShort);
    }
    if (JSON.stringify(newLoadedLong) !== JSON.stringify(loadedLongItems)) {
      setLoadedLongItems(newLoadedLong);
    }
  }, [loadedPairs]);

  useEffect(() => {
    const newOpenShort = openPairs.map((pair) => ({
      ...pair.shortComponent,
      pairKey: pair.key,
    }));
    const newOpenLong = openPairs.map((pair) => ({
      ...pair.longComponent,
      pairKey: pair.key,
    }));
    if (JSON.stringify(newOpenShort) !== JSON.stringify(openShortItems)) {
      setOpenShortItems(newOpenShort);
    }
    if (JSON.stringify(newOpenLong) !== JSON.stringify(openLongItems)) {
      setOpenLongItems(newOpenLong);
    }
  }, [openPairs, openShortItems, openLongItems]);
  useEffect(() => {
    const newClosedShort = closedPairs.map((pair) => ({
      ...pair.shortComponent,
      pairKey: pair.key,
    }));
    const newClosedLong = closedPairs.map((pair) => ({
      ...pair.longComponent,
      pairKey: pair.key,
    }));
    if (JSON.stringify(newClosedShort) !== JSON.stringify(closedShortItems)) {
      setClosedShortItems(newClosedShort);
    }
    if (JSON.stringify(newClosedLong) !== JSON.stringify(closedLongItems)) {
      setClosedLongItems(newClosedLong);
    }
  }, [closedPairs, closedShortItems, closedLongItems]);

  const extractShortFields = (component) => ({
    ticker: component.ticker || "",
    shares: component.expectedQuantity || "",
    sector: component.sectorValue || "",
    spread: component.formattedSpreadUser || "",
    volume: component.formattedLoadedVolume || "",
    id: component.id || "",
    status: component.statusValue || "",
    dividend: component.formattedUserDividend || "",
  });

  const extractLongFields = (component) => ({
    ticker: component.ticker || "",
    shares: component.expectedQuantity || "",
    sector: component.sectorValue || "",
    spread: component.formattedSpreadUser || "",
    volume: component.formattedLoadedVolume || "",
    status: component.statusValue || "",
    id: component.id || "",
    dividend: component.formattedUserDividend || "",
  });

  const handleDragEndLoadedShort = (event) => {
    const { active, over } = event;
    if (!over || active.id === over.id) return;
    const oldIndex = Number(active.id);
    const newIndex = Number(over.id);
    const newOrder = arrayMove(loadedShortItems, oldIndex, newIndex);
    setLoadedShortItems(newOrder);
    setShortLoadedTableData(newOrder.map(extractShortFields));
  };

  const handleDragEndLoadedLong = (event) => {
    const { active, over } = event;
    if (!over || active.id === over.id) return;
    const oldIndex = Number(active.id);
    const newIndex = Number(over.id);
    const newOrder = arrayMove(loadedLongItems, oldIndex, newIndex);
    setLoadedLongItems(newOrder);
    setLongLoadedTableData(newOrder.map(extractLongFields));
  };

  const handleDragEndOpenShort = (event) => {
    const { active, over } = event;
    if (!over || active.id === over.id) return;
    const oldIndex = openShortItems.findIndex(
      (_, idx) => idx.toString() === active.id
    );
    const newIndex = openShortItems.findIndex(
      (_, idx) => idx.toString() === over.id
    );
    if (oldIndex === -1 || newIndex === -1) return;
    const newOrder = arrayMove(openShortItems, oldIndex, newIndex);
    setOpenShortItems(newOrder);
    setShortOpenTableData(newOrder.map(extractShortFields));
  };

  const handleDragEndOpenLong = (event) => {
    const { active, over } = event;
    if (!over || active.id === over.id) return;
    const oldIndex = openLongItems.findIndex(
      (_, idx) => idx.toString() === active.id
    );
    const newIndex = openLongItems.findIndex(
      (_, idx) => idx.toString() === over.id
    );
    if (oldIndex === -1 || newIndex === -1) return;
    const newOrder = arrayMove(openLongItems, oldIndex, newIndex);
    setOpenLongItems(newOrder);
    setLongOpenTableData(newOrder.map(extractLongFields));
  };

  const handleDragEndClosedShort = (event) => {
    const { active, over } = event;
    if (!over || active.id === over.id) return;
    const oldIndex = closedShortItems.findIndex(
      (_, idx) => idx.toString() === active.id
    );
    const newIndex = closedShortItems.findIndex(
      (_, idx) => idx.toString() === over.id
    );
    if (oldIndex === -1 || newIndex === -1) return;
    const newOrder = arrayMove(closedShortItems, oldIndex, newIndex);
    setClosedShortItems(newOrder);
    setShortClosedTableData(newOrder.map(extractShortFields));
  };

  const handleDragEndClosedLong = (event) => {
    const { active, over } = event;
    if (!over || active.id === over.id) return;
    const oldIndex = closedLongItems.findIndex(
      (_, idx) => idx.toString() === active.id
    );
    const newIndex = closedLongItems.findIndex(
      (_, idx) => idx.toString() === over.id
    );
    if (oldIndex === -1 || newIndex === -1) return;
    const newOrder = arrayMove(closedLongItems, oldIndex, newIndex);
    setClosedLongItems(newOrder);
    setLongClosedTableData(newOrder.map(extractLongFields));
  };

  // Sorting functions that work with items (which have all fields)
  const getSortValue = (item, field, section) => {
    switch (field) {
      case 'cost':
        return parseFloat(item.formattedCost?.replace(/[,$]/g, '') || '0');
      case 'ticker':
        return item.ticker || '';
      case 'shares':
        // Different field names for different sections
        if (section === 'loaded') {
          return parseFloat(item.expectedQuantity?.replace(/[,$]/g, '') || '0');
        } else {
          return parseFloat(item.formattedAmt?.replace(/[,$]/g, '') || '0');
        }
      case 'bid':
        return parseFloat(item.formattedBid?.replace(/[,$]/g, '') || '0');
      case 'ask':
        return parseFloat(item.formattedAsk?.replace(/[,$]/g, '') || '0');
      case 'last':
        return parseFloat(item.formattedLast?.replace(/[,$]/g, '') || '0');
      case 'change':
        return parseFloat(item.formattedChange?.replace(/[,%]/g, '') || '0');
      case 'spread':
        // Different spread sources for different sections
        if (section === 'open') {
          // For open positions, use real spread (formattedSpread)
          return parseFloat(item.formattedSpread?.replace(/[,$]/g, '') || '0');
        } else {
          // For loaded and closed positions, use user-entered spread (formattedSpreadUser)
          return parseFloat(item.formattedSpreadUser?.replace(/[,$]/g, '') || '0');
        }
      case 'volume':
        return parseFloat(item.formattedVolume?.replace(/[,$]/g, '') || '0');
      case 'dividend':
        return parseFloat(item.formattedDividend?.replace(/[,$]/g, '') || '0');
      case 'sector':
        return item.sectorValue || '';
      case 'status':
        return item.statusValue || '';
      case 'pnl':
        return parseFloat(item.pnl || '0');
      case 'exdate':
        return item.exDateValue || '';
      default:
        return '';
    }
  };

  const handleSort = (section, field, side) => {
    const currentSort = sortConfig[section];
    const isCurrentField = currentSort.field === field && currentSort.side === side;
    const newDirection = isCurrentField && currentSort.direction === 'asc' ? 'desc' : 'asc';

    setSortConfig(prev => ({
      ...prev,
      [section]: {
        field,
        direction: newDirection,
        side
      }
    }));

    // Get the appropriate items arrays (which have all the fields)
    let shortItems, longItems, setShortItems, setLongItems, setShortTableData, setLongTableData;

    switch (section) {
      case 'open':
        shortItems = [...openShortItems];
        longItems = [...openLongItems];
        setShortItems = setOpenShortItems;
        setLongItems = setOpenLongItems;
        setShortTableData = setShortOpenTableData;
        setLongTableData = setLongOpenTableData;
        break;
      case 'loaded':
        shortItems = [...loadedShortItems];
        longItems = [...loadedLongItems];
        setShortItems = setLoadedShortItems;
        setLongItems = setLoadedLongItems;
        setShortTableData = setShortLoadedTableData;
        setLongTableData = setLongLoadedTableData;
        break;
      case 'closed':
        shortItems = [...closedShortItems];
        longItems = [...closedLongItems];
        setShortItems = setClosedShortItems;
        setLongItems = setClosedLongItems;
        setShortTableData = setShortClosedTableData;
        setLongTableData = setLongClosedTableData;
        break;
      default:
        return;
    }

    // Create pairs for sorting from items (which have all the fields)
    const pairs = shortItems.map((shortItem, index) => ({
      short: shortItem,
      long: longItems[index],
      index
    }));

    // Sort pairs based on the selected side (SHORT or LONG)
    pairs.sort((a, b) => {
      const sourceItem = side === 'short' ? a.short : a.long;
      const targetItem = side === 'short' ? b.short : b.long;

      const aValue = getSortValue(sourceItem, field, section);
      const bValue = getSortValue(targetItem, field, section);

      if (typeof aValue === 'string') {
        const result = aValue.localeCompare(bValue);
        return newDirection === 'asc' ? result : -result;
      } else {
        const result = aValue - bValue;
        return newDirection === 'asc' ? result : -result;
      }
    });

    // Extract sorted arrays
    const sortedShortItems = pairs.map(pair => pair.short);
    const sortedLongItems = pairs.map(pair => pair.long);

    // Update items first
    setShortItems(sortedShortItems);
    setLongItems(sortedLongItems);

    // Then update Excel data using the extract functions
    setShortTableData(sortedShortItems.map(extractShortFields));
    setLongTableData(sortedLongItems.map(extractLongFields));
  };

  // Combined function to load both Excel data and pairs from database
  const loadAllDataFromDatabase = async () => {
    try {
      // First load Excel data
      const excelResult = await loadExcelDataFromDatabase();

      if (excelResult.success) {
        // Then load pairs
        await loadPairsFromDatabase();
        console.log(
          "Successfully loaded both Excel data and pairs from database"
        );
      } else {
        console.warn(
          "Excel data load failed, but continuing with pairs load:",
          excelResult.message
        );
        // Still try to load pairs even if Excel data fails
        await loadPairsFromDatabase();
      }
    } catch (error) {
      console.error("Error in combined data load:", error);
      // Fallback to just loading pairs
      await loadPairsFromDatabase();
    }
  };

  // Function to update stock symbols on the server
  const updateStockSymbols = async () => {
    const serverUrl = getServerUrl();
    try {
      setUpdateStatus("Update in progress...");

      // Collect all ticker symbols from both short and long data
      const shortTickers = [
        ...shortLoadedTableData,
        ...shortOpenTableData,
        ...shortClosedTableData,
      ]
        .map((item) => item.ticker)
        .filter((ticker) => ticker && ticker.trim() !== "");

      const longTickers = [
        ...longLoadedTableData,
        ...longOpenTableData,
        ...longClosedTableData,
      ]
        .map((item) => item.ticker)
        .filter((ticker) => ticker && ticker.trim() !== "");

      // Combine and remove duplicates
      const allTickers = [...new Set([...shortTickers, ...longTickers])];

      if (allTickers.length === 0) {
        setUpdateStatus("No symbols found to update");
        return;
      }

      console.log("Symbols to send to server:", allTickers);

      // First, test if the server is responding
      try {
        const testUrl = `${serverUrl}/api/test`;
        setUpdateStatus(`Verifying server connection: ${testUrl}...`);

        const testResponse = await fetch(testUrl);
        if (!testResponse.ok) {
          setUpdateStatus(
            `Error: The server is not responding correctly. Code: ${testResponse.status}`
          );
          return;
        }

        const testData = await testResponse.json();
        console.log("Test server response:", testData);
        setUpdateStatus(
          `Server connected. Current symbols: ${testData.currentSymbols}. Sending new symbols...`
        );
      } catch (testError) {
        console.error("Error in connection test:", testError);
        setUpdateStatus(
          `Connection error: ${testError.message}. Make sure the server is running on ${serverUrl}`
        );
        return;
      }

      // Send to server
      const url = `${serverUrl}/api/update-stock-symbols`;

      try {
        const accessToken = await retrieveAccessToken();
        const customerId = await retrieveCustomerId();
        const correlId = await retrieveCorrelId();

        // Get user session for email
        const session = await fetch("/api/auth/session");
        const sessionData = await session.json();
        const userEmail = sessionData?.user?.email;

        if (!userEmail) {
          console.warn(
            "No user email found in session, symbols will not be associated with user account"
          );
        }

        const response = await fetch(url, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            symbols: allTickers,
            token: accessToken,
            clientCustomerId: customerId,
            clientCorrelId: correlId,
            userEmail: userEmail,
          }),
          credentials: "include",
        });

        // Check if response is JSON
        const contentType = response.headers.get("content-type");
        if (!contentType || !contentType.includes("application/json")) {
          const textResponse = await response.text();
          console.error("Server returned non-JSON response:", textResponse);
          setUpdateStatus(
            `Error: The server returned an invalid response. Make sure the server is running on ${serverUrl}`
          );
          return;
        }

        const data = await response.json();

        if (response.ok) {
          setUpdateStatus(`Symbols updated successfully: ${data.symbols}`);
        } else {
          setUpdateStatus(`Error: ${data.error || "Unknown error"}`);
        }
      } catch (fetchError) {
        console.error("Error in fetch request:", fetchError);
        setUpdateStatus(
          `Connection error: ${fetchError.message}. Make sure the server is running on ${serverUrl}`
        );
      }
    } catch (error) {
      console.error("Error updating symbols:", error);
      setUpdateStatus(`Error: ${error.message || "Unknown error"}`);
    }
  };

  // Auto-load data from database if no local data exists
  useEffect(() => {
    const checkAndLoadData = async () => {
      // Check if we have any data in localStorage
      const hasLocalData =
        localStorage.getItem("shortOpenTableData") ||
        localStorage.getItem("shortLoadedTableData") ||
        localStorage.getItem("longOpenTableData") ||
        localStorage.getItem("longLoadedTableData") ||
        localStorage.getItem("shortClosedTableData") ||
        localStorage.getItem("longClosedTableData");

      // If no local data exists, try to load from database
      if (!hasLocalData) {
        console.log("No local data found, attempting to load from database...");
        try {
          await loadAllDataFromDatabase();
        } catch (error) {
          console.error("Failed to auto-load data from database:", error);
        }
      } else {
        console.log("Local data found, using cached data");
      }
    };

    // Only run this check after a short delay to ensure contexts are initialized
    const timer = setTimeout(checkAndLoadData, 1000);
    return () => clearTimeout(timer);
  }, []); // Empty dependency array - this should only run once on mount

  // Move logic for all directions
  function handleMovePair(section, pairId, idx, destination) {
    let longItem, shortItem;

    // Use index to get both items from their respective arrays
    if (section === "Loaded") {
      longItem = longLoadedTableData[idx];
      shortItem = shortLoadedTableData[idx];
    } else if (section === "Open") {
      longItem = longOpenTableData[idx];
      shortItem = shortOpenTableData[idx];
    } else if (section === "Closed") {
      longItem = longClosedTableData[idx];
      shortItem = shortClosedTableData[idx];
    }
    if (!longItem || !shortItem) {
      console.error("Pair not found for move");
      return;
    }

    // Determine new status
    let newStatus;
    if (destination === "Open") newStatus = "WB_OpenPositions";
    else if (destination === "Loaded") newStatus = "WB_LoadedPairs";
    else if (destination === "Closed") newStatus = "WB_ClosedPositions";

    console.log("✅ Moving pair:", {
      section,
      idx,
      destination,
      newStatus,
      longId: longItem.id,
      shortId: shortItem.id,
    });

    try {
      // Update individual component statuses
      updateLongStatus(longItem.id, newStatus);
      updateShortStatus(shortItem.id, newStatus);

      // Also update the pair status in pairArray
      setPairArray((prevPairArray) => {
        return prevPairArray.map((pair) => {
          // Find the pair that matches our components
          if (
            pair.longComponent?.id === longItem.id &&
            pair.shortComponent?.id === shortItem.id
          ) {
            console.log(
              "🔄 Updating pair status from",
              pair.status,
              "to",
              newStatus
            );
            return {
              ...pair,
              status: newStatus,
              longComponent: {
                ...pair.longComponent,
                statusValue: newStatus,
              },
              shortComponent: {
                ...pair.shortComponent,
                statusValue: newStatus,
              },
            };
          }
          return pair;
        });
      });

      console.log("✅ Status updates completed successfully");
    } catch (error) {
      console.error("❌ Error updating status:", error);
    }

    setMoveDialog({ open: false, section: null, idx: null, id: null });
  }
  // Helper to get valid move destinations
  function getMoveDestinations(section) {
    if (section === "Loaded") return ["Open", "Closed"];
    if (section === "Open") return ["Loaded", "Closed"];
    if (section === "Closed") return ["Open", "Loaded"];
    return [];
  }

  // Function to handle edit action
  const handleEditPair = (pairItem, idx, section, tableType) => {
    // Get both Long and Short Excel data for the same pair
    let longExcelData, shortExcelData;

    switch (section) {
      case "Open":
        longExcelData = longOpenTableData[idx];
        shortExcelData = shortOpenTableData[idx];
        break;
      case "Loaded":
        longExcelData = longLoadedTableData[idx];
        shortExcelData = shortLoadedTableData[idx];
        break;
      case "Closed":
        longExcelData = longClosedTableData[idx];
        shortExcelData = shortClosedTableData[idx];
        break;
      default:
        return;
    }

    if (!longExcelData || !shortExcelData) {
      console.error("No Excel data found for index:", idx);
      return;
    }

    setEditDialog({
      open: true,
      section,
      idx,
      id: pairItem.id, // Keep pair ID for reference
      longData: { ...longExcelData }, // Clone the Long Excel data
      shortData: { ...shortExcelData }, // Clone the Short Excel data
      tableType, // Keep for reference but now we edit both sides
    });
  };

  function handleDeletePair(e, longId, idx, section = "loaded") {
    e.stopPropagation();

    // Determina quale tabella usare in base alla sezione
    let longTableData, shortTableData, longItems, shortItems;
    let setLongTableData, setShortTableData, setLongItems, setShortItems;

    switch (section) {
      case "loaded":
        longTableData = longLoadedTableData;
        shortTableData = shortLoadedTableData;
        longItems = loadedLongItems;
        shortItems = loadedShortItems;
        setLongTableData = setLongLoadedTableData;
        setShortTableData = setShortLoadedTableData;
        setLongItems = setLoadedLongItems;
        setShortItems = setLoadedShortItems;
        break;
      case "open":
        longTableData = longOpenTableData;
        shortTableData = shortOpenTableData;
        longItems = openLongItems;
        shortItems = openShortItems;
        setLongTableData = setLongOpenTableData;
        setShortTableData = setShortOpenTableData;
        setLongItems = setOpenLongItems;
        setShortItems = setOpenShortItems;
        break;
      case "closed":
        longTableData = longClosedTableData;
        shortTableData = shortClosedTableData;
        longItems = closedLongItems;
        shortItems = closedShortItems;
        setLongTableData = setLongClosedTableData;
        setShortTableData = setShortClosedTableData;
        setLongItems = setClosedLongItems;
        setShortItems = setClosedShortItems;
        break;
      default:
        console.error(`Unknown section: ${section}`);
        return;
    }

    // Get the long item by ID
    const longItem = longTableData.find((item) => item.id === longId);
    if (!longItem) {
      console.error(
        `Long item with ID ${longId} not found in ${section} section`
      );
      return;
    }

    // Find the corresponding short item by index
    const shortItem = shortTableData[idx];
    if (!shortItem) {
      console.error(
        `Short item at index ${idx} not found in ${section} section`
      );
      return;
    }

    console.log(
      `Deleting pair from ${section}: Long ID=${longId}, Short ID=${shortItem.id}`
    );

    // Remove items from Excel data
    setShortTableData((prev) =>
      prev.filter((item) => item.id !== shortItem.id)
    );
    setLongTableData((prev) => prev.filter((item) => item.id !== longId));

    // Update the UI
    setShortItems((prev) => prev.filter((_, i) => i !== idx));
    setLongItems((prev) => prev.filter((_, i) => i !== idx));

    // IMPORTANT: Also update the pairArray to ensure database sync
    const pairKey = longItems[idx]?.pairKey;

    console.log(
      `Before deletion from ${section} - pairArray length:`,
      pairArray.length
    );

    if (pairKey) {
      console.log(`Removing pair with key ${pairKey} from pairArray`);
      setPairArray((prev) => {
        const newArray = prev.filter((pair) => pair.key !== pairKey);
        console.log(
          `After deletion from ${section} - pairArray length:`,
          newArray.length
        );
        return newArray;
      });
    } else {
      // Fallback to filtering by component IDs if pairKey is not available
      console.log(
        `Removing pair with longId=${longId} and shortId=${shortItem.id} from pairArray`
      );
      setPairArray((prev) => {
        const newArray = prev.filter(
          (pair) =>
            !(
              pair.longComponent.id === longId &&
              pair.shortComponent.id === shortItem.id
            )
        );
        console.log(
          `After deletion from ${section} - pairArray length:`,
          newArray.length
        );
        return newArray;
      });
    }
  }

  // EDITING: Funzioni per gestire l'editing dei dati
  const initializeEditingData = () => {
    // Usa i dati già con gli override applicati
    const allPairs = [...loadedPairs, ...openPairs];
    const editingData = allPairs.map((pair) => ({
      key: pair.key,
      status: pair.status,
      shortComponent: {
        id: pair.shortComponent.id,
        ticker: pair.shortComponent.ticker || "",
        formattedBid: pair.shortComponent.formattedBid || "",
        formattedAsk: pair.shortComponent.formattedAsk || "",
        formattedLast: pair.shortComponent.formattedLast || "",
        formattedChange: pair.shortComponent.formattedChange || "",
        formattedVolume: pair.shortComponent.formattedVolume || "",
        formattedDividend: pair.shortComponent.formattedDividend || "",
        exDateValue: pair.shortComponent.exDateValue || "",
        spreadValue: pair.shortComponent.spreadValue || "",
        formattedSpreadUser: pair.shortComponent.formattedSpreadUser || "",
        formattedCost: pair.shortComponent.formattedCost || "",
        formattedAmt: pair.shortComponent.formattedAmt || "",
        expectedQuantity: pair.shortComponent.expectedQuantity || "",
        dollarCost: pair.shortComponent.dollarCost || "",
      },
      longComponent: {
        id: pair.longComponent.id,
        ticker: pair.longComponent.ticker || "",
        formattedBid: pair.longComponent.formattedBid || "",
        formattedAsk: pair.longComponent.formattedAsk || "",
        formattedLast: pair.longComponent.formattedLast || "",
        formattedChange: pair.longComponent.formattedChange || "",
        formattedVolume: pair.longComponent.formattedVolume || "",
        formattedDividend: pair.longComponent.formattedDividend || "",
        exDateValue: pair.longComponent.exDateValue || "",
        spreadValue: pair.longComponent.spreadValue || "",
        formattedSpreadUser: pair.longComponent.formattedSpreadUser || "",
        formattedCost: pair.longComponent.formattedCost || "",
        formattedAmt: pair.longComponent.formattedAmt || "",
        expectedQuantity: pair.longComponent.expectedQuantity || "",
        dollarCost: pair.longComponent.dollarCost || "",
      },
    }));
    setEditingPairs(editingData);
    setShowEditingSection(true);
    setHasUnsavedChanges(false);
  };

  const updateEditingValue = (pairIndex, component, field, value) => {
    setEditingPairs((prev) => {
      const newPairs = [...prev];
      newPairs[pairIndex][component][field] = value;
      return newPairs;
    });
    setHasUnsavedChanges(true);
  };

  const applyEditingChanges = () => {
    // Salva le modifiche negli override persistenti
    const newOverrides = { ...persistentOverrides };

    editingPairs.forEach((editingPair) => {
      const originalPair = pairArray.find((p) => p.key === editingPair.key);
      if (originalPair) {
        // Calcola solo i campi che sono stati effettivamente modificati
        const shortOverrides = {};
        const longOverrides = {};

        Object.keys(editingPair.shortComponent).forEach((key) => {
          if (
            editingPair.shortComponent[key] !==
            (originalPair.shortComponent[key] || "")
          ) {
            shortOverrides[key] = editingPair.shortComponent[key];
          }
        });

        Object.keys(editingPair.longComponent).forEach((key) => {
          if (
            editingPair.longComponent[key] !==
            (originalPair.longComponent[key] || "")
          ) {
            longOverrides[key] = editingPair.longComponent[key];
          }
        });

        if (
          Object.keys(shortOverrides).length > 0 ||
          Object.keys(longOverrides).length > 0
        ) {
          newOverrides[editingPair.key] = {
            shortComponent: {
              ...newOverrides[editingPair.key]?.shortComponent,
              ...shortOverrides,
            },
            longComponent: {
              ...newOverrides[editingPair.key]?.longComponent,
              ...longOverrides,
            },
          };
        }
      }
    });

    setPersistentOverrides(newOverrides);
    setHasUnsavedChanges(false);
    alert(
      "Changes applied successfully! They will persist across drag & drop operations."
    );
  };

  const cancelEditing = () => {
    setShowEditingSection(false);
    setEditingPairs([]);
    setHasUnsavedChanges(false);
  };

  const resetAllOverrides = () => {
    if (
      confirm(
        "Are you sure you want to reset all custom data modifications? This will restore original values."
      )
    ) {
      setPersistentOverrides({});
      alert("All custom modifications have been reset!");
    }
  };

  // AUTO-CLOSE: Funzione per controllare e spostare pair con 0 shares (esclude pair fake)
  // TEMPORARILY COMMENTED OUT - This was causing pairs to auto-move to closed when moved to open
  // See WB_Dashboard_Auto_Close_Logic_Notes.md for details
  /*
  const checkAndMoveZeroSharesPairs = () => {
    if (!pairArray || pairArray.length === 0) return;

    const pairsToMove = [];
    const updatedPairArray = pairArray.map((pair) => {
      if (pair.status === "WB_OpenPositions") {
        const longShares = parseFloat(pair.longComponent?.formattedAmt || "0");
        const shortShares = parseFloat(
          pair.shortComponent?.formattedAmt || "0"
        );

        // Controlla se entrambi i ticker sono "FAKE"
        const longTicker = pair.longComponent?.ticker || "";
        const shortTicker = pair.shortComponent?.ticker || "";
        const isFakePair =
          longTicker.toUpperCase() === "FAKE" &&
          shortTicker.toUpperCase() === "FAKE";

        // Se entrambi i componenti hanno 0 shares E non è una pair finta, sposta a closed
        if (longShares === 0 && shortShares === 0 && !isFakePair) {
          pairsToMove.push(pair.key);
          return {
            ...pair,
            status: "WB_ClosedPositions",
            longComponent: {
              ...pair.longComponent,
              statusValue: "WB_ClosedPositions",
            },
            shortComponent: {
              ...pair.shortComponent,
              statusValue: "WB_ClosedPositions",
            },
          };
        }
      }
      return pair;
    });

    if (pairsToMove.length > 0) {
      setPairArray(updatedPairArray);
      console.log(
        `Auto-moved ${pairsToMove.length} pair(s) to Closed Positions:`,
        pairsToMove
      );

      // Aggiorna anche gli status nei dati Excel per mantenere la sincronizzazione
      pairsToMove.forEach((pairKey) => {
        const pair = updatedPairArray.find((p) => p.key === pairKey);
        if (pair) {
          // Aggiorna lo status nei dati Excel usando le nuove funzioni per closed
          updateLongClosedStatus(pair.longComponent.id, "WB_ClosedPositions");
          updateShortClosedStatus(pair.shortComponent.id, "WB_ClosedPositions");
        }
      });
    }
  };
  */

  // AUTO-CLOSE: useEffect per monitorare i cambiamenti negli openPairs
  // TEMPORARILY COMMENTED OUT - This was causing pairs to auto-move to closed when moved to open
  // See WB_Dashboard_Auto_Close_Logic_Notes.md for details
  // useEffect(() => {
  //   checkAndMoveZeroSharesPairs();
  // }, [openPairs]); // Monitora i cambiamenti negli openPairs

  // HIGHLIGHT: Funzione per determinare se una cella deve essere evidenziata
  const shouldHighlightZeroShares = (value) => {
    const numValue = parseFloat(value || "0");
    return numValue === 0;
  };

  // FAKE PAIR: Funzione per creare una pair finta usando lo stesso sistema di configuration
  const createFakePair = () => {
    // Calcola gli ID esistenti per evitare conflitti
    const existingDataShort =
      shortLoadedTableData.length +
        shortOpenTableData.length +
        shortClosedTableData.length || 0;
    const existingDataLong =
      longLoadedTableData.length +
        longOpenTableData.length +
        longClosedTableData.length || 0;

    // Generate fake SHORT component data
    const fakeShortComponent = {
      ticker: "FAKE-S",
      shares: "100",
      sector: "Technology",
      spread: "0.50",
      volume: "1000",
      status: "WB_LoadedPairs",
      dividend: "0",
      id: existingDataShort.toString(),
    };

    // Generate fake LONG component data
    const fakeLongComponent = {
      ticker: "FAKE-L",
      shares: "100",
      sector: "Technology",
      spread: "0.50",
      volume: "1000",
      status: "WB_LoadedPairs",
      dividend: "0",
      id: existingDataLong.toString(),
    };

    // Add to individual table data
    setShortLoadedTableData((prev) => {
      const newData = [...prev, fakeShortComponent];
      console.log("Updated shortLoadedTableData:", newData.length, "items");
      return newData;
    });
    setLongLoadedTableData((prev) => {
      const newData = [...prev, fakeLongComponent];
      console.log("Updated longLoadedTableData:", newData.length, "items");
      return newData;
    });

    // Create the actual pair and add it to pairArray
    const pairId = pairArray.length + 1;
    const pairKey = `${fakeShortComponent.ticker}-${fakeLongComponent.ticker}-${timestamp}`;
    const pnlLong = parseFloat(fakeLongComponent.formattedPnl || 0);
    const pnlShort = parseFloat(fakeShortComponent.formattedPnl || 0);
    const combinedPNL = (pnlLong + pnlShort).toFixed(2);

    const newPair = {
      id: pairId,
      key: pairKey,
      longComponent: fakeLongComponent,
      shortComponent: fakeShortComponent,
      combinedPNL: combinedPNL,
      status: "WB_LoadedPairs",
    };

    setPairArray((prev) => {
      const newPairArray = [...prev, newPair];
      console.log("Updated pairArray:", newPairArray.length, "pairs");
      return newPairArray;
    });

    console.log("✅ Fake pair created successfully!");
    console.log("Pair details:", newPair);

    // Force a small delay to ensure the data propagates through the contexts
    setTimeout(() => {
      console.log("Fake pair should now be visible in tables and localStorage");
    }, 100);
  };

  return (
    <ProtectedRoute>
      <div className='relative min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50 dark:from-black dark:via-gray-900/30 dark:to-gray-800/20'>
        {/* Beams Background */}
        <div className='absolute inset-0 overflow-hidden pointer-events-none'>
          <canvas
            ref={canvasRef}
            className='absolute inset-0 opacity-30 dark:opacity-20'
            style={{ filter: "blur(15px)" }}
          />
        </div>
        {/* Discrete Controls Section */}
        <div className='relative z-1 w-full px-2 py-2'>
          <div className='inline-block bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700 p-2 mb-1'>
            <button
              onClick={() => setControlsExpanded(!controlsExpanded)}
              className='flex items-center justify-between w-full text-left'
            >
              <h3 className='text-sm font-medium text-gray-700 dark:text-gray-300'>
                Controls
              </h3>
              <svg
                className={`w-4 h-4 text-gray-500 transition-transform duration-200 ${
                  controlsExpanded ? "rotate-180" : ""
                }`}
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M19 9l-7 7-7-7'
                />
              </svg>
            </button>

            {controlsExpanded && (
              <div className='mt-3'>
                <div className='flex flex-wrap gap-2'>
                  <button
                    className={`${
                      isSaving
                        ? "bg-gray-400 dark:bg-gray-600 cursor-not-allowed"
                        : "bg-green-500 hover:bg-green-600 dark:bg-green-600 dark:hover:bg-green-700"
                    } text-white text-sm font-medium py-1.5 px-3 rounded transition-colors`}
                    onClick={savePairsToDatabase}
                    disabled={isSaving}
                  >
                    {isSaving ? "Saving..." : "Save to Database"}
                  </button>

                  <button
                    className={`${
                      isLoading
                        ? "bg-gray-400 dark:bg-gray-600 cursor-not-allowed"
                        : "bg-blue-500 hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700"
                    } text-white text-sm font-medium py-1.5 px-3 rounded transition-colors`}
                    onClick={loadAllDataFromDatabase}
                    disabled={isLoading}
                  >
                    {isLoading ? "Loading..." : "Load from Database"}
                  </button>

                  {/* Update Symbols button */}
                  <button
                    className='bg-emerald-500 hover:bg-emerald-600 dark:bg-emerald-600 dark:hover:bg-emerald-700 text-white text-sm font-medium py-1.5 px-3 rounded transition-colors flex items-center gap-2'
                    onClick={updateStockSymbols}
                    title='Update stock symbols on the server for real-time market data synchronization'
                  >
                    <svg
                      xmlns='http://www.w3.org/2000/svg'
                      className='h-4 w-4'
                      fill='none'
                      viewBox='0 0 24 24'
                      stroke='currentColor'
                    >
                      <path
                        strokeLinecap='round'
                        strokeLinejoin='round'
                        strokeWidth={2}
                        d='M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15'
                      />
                    </svg>
                    Update Symbols
                  </button>

                  <button
                    className='bg-orange-500 hover:bg-orange-600 dark:bg-orange-600 dark:hover:bg-orange-700 text-white text-sm font-medium py-1.5 px-3 rounded transition-colors'
                    onClick={() => {
                      console.log("Add Placeholder Pair button clicked!");
                      createFakePair();
                    }}
                    title='Create a fake pair with dummy data for testing'
                  >
                    Add Placeholder Pair
                  </button>

                  {Object.keys(persistentOverrides).length > 0 && (
                    <button
                      className='bg-red-500 hover:bg-red-600 dark:bg-red-600 dark:hover:bg-red-700 text-white text-sm font-medium py-1.5 px-3 rounded transition-colors'
                      onClick={resetAllOverrides}
                    >
                      Reset Custom Data
                    </button>
                  )}
                </div>

                {/* Discrete Status Messages */}
                <div className='flex flex-wrap gap-2 mt-3'>
                  {Object.keys(persistentOverrides).length > 0 && (
                    <div className='px-2 py-1 rounded bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 text-xs'>
                      {Object.keys(persistentOverrides).length} pair(s) with
                      custom data
                    </div>
                  )}

                  {saveStatus && (
                    <div
                      className={`px-2 py-1 rounded text-xs ${
                        saveStatus.includes("Error")
                          ? "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300"
                          : "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300"
                      }`}
                    >
                      {saveStatus}
                    </div>
                  )}

                  {loadStatus && (
                    <div
                      className={`px-2 py-1 rounded text-xs ${
                        loadStatus.includes("Error")
                          ? "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300"
                          : "bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300"
                      }`}
                    >
                      {loadStatus}
                    </div>
                  )}

                  {updateStatus && (
                    <div
                      className={`px-2 py-1 rounded text-xs ${
                        updateStatus.includes("Error")
                          ? "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300"
                          : "bg-emerald-100 dark:bg-emerald-900/30 text-emerald-800 dark:text-emerald-300"
                      }`}
                    >
                      {updateStatus}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Dashboard Content - Full width for tables */}
        <div className='relative z-1 w-full px-2 pb-8'>
          <div className='space-y-6'>
            {/* EDITING Section */}
            {showEditingSection && (
              <div className='mb-8 p-6 bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg'>
                <div className='flex justify-between items-center mb-4'>
                  <h2 className='text-xl font-semibold text-purple-800 dark:text-purple-200'>
                    🛠️ Edit Pair Data
                  </h2>
                  <div className='flex gap-2'>
                    <button
                      className={`${
                        hasUnsavedChanges
                          ? "bg-green-500 hover:bg-green-700"
                          : "bg-gray-400"
                      } text-white font-bold py-1 px-3 rounded transition-colors`}
                      onClick={applyEditingChanges}
                      disabled={!hasUnsavedChanges}
                    >
                      Apply Changes
                    </button>
                    <button
                      className='bg-gray-500 hover:bg-gray-700 text-white font-bold py-1 px-3 rounded transition-colors'
                      onClick={cancelEditing}
                    >
                      Cancel
                    </button>
                  </div>
                </div>

                {hasUnsavedChanges && (
                  <div className='mb-4 p-2 bg-yellow-100 dark:bg-yellow-900/30 border border-yellow-300 dark:border-yellow-700 rounded text-yellow-800 dark:text-yellow-200 text-sm'>
                    ⚠️ You have unsaved changes. Click "Apply Changes" to save
                    them.
                  </div>
                )}

                <div className='space-y-6'>
                  {editingPairs.map((pair, pairIndex) => (
                    <div
                      key={pair.key}
                      className='bg-white dark:bg-gray-900 p-4 rounded-lg border border-gray-200 dark:border-gray-700'
                    >
                      <h3 className='text-lg font-medium mb-3 text-gray-800 dark:text-gray-200'>
                        Pair {pairIndex + 1} -{" "}
                        {pair.status === "WB_OpenPositions" ? "OPEN" : "LOADED"}
                      </h3>

                      <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
                        {/* SHORT Component */}
                        <div className='bg-red-50 dark:bg-red-900/20 p-4 rounded border border-red-200 dark:border-red-800'>
                          <h4 className='font-medium text-red-800 dark:text-red-200 mb-3'>
                            SHORT Component
                          </h4>
                          <div className='grid grid-cols-2 gap-3'>
                            <div>
                              <label className='block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1'>
                                Ticker
                              </label>
                              <input
                                type='text'
                                value={pair.shortComponent.ticker}
                                onChange={(e) =>
                                  updateEditingValue(
                                    pairIndex,
                                    "shortComponent",
                                    "ticker",
                                    e.target.value
                                  )
                                }
                                className='w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100'
                              />
                            </div>
                            <div>
                              <label className='block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1'>
                                Bid
                              </label>
                              <input
                                type='text'
                                value={pair.shortComponent.formattedBid}
                                onChange={(e) =>
                                  updateEditingValue(
                                    pairIndex,
                                    "shortComponent",
                                    "formattedBid",
                                    e.target.value
                                  )
                                }
                                className='w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100'
                              />
                            </div>
                            <div>
                              <label className='block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1'>
                                Ask
                              </label>
                              <input
                                type='text'
                                value={pair.shortComponent.formattedAsk}
                                onChange={(e) =>
                                  updateEditingValue(
                                    pairIndex,
                                    "shortComponent",
                                    "formattedAsk",
                                    e.target.value
                                  )
                                }
                                className='w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100'
                              />
                            </div>
                            <div>
                              <label className='block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1'>
                                Last
                              </label>
                              <input
                                type='text'
                                value={pair.shortComponent.formattedLast}
                                onChange={(e) =>
                                  updateEditingValue(
                                    pairIndex,
                                    "shortComponent",
                                    "formattedLast",
                                    e.target.value
                                  )
                                }
                                className='w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100'
                              />
                            </div>
                            <div>
                              <label className='block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1'>
                                Change
                              </label>
                              <input
                                type='text'
                                value={pair.shortComponent.formattedChange}
                                onChange={(e) =>
                                  updateEditingValue(
                                    pairIndex,
                                    "shortComponent",
                                    "formattedChange",
                                    e.target.value
                                  )
                                }
                                className='w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100'
                              />
                            </div>
                            <div>
                              <label className='block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1'>
                                Volume
                              </label>
                              <input
                                type='text'
                                value={pair.shortComponent.formattedVolume}
                                onChange={(e) =>
                                  updateEditingValue(
                                    pairIndex,
                                    "shortComponent",
                                    "formattedVolume",
                                    e.target.value
                                  )
                                }
                                className='w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100'
                              />
                            </div>
                            <div>
                              <label className='block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1'>
                                Dividend
                              </label>
                              <input
                                type='text'
                                value={pair.shortComponent.formattedDividend}
                                onChange={(e) =>
                                  updateEditingValue(
                                    pairIndex,
                                    "shortComponent",
                                    "formattedDividend",
                                    e.target.value
                                  )
                                }
                                className='w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100'
                              />
                            </div>
                            <div>
                              <label className='block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1'>
                                Ex Date
                              </label>
                              <input
                                type='text'
                                value={pair.shortComponent.exDateValue}
                                onChange={(e) =>
                                  updateEditingValue(
                                    pairIndex,
                                    "shortComponent",
                                    "exDateValue",
                                    e.target.value
                                  )
                                }
                                className='w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100'
                              />
                            </div>
                            <div>
                              <label className='block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1'>
                                Spread
                              </label>
                              <input
                                type='text'
                                value={pair.shortComponent.spreadValue}
                                onChange={(e) =>
                                  updateEditingValue(
                                    pairIndex,
                                    "shortComponent",
                                    "spreadValue",
                                    e.target.value
                                  )
                                }
                                className='w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100'
                              />
                            </div>
                            <div>
                              <label className='block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1'>
                                Shares
                              </label>
                              <input
                                type='text'
                                value={pair.shortComponent.formattedAmt}
                                onChange={(e) =>
                                  updateEditingValue(
                                    pairIndex,
                                    "shortComponent",
                                    "formattedAmt",
                                    e.target.value
                                  )
                                }
                                className='w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100'
                              />
                            </div>
                          </div>
                        </div>

                        {/* LONG Component */}
                        <div className='bg-green-50 dark:bg-green-900/20 p-4 rounded border border-green-200 dark:border-green-800'>
                          <h4 className='font-medium text-green-800 dark:text-green-200 mb-3'>
                            LONG Component
                          </h4>
                          <div className='grid grid-cols-2 gap-3'>
                            <div>
                              <label className='block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1'>
                                Ticker
                              </label>
                              <input
                                type='text'
                                value={pair.longComponent.ticker}
                                onChange={(e) =>
                                  updateEditingValue(
                                    pairIndex,
                                    "longComponent",
                                    "ticker",
                                    e.target.value
                                  )
                                }
                                className='w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100'
                              />
                            </div>
                            <div>
                              <label className='block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1'>
                                Bid
                              </label>
                              <input
                                type='text'
                                value={pair.longComponent.formattedBid}
                                onChange={(e) =>
                                  updateEditingValue(
                                    pairIndex,
                                    "longComponent",
                                    "formattedBid",
                                    e.target.value
                                  )
                                }
                                className='w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100'
                              />
                            </div>
                            <div>
                              <label className='block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1'>
                                Ask
                              </label>
                              <input
                                type='text'
                                value={pair.longComponent.formattedAsk}
                                onChange={(e) =>
                                  updateEditingValue(
                                    pairIndex,
                                    "longComponent",
                                    "formattedAsk",
                                    e.target.value
                                  )
                                }
                                className='w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100'
                              />
                            </div>
                            <div>
                              <label className='block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1'>
                                Last
                              </label>
                              <input
                                type='text'
                                value={pair.longComponent.formattedLast}
                                onChange={(e) =>
                                  updateEditingValue(
                                    pairIndex,
                                    "longComponent",
                                    "formattedLast",
                                    e.target.value
                                  )
                                }
                                className='w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100'
                              />
                            </div>
                            <div>
                              <label className='block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1'>
                                Change
                              </label>
                              <input
                                type='text'
                                value={pair.longComponent.formattedChange}
                                onChange={(e) =>
                                  updateEditingValue(
                                    pairIndex,
                                    "longComponent",
                                    "formattedChange",
                                    e.target.value
                                  )
                                }
                                className='w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100'
                              />
                            </div>
                            <div>
                              <label className='block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1'>
                                Volume
                              </label>
                              <input
                                type='text'
                                value={pair.longComponent.formattedVolume}
                                onChange={(e) =>
                                  updateEditingValue(
                                    pairIndex,
                                    "longComponent",
                                    "formattedVolume",
                                    e.target.value
                                  )
                                }
                                className='w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100'
                              />
                            </div>
                            <div>
                              <label className='block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1'>
                                Dividend
                              </label>
                              <input
                                type='text'
                                value={pair.longComponent.formattedDividend}
                                onChange={(e) =>
                                  updateEditingValue(
                                    pairIndex,
                                    "longComponent",
                                    "formattedDividend",
                                    e.target.value
                                  )
                                }
                                className='w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100'
                              />
                            </div>
                            <div>
                              <label className='block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1'>
                                Ex Date
                              </label>
                              <input
                                type='text'
                                value={pair.longComponent.exDateValue}
                                onChange={(e) =>
                                  updateEditingValue(
                                    pairIndex,
                                    "longComponent",
                                    "exDateValue",
                                    e.target.value
                                  )
                                }
                                className='w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100'
                              />
                            </div>
                            <div>
                              <label className='block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1'>
                                Spread
                              </label>
                              <input
                                type='text'
                                value={pair.longComponent.spreadValue}
                                onChange={(e) =>
                                  updateEditingValue(
                                    pairIndex,
                                    "longComponent",
                                    "spreadValue",
                                    e.target.value
                                  )
                                }
                                className='w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100'
                              />
                            </div>
                            <div>
                              <label className='block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1'>
                                Shares
                              </label>
                              <input
                                type='text'
                                value={pair.longComponent.formattedAmt}
                                onChange={(e) =>
                                  updateEditingValue(
                                    pairIndex,
                                    "longComponent",
                                    "formattedAmt",
                                    e.target.value
                                  )
                                }
                                className='w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100'
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Open Pairs Section */}
            <div className='mb-8'>
              <h2 className='text-xl font-semibold mb-4 pb-2 border-b dark:border-gray-700 dark:text-gray-200'>
                Open Positions
              </h2>
              <div className='flex flex-nowrap gap-0'>
                {/* Open SHORT Table */}
                <div className='flex-auto min-w-0'>

                  <DndContext
                    collisionDetection={closestCenter}
                    onDragEnd={handleDragEndOpenShort}
                  >
                    <SortableContext
                      items={openShortItems.map((_, idx) => idx.toString())}
                      strategy={verticalListSortingStrategy}
                    >
                      <div className='bg-black overflow-hidden'>
                        <table className='w-full caption-bottom text-sm table-fixed'>
                          <thead className='[&_tr]:border-b dark:[&_tr]:border-gray-700'>
                            <tr
                              style={{ background: "#f8d7da" }}
                              className='dark:bg-red-800 dark:bg-opacity-90'
                            >
                              <SortableHeader
                                section="open"
                                field="cost"
                                side="short"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium'
                              >
                                Cost
                              </SortableHeader>
                              <SortableHeader
                                section="open"
                                field="shares"
                                side="short"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium'
                              >
                                Sh
                              </SortableHeader>
                              <SortableHeader
                                section="open"
                                field="ticker"
                                side="short"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium'
                              >
                                Sym
                              </SortableHeader>
                              <SortableHeader
                                section="open"
                                field="bid"
                                side="short"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium'
                              >
                                Bid
                              </SortableHeader>
                              <SortableHeader
                                section="open"
                                field="ask"
                                side="short"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium'
                              >
                                Ask
                              </SortableHeader>
                              <SortableHeader
                                section="open"
                                field="last"
                                side="short"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium'
                              >
                                Last
                              </SortableHeader>
                              <SortableHeader
                                section="open"
                                field="change"
                                side="short"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium'
                              >
                                Chg
                              </SortableHeader>
                              <SortableHeader
                                section="open"
                                field="volume"
                                side="short"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium'
                              >
                                Vol
                              </SortableHeader>
                              <SortableHeader
                                section="open"
                                field="dividend"
                                side="short"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium'
                              >
                                Div
                              </SortableHeader>
                              <SortableHeader
                                section="open"
                                field="exdate"
                                side="short"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium'
                              >
                                Xd
                              </SortableHeader>
                              <SortableHeader
                                section="open"
                                field="spread"
                                side="short"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium'
                              >
                                S
                              </SortableHeader>
                              <th className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-white dark:text-white-800 font-medium bg-blue-700'>
                                O
                              </th>
                            </tr>
                          </thead>
                          <tbody className='dark:text-gray-300'>
                            {openShortItems.map((item, idx) => (
                              <ShortSortableRow
                                key={idx}
                                id={idx}
                                item={item}
                              />
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </SortableContext>
                  </DndContext>
                </div>
                {/* Open LONG Table */}
                <div className='flex-auto min-w-0'>

                  <DndContext
                    collisionDetection={closestCenter}
                    onDragEnd={handleDragEndOpenLong}
                  >
                    <SortableContext
                      items={openLongItems.map((_, idx) => idx.toString())}
                      strategy={verticalListSortingStrategy}
                    >
                      <div className='bg-black overflow-hidden'>
                        <table className='w-full caption-bottom text-sm table-fixed'>
                          <thead className='[&_tr]:border-b dark:[&_tr]:border-gray-700'>
                            <tr
                              style={{ background: "#d4edda" }}
                              className='dark:bg-green-800 dark:bg-opacity-90'
                            >
                              <SortableHeader
                                section="open"
                                field="spread"
                                side="long"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium'
                              >
                                S
                              </SortableHeader>
                              <SortableHeader
                                section="open"
                                field="exdate"
                                side="long"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium'
                              >
                                Xd
                              </SortableHeader>
                              <SortableHeader
                                section="open"
                                field="cost"
                                side="long"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium'
                              >
                                Cost
                              </SortableHeader>
                              <SortableHeader
                                section="open"
                                field="shares"
                                side="long"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium'
                              >
                                Sh
                              </SortableHeader>
                              <SortableHeader
                                section="open"
                                field="ticker"
                                side="long"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium'
                              >
                                Sym
                              </SortableHeader>
                              <SortableHeader
                                section="open"
                                field="bid"
                                side="long"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium'
                              >
                                Bid
                              </SortableHeader>
                              <SortableHeader
                                section="open"
                                field="ask"
                                side="long"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium'
                              >
                                Ask
                              </SortableHeader>
                              <SortableHeader
                                section="open"
                                field="last"
                                side="long"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium'
                              >
                                Last
                              </SortableHeader>
                              <SortableHeader
                                section="open"
                                field="change"
                                side="long"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium'
                              >
                                Chg
                              </SortableHeader>
                              <SortableHeader
                                section="open"
                                field="volume"
                                side="long"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium'
                              >
                                Vol
                              </SortableHeader>
                              <SortableHeader
                                section="open"
                                field="dividend"
                                side="long"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium'
                              >
                                Div
                              </SortableHeader>
                            </tr>
                          </thead>
                          <tbody className='dark:text-gray-300'>
                            {openLongItems.map((item, idx) => (
                              <LongSortableRow key={idx} id={idx} item={item} />
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </SortableContext>
                  </DndContext>
                </div>
                {/* Actions table for Open Pairs */}
                <div
                  className='w-28'
                  style={{ minWidth: "7rem", width: "7rem" }}
                >

                  <div className='bg-black overflow-hidden'>
                    <table className='w-full caption-bottom text-sm table-fixed'>
                      <thead className='[&_tr]:border-b dark:[&_tr]:border-gray-700'>
                        <tr
                          style={{ background: "#e2e3e5" }}
                          className='dark:bg-gray-700'
                        >
                          <th className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center dark:text-black font-medium'>
                            Actions
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {openLongItems.map((item, idx) => (
                          <tr
                            key={idx}
                            className='border-b dark:border-gray-700 transition-colors bg-black hover:bg-gray-900'
                          >
                            <td
                              className='border dark:border-gray-700 px-0 py-0 h-7 text-base w-28'
                              style={{ minWidth: "7rem", width: "7rem" }}
                            >
                              <div className='flex justify-center space-x-2 py-0.5 w-full'>
                                <button
                                  className='bg-blue-500 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-800 text-white text-sm py-0.5 px-1.5 rounded transition-colors'
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleEditPair(item, idx, "Open", "long");
                                  }}
                                  title='Edit Data'
                                >
                                  <svg
                                    xmlns='http://www.w3.org/2000/svg'
                                    className='h-3 w-3'
                                    fill='none'
                                    viewBox='0 0 24 24'
                                    stroke='currentColor'
                                  >
                                    <path
                                      strokeLinecap='round'
                                      strokeLinejoin='round'
                                      strokeWidth={2}
                                      d='M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z'
                                    />
                                  </svg>
                                </button>
                                <button
                                  className='bg-green-500 hover:bg-green-700 dark:bg-green-600 dark:hover:bg-green-800 text-white text-sm py-0.5 px-1.5 rounded transition-colors'
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setMoveDialog({
                                      open: true,
                                      section: "Open",
                                      idx,
                                      id: item.id,
                                    });
                                  }}
                                  title='Move Pair'
                                >
                                  <svg
                                    xmlns='http://www.w3.org/2000/svg'
                                    className='h-3 w-3'
                                    fill='none'
                                    viewBox='0 0 24 24'
                                    stroke='currentColor'
                                  >
                                    <path
                                      strokeLinecap='round'
                                      strokeLinejoin='round'
                                      strokeWidth={2.5}
                                      d='M5 10l7-7m0 0l7 7m-7-7v18'
                                    />
                                  </svg>
                                </button>
                                <button
                                  className='bg-red-500 hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-800 text-white text-sm py-0.5 px-1.5 rounded transition-colors'
                                  onClick={(e) =>
                                    handleDeletePair(e, item.id, idx, "open")
                                  }
                                  title='Delete Pair'
                                >
                                  <svg
                                    xmlns='http://www.w3.org/2000/svg'
                                    className='h-3 w-3'
                                    fill='none'
                                    viewBox='0 0 24 24'
                                    stroke='currentColor'
                                  >
                                    <path
                                      strokeLinecap='round'
                                      strokeLinejoin='round'
                                      strokeWidth={2}
                                      d='M6 18L18 6M6 6l12 12'
                                    />
                                  </svg>
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
                {/* Pair Summary table: only in Open Pairs section, next to Open LONG */}
                <div className='flex-auto min-w-0 max-w-[450px] flex flex-col justify-stretch'>

                  <div className='bg-black overflow-x-auto rounded-md shadow h-full flex-1 flex flex-col justify-stretch'>
                    <table className='w-full caption-bottom text-sm table-fixed'>
                      <thead className='[&_tr]:border-b dark:[&_tr]:border-gray-700'>
                        <tr
                          style={{ background: "#4875a5ff" }}
                          className='dark:bg-blue-900 dark:bg-opacity-80'
                        >
                          <th className='border-r border-l dark:border-gray-700 px-1 py-1 text-xs text-center'>
                            S P/L
                          </th>
                          <th className='border-r border-l dark:border-gray-700 px-1 py-1 text-xs text-center'>
                            Div Adj
                          </th>
                          <th className='border-r border-l dark:border-gray-700 px-1 py-1 text-xs text-center'>
                            L P/L
                          </th>
                          <th className='border-r border-l dark:border-gray-700 px-1 py-1 text-xs text-center'>
                            Combined PNL
                          </th>
                          <th className='border-r border-l dark:border-gray-700 px-1 py-1 text-xs text-center'>
                            NQW
                          </th>
                          <th className='border-r border-l dark:border-gray-700 px-1 py-1 text-xs text-center'>
                            NQM
                          </th>
                          <th className='border-r border-l dark:border-gray-700 px-1 py-1 text-xs text-center'>
                            NQB
                          </th>
                        </tr>
                      </thead>
                      <tbody className='dark:text-gray-300'>
                        {pairArray && pairArray.length > 0 ? (
                          pairArray
                            .filter(
                              (pair) => pair.status === "WB_OpenPositions"
                            )
                            .map((pair, idx) => {
                              const short = pair.shortComponent || {};
                              const long = pair.longComponent || {};

                              // PNL now calculated correctly in PairComponent.js
                              const shortPNL = Math.round(
                                parseFloat(short.formattedPnl || 0)
                              );
                              const longPNL = Math.round(
                                parseFloat(long.formattedPnl || 0)
                              );
                              const combinedPNL = (shortPNL + longPNL).toFixed(
                                2
                              );
                              const shortUserDiv = Math.round(parseFloat(short.formattedUserDividend || 0));
                              const longUserDiv = Math.round(parseFloat(long.formattedUserDividend || 0));
                              const combinedUserDiv = shortUserDiv + longUserDiv;
                              const NQW = Math.round(
                                parseFloat(short.formattedAmt|| 0) * parseFloat(short.formattedAsk || 0) +
                                parseFloat(long.formattedAmt|| 0) * parseFloat(long.formattedBid || 0)
                              );
                              const NQM = Math.round(
                                parseFloat(short.formattedAmt|| 0) * ((parseFloat(short.formattedBid || 0) + parseFloat(short.formattedAsk || 0)) / 2)+
                                parseFloat(long.formattedAmt|| 0) * ((parseFloat(long.formattedBid || 0) + parseFloat(long.formattedAsk || 0)) / 2)
                              );
                              const NQB = Math.round(
                                parseFloat(short.formattedAmt|| 0) * parseFloat(short.formattedBid || 0) +
                                parseFloat(long.formattedAmt|| 0) * parseFloat(long.formattedAsk || 0)
                              );
                              return (
                                <tr
                                  key={pair.key || idx}
                                  className='border-b dark:border-gray-700 h-7 align-middle'
                                >
                                  <td className='px-1 py-0.5 text-base-condensed text-right text-red-500 dark:text-red-400'>
                                    {shortPNL || "-"}
                                  </td>
                                  <td className='px-1 py-0.5 text-base-condensed text-right text-blue-700 dark:text-blue-300'>
                                    <TooltipProvider delayDuration={100}>
                                      <Tooltip>
                                        <TooltipTrigger asChild>
                                          <span className="cursor-help">
                                            {combinedUserDiv || "-"}
                                          </span>
                                        </TooltipTrigger>
                                        <TooltipContent side="top">
                                          <div className="text-sm">
                                            <div>Short Div Adj: {shortUserDiv || 0}</div>
                                            <div>Long Div Adj: {longUserDiv || 0}</div>
                                            <div className="border-t pt-1 mt-1 font-semibold">
                                              Total: {combinedUserDiv || 0}
                                            </div>
                                          </div>
                                        </TooltipContent>
                                      </Tooltip>
                                    </TooltipProvider>
                                  </td>
                                  <td className='px-1 py-0.5 text-base-condensed text-right text-green-500 dark:text-green-400'>
                                    {longPNL || "-"}
                                  </td>
                                  <td className='px-1 py-0.5 text-base-condensed text-right'>
                                    {combinedPNL || "-"}
                                  </td>
                                  <td className='px-1 py-0.5 text-base-condensed text-right'>
                                    {NQW || "-"}
                                  </td>
                                  <td className='px-1 py-0.5 text-base-condensed text-right'>
                                    {NQM || "-"}
                                  </td>
                                  <td className='px-1 py-0.5 text-base-condensed text-right'>
                                    {NQB || "-"}
                                  </td>
                                  
                                </tr>
                              );
                            })
                        ) : (
                          <tr>
                            <td
                              colSpan={7}
                              className='text-center py-2 text-base-condensed text-gray-400'
                            >
                              No pairs available
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
            {/* end of open pairs */}

            {/* Loaded Pairs Section */}
            <div className='mb-8'>
              <h2 className='text-xl font-semibold mb-4 pb-2 border-b dark:border-gray-700 dark:text-gray-200'>
                
              </h2>
              <div className='flex flex-nowrap gap-0'>
                <div className='flex-auto min-w-0'>

                  <DndContext
                    collisionDetection={closestCenter}
                    onDragEnd={handleDragEndLoadedShort}
                  >
                    <SortableContext
                      items={loadedShortItems.map((_, idx) => idx.toString())}
                      strategy={verticalListSortingStrategy}
                    >
                      <div className='bg-black overflow-hidden'>
                        <table className='w-full caption-bottom text-sm table-fixed'>
                          <thead className='[&_tr]:border-b dark:[&_tr]:border-gray-700'>
                            <tr
                              style={{ background: "#f8d7da" }}
                              className='dark:bg-red-800 dark:bg-opacity-90'
                            >
                              <SortableHeader
                                section="loaded"
                                field="cost"
                                side="short"
                                className='border dark:border-gray-700 px-1 py-0 h-8 text-sm text-center text-red-800 dark:text-red-800 font-medium'
                              >
                                Cost
                              </SortableHeader>
                              <SortableHeader
                                section="loaded"
                                field="shares"
                                side="short"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium'
                              >
                                Sh
                              </SortableHeader>
                              <SortableHeader
                                section="loaded"
                                field="ticker"
                                side="short"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium'
                              >
                                Sym
                              </SortableHeader>
                              <SortableHeader
                                section="loaded"
                                field="bid"
                                side="short"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium'
                              >
                                Bid
                              </SortableHeader>
                              <SortableHeader
                                section="loaded"
                                field="ask"
                                side="short"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium'
                              >
                                Ask
                              </SortableHeader>
                              <SortableHeader
                                section="loaded"
                                field="last"
                                side="short"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium'
                              >
                                Last
                              </SortableHeader>
                              <SortableHeader
                                section="loaded"
                                field="change"
                                side="short"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium'
                              >
                                Chg
                              </SortableHeader>
                              <SortableHeader
                                section="loaded"
                                field="volume"
                                side="short"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium'
                              >
                                Vol
                              </SortableHeader>
                              <SortableHeader
                                section="loaded"
                                field="dividend"
                                side="short"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium'
                              >
                                Div
                              </SortableHeader>
                              <SortableHeader
                                section="loaded"
                                field="exdate"
                                side="short"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium'
                              >
                                Xd
                              </SortableHeader>
                              <SortableHeader
                                section="loaded"
                                field="spread"
                                side="short"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium'
                              >
                                S
                              </SortableHeader>
                              <th className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-white dark:text-white-800 font-medium bg-blue-700'>
                                P
                              </th>
                            </tr>
                          </thead>
                          <tbody className='dark:text-gray-300'>
                            {loadedShortItems.map((item, idx) => (
                              <ShortSortableRow
                                key={idx}
                                id={idx}
                                item={item}
                              />
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </SortableContext>
                  </DndContext>
                </div>
                <div className='flex-auto min-w-0'>

                  <DndContext
                    collisionDetection={closestCenter}
                    onDragEnd={handleDragEndLoadedLong}
                  >
                    <SortableContext
                      items={loadedLongItems.map((_, idx) => idx.toString())}
                      strategy={verticalListSortingStrategy}
                    >
                      <div className='bg-black overflow-hidden'>
                        <table className='w-full caption-bottom text-sm table-fixed'>
                          <thead className='[&_tr]:border-b dark:[&_tr]:border-gray-700'>
                            <tr
                              style={{ background: "#d4edda" }}
                              className='dark:bg-green-800 dark:bg-opacity-90'
                            >
                              <SortableHeader
                                section="loaded"
                                field="spread"
                                side="long"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium'
                              >
                                S
                              </SortableHeader>
                              <SortableHeader
                                section="loaded"
                                field="exdate"
                                side="long"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium'
                              >
                                Xd
                              </SortableHeader>
                              <SortableHeader
                                section="loaded"
                                field="cost"
                                side="long"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium'
                              >
                                Cost
                              </SortableHeader>
                              <SortableHeader
                                section="loaded"
                                field="shares"
                                side="long"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium'
                              >
                                Sh
                              </SortableHeader>
                              <SortableHeader
                                section="loaded"
                                field="ticker"
                                side="long"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium'
                              >
                                Sym
                              </SortableHeader>
                              <SortableHeader
                                section="loaded"
                                field="bid"
                                side="long"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium'
                              >
                                Bid
                              </SortableHeader>
                              <SortableHeader
                                section="loaded"
                                field="ask"
                                side="long"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium'
                              >
                                Ask
                              </SortableHeader>
                              <SortableHeader
                                section="loaded"
                                field="last"
                                side="long"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium'
                              >
                                Last
                              </SortableHeader>
                              <SortableHeader
                                section="loaded"
                                field="change"
                                side="long"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium'
                              >
                                Chg
                              </SortableHeader>
                              <SortableHeader
                                section="loaded"
                                field="volume"
                                side="long"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium'
                              >
                                Vol
                              </SortableHeader>
                              <SortableHeader
                                section="loaded"
                                field="dividend"
                                side="long"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium'
                              >
                                Div
                              </SortableHeader>
                            </tr>
                          </thead>
                          <tbody className='dark:text-gray-300'>
                            {loadedLongItems.map((item, idx) => (
                              <LongSortableRow key={idx} id={idx} item={item} />
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </SortableContext>
                  </DndContext>
                </div>
                <div
                  className='w-28'
                  style={{ minWidth: "7rem", width: "7rem" }}
                >

                  <div className='bg-black overflow-hidden'>
                    <table className='w-full caption-bottom text-sm table-fixed'>
                      <thead className='[&_tr]:border-b dark:[&_tr]:border-gray-700'>
                        <tr
                          style={{ background: "#e2e3e5" }}
                          className='dark:bg-gray-700'
                        >
                          <th className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center dark:text-black font-medium'>
                            Actions
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {loadedLongItems.map((item, idx) => (
                          <tr
                            key={idx}
                            className='border-b dark:border-gray-700 transition-colors bg-black hover:bg-gray-900'
                          >
                            <td
                              className='border dark:border-gray-700 px-0 py-0 h-7 text-base w-28'
                              style={{ minWidth: "7rem", width: "7rem" }}
                            >
                              <div className='flex justify-center space-x-2 py-0.5 w-full'>
                                <button
                                  className='bg-blue-500 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-800 text-white text-sm py-0.5 px-1.5 rounded transition-colors'
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleEditPair(item, idx, "Loaded", "long");
                                  }}
                                  title='Edit Data'
                                >
                                  <svg
                                    xmlns='http://www.w3.org/2000/svg'
                                    className='h-3 w-3'
                                    fill='none'
                                    viewBox='0 0 24 24'
                                    stroke='currentColor'
                                  >
                                    <path
                                      strokeLinecap='round'
                                      strokeLinejoin='round'
                                      strokeWidth={2}
                                      d='M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z'
                                    />
                                  </svg>
                                </button>
                                <button
                                  className='bg-green-500 hover:bg-green-700 dark:bg-green-600 dark:hover:bg-green-800 text-white text-sm py-0.5 px-1.5 rounded transition-colors'
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setMoveDialog({
                                      open: true,
                                      section: "Loaded",
                                      idx,
                                      id: item.id,
                                    });
                                  }}
                                  title='Move Pair'
                                >
                                  <svg
                                    xmlns='http://www.w3.org/2000/svg'
                                    className='h-3 w-3'
                                    fill='none'
                                    viewBox='0 0 24 24'
                                    stroke='currentColor'
                                  >
                                    <path
                                      strokeLinecap='round'
                                      strokeLinejoin='round'
                                      strokeWidth={2.5}
                                      d='M5 10l7-7m0 0l7 7m-7-7v18'
                                    />
                                  </svg>
                                </button>
                                <button
                                  className='bg-red-500 hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-800 text-white text-sm py-0.5 px-1.5 rounded transition-colors'
                                  onClick={(e) =>
                                    handleDeletePair(e, item.id, idx, "loaded")
                                  }
                                  title='Delete Pair'
                                >
                                  <svg
                                    xmlns='http://www.w3.org/2000/svg'
                                    className='h-3 w-3'
                                    fill='none'
                                    viewBox='0 0 24 24'
                                    stroke='currentColor'
                                  >
                                    <path
                                      strokeLinecap='round'
                                      strokeLinejoin='round'
                                      strokeWidth={2}
                                      d='M6 18L18 6M6 6l12 12'
                                    />
                                  </svg>
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
                {/* Pair Summary table for Loaded Pairs */}
                <div className='flex-auto min-w-0 max-w-[450px] flex flex-col justify-stretch'>

                  <div className='bg-black overflow-x-auto rounded-md shadow h-full flex-1 flex flex-col justify-stretch'>
                    <table className='w-full caption-bottom text-sm table-fixed'>
                      <thead className='[&_tr]:border-b dark:[&_tr]:border-gray-700'>
                        <tr
                          style={{ background: "#4875a5ff" }}
                          className='dark:bg-blue-900 dark:bg-opacity-80'
                        >
                          <th className='border-r border-l dark:border-gray-700 px-1 py-1 text-xs text-center'>
                            S P/L
                          </th>
                          <th className='border-r border-l dark:border-gray-700 px-1 py-1 text-xs text-center'>
                            Div Adj
                          </th>
                          <th className='border-r border-l dark:border-gray-700 px-1 py-1 text-xs text-center'>
                            L P/L
                          </th>
                          <th className='border-r border-l dark:border-gray-700 px-1 py-1 text-xs text-center'>
                            Combined PNL
                          </th>
                          <th className='border-r border-l dark:border-gray-700 px-1 py-1 text-xs text-center'>
                            NQW
                          </th>
                          <th className='border-r border-l dark:border-gray-700 px-1 py-1 text-xs text-center'>
                            NQM
                          </th>
                          <th className='border-r border-l dark:border-gray-700 px-1 py-1 text-xs text-center'>
                            NQB
                          </th>
                        </tr>
                      </thead>
                      <tbody className='dark:text-gray-300'>
                        {pairArray && pairArray.length > 0 ? (
                          pairArray
                            .filter((pair) => pair.status === "WB_LoadedPairs")
                            .map((pair, idx) => {
                              const short = pair.shortComponent || {};
                              const long = pair.longComponent || {};

                              // PNL now calculated correctly in PairComponent.js
                              const shortPNL = Math.round(
                                parseFloat(short.formattedPnl || 0)
                              );
                              const longPNL = Math.round(
                                parseFloat(long.formattedPnl || 0)
                              );
                              const combinedPNL = (shortPNL + longPNL).toFixed(
                                2
                              );
                              const shortUserDiv = Math.round(parseFloat(short.formattedUserDividend || 0));
                              const longUserDiv = Math.round(parseFloat(long.formattedUserDividend || 0));
                              const combinedUserDiv = shortUserDiv + longUserDiv;
                              const NQW = Math.round(
                                parseFloat(short.expectedQuantity|| 0) * parseFloat(short.formattedAsk || 0) +
                                parseFloat(long.expectedQuantity|| 0) * parseFloat(long.formattedBid || 0)
                              );
                              const NQM = Math.round(
                                parseFloat(short.expectedQuantity|| 0) * ((parseFloat(short.formattedBid || 0) + parseFloat(short.formattedAsk || 0)) / 2)+
                                parseFloat(long.expectedQuantity|| 0) * ((parseFloat(long.formattedBid || 0) + parseFloat(long.formattedAsk || 0)) / 2)
                              );
                              const NQB = Math.round(
                                parseFloat(short.expectedQuantity|| 0) * parseFloat(short.formattedBid || 0) +
                                parseFloat(long.expectedQuantity|| 0) * parseFloat(long.formattedAsk || 0)
                              );
                              return (
                                <tr
                                  key={pair.key || idx}
                                  className='border-b dark:border-gray-700 h-7 align-middle'
                                >
                                  <td className='px-1 py-0.5 text-base-condensed text-right text-red-500 dark:text-red-400'>
                                    {shortPNL || "-"}
                                  </td>
                                  <td className='px-1 py-0.5 text-base-condensed text-right text-blue-700 dark:text-blue-300'>
                                    <TooltipProvider delayDuration={100}>
                                      <Tooltip>
                                        <TooltipTrigger asChild>
                                          <span className="cursor-help">
                                            {combinedUserDiv || "-"}
                                          </span>
                                        </TooltipTrigger>
                                        <TooltipContent side="top">
                                          <div className="text-sm">
                                            <div>Short Div Adj: {shortUserDiv || 0}</div>
                                            <div>Long Div Adj: {longUserDiv || 0}</div>
                                            <div className="border-t pt-1 mt-1 font-semibold">
                                              Total: {combinedUserDiv || 0}
                                            </div>
                                          </div>
                                        </TooltipContent>
                                      </Tooltip>
                                    </TooltipProvider>
                                  </td>
                                  <td className='px-1 py-0.5 text-base-condensed text-right text-green-500 dark:text-green-400'>
                                    {longPNL || "-"}
                                  </td>
                                  <td className='px-1 py-0.5 text-base-condensed text-right'>
                                    {combinedPNL || "-"}
                                  </td>
                                  <td className='px-1 py-0.5 text-base-condensed text-right'>
                                    {NQW || "-"}
                                  </td>
                                  <td className='px-1 py-0.5 text-base-condensed text-right'>
                                    {NQM || "-"}
                                  </td>
                                  <td className='px-1 py-0.5 text-base-condensed text-right'>
                                    {NQB || "-"}
                                  </td>
                                </tr>
                              );
                            })
                        ) : (
                          <tr>
                            <td
                              colSpan={7}
                              className='text-center py-2 text-base-condensed text-gray-400'
                            >
                              No pairs available
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
            {/* end of loaded pairs */}

            {/* Closed Pairs Section */}
            <div className='mb-8'>
              <h2 className='text-xl font-semibold mb-4 pb-2 border-b dark:border-gray-700 dark:text-gray-200'>
                
              </h2>
              <div className='flex flex-nowrap gap-0'>
                <div className='flex-auto min-w-0'>
                  <DndContext
                    collisionDetection={closestCenter}
                    onDragEnd={handleDragEndClosedShort}
                  >
                    <SortableContext
                      items={closedShortItems.map((_, idx) => idx.toString())}
                      strategy={verticalListSortingStrategy}
                    >
                      <div className='bg-black overflow-hidden'>
                        <table className='w-full caption-bottom text-sm table-fixed'>
                          <thead className='[&_tr]:border-b dark:[&_tr]:border-gray-700'>
                            <tr
                              style={{ background: "#f8d7da" }}
                              className='dark:bg-red-800 dark:bg-opacity-90'
                            >
                              <SortableHeader
                                section="closed"
                                field="cost"
                                side="short"
                                className='border dark:border-gray-700 px-1 py-0 h-8 text-sm text-center text-red-800 dark:text-red-800 font-medium'
                              >
                                Cost
                              </SortableHeader>
                              <SortableHeader
                                section="closed"
                                field="shares"
                                side="short"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium'
                              >
                                Sh
                              </SortableHeader>
                              <SortableHeader
                                section="closed"
                                field="ticker"
                                side="short"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium'
                              >
                                Sym
                              </SortableHeader>
                              <SortableHeader
                                section="closed"
                                field="bid"
                                side="short"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium'
                              >
                                Bid
                              </SortableHeader>
                              <SortableHeader
                                section="closed"
                                field="ask"
                                side="short"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium'
                              >
                                Ask
                              </SortableHeader>
                              <SortableHeader
                                section="closed"
                                field="last"
                                side="short"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium'
                              >
                                Last
                              </SortableHeader>
                              <SortableHeader
                                section="closed"
                                field="change"
                                side="short"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium'
                              >
                                Chg
                              </SortableHeader>
                              <SortableHeader
                                section="closed"
                                field="volume"
                                side="short"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium'
                              >
                                Vol
                              </SortableHeader>
                              <SortableHeader
                                section="closed"
                                field="dividend"
                                side="short"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium'
                              >
                                Div
                              </SortableHeader>
                              <SortableHeader
                                section="closed"
                                field="exdate"
                                side="short"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium'
                              >
                                Xd
                              </SortableHeader>
                              <SortableHeader
                                section="closed"
                                field="spread"
                                side="short"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-red-800 dark:text-red-800 font-medium'
                              >
                                S
                              </SortableHeader>
                              <th className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-white dark:text-white-800 font-medium bg-blue-700'>
                                P
                              </th>
                            </tr>
                          </thead>
                          <tbody className='dark:text-gray-300'>
                            {closedShortItems.map((item, idx) => (
                              <ShortSortableRow
                                key={idx}
                                id={idx}
                                item={item}
                              />
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </SortableContext>
                  </DndContext>
                </div>
                <div className='flex-auto min-w-0'>

                  <DndContext
                    collisionDetection={closestCenter}
                    onDragEnd={handleDragEndClosedLong}
                  >
                    <SortableContext
                      items={closedLongItems.map((_, idx) => idx.toString())}
                      strategy={verticalListSortingStrategy}
                    >
                      <div className='bg-black overflow-hidden'>
                        <table className='w-full caption-bottom text-sm table-fixed'>
                          <thead className='[&_tr]:border-b dark:[&_tr]:border-gray-700'>
                            <tr
                              style={{ background: "#d4edda" }}
                              className='dark:bg-green-800 dark:bg-opacity-90'
                            >
                              <SortableHeader
                                section="closed"
                                field="spread"
                                side="long"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium'
                              >
                                S
                              </SortableHeader>
                              <SortableHeader
                                section="closed"
                                field="exdate"
                                side="long"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium'
                              >
                                Xd
                              </SortableHeader>
                              <SortableHeader
                                section="closed"
                                field="cost"
                                side="long"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium'
                              >
                                Cost
                              </SortableHeader>
                              <SortableHeader
                                section="closed"
                                field="shares"
                                side="long"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium'
                              >
                                Sh
                              </SortableHeader>
                              <SortableHeader
                                section="closed"
                                field="ticker"
                                side="long"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium'
                              >
                                Sym
                              </SortableHeader>
                              <SortableHeader
                                section="closed"
                                field="bid"
                                side="long"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium'
                              >
                                Bid
                              </SortableHeader>
                              <SortableHeader
                                section="closed"
                                field="ask"
                                side="long"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium'
                              >
                                Ask
                              </SortableHeader>
                              <SortableHeader
                                section="closed"
                                field="last"
                                side="long"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium'
                              >
                                Last
                              </SortableHeader>
                              <SortableHeader
                                section="closed"
                                field="change"
                                side="long"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium'
                              >
                                Chg
                              </SortableHeader>
                              <SortableHeader
                                section="closed"
                                field="volume"
                                side="long"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium'
                              >
                                Vol
                              </SortableHeader>
                              <SortableHeader
                                section="closed"
                                field="dividend"
                                side="long"
                                className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center text-green-800 dark:text-green-800 font-medium'
                              >
                                Div
                              </SortableHeader>
                            </tr>
                          </thead>
                          <tbody className='dark:text-gray-300'>
                            {closedLongItems.map((item, idx) => (
                              <LongSortableRow key={idx} id={idx} item={item} />
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </SortableContext>
                  </DndContext>
                </div>
                <div
                  className='w-28'
                  style={{ minWidth: "7rem", width: "7rem" }}
                >

                  <div className='bg-black overflow-hidden'>
                    <table className='w-full caption-bottom text-sm table-fixed'>
                      <thead className='[&_tr]:border-b dark:[&_tr]:border-gray-700'>
                        <tr
                          style={{ background: "#e2e3e5" }}
                          className='dark:bg-gray-700'
                        >
                          <th className='border dark:border-gray-700 px-1 py-2 h-10 text-sm text-center dark:text-black font-medium'>
                            Actions
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {closedLongItems.map((item, idx) => (
                          <tr
                            key={idx}
                            className='border-b dark:border-gray-700 transition-colors bg-black hover:bg-gray-900'
                          >
                            <td className='border dark:border-gray-700 px-0 py-0 h-7 text-base'>
                              <div className='flex justify-center space-x-1 py-0.5'>
                                <button
                                  className='bg-blue-500 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-800 text-white text-sm py-0.5 px-1.5 rounded transition-colors'
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleEditPair(item, idx, "Closed", "long");
                                  }}
                                  title='Edit Data'
                                >
                                  <svg
                                    xmlns='http://www.w3.org/2000/svg'
                                    className='h-3 w-3'
                                    fill='none'
                                    viewBox='0 0 24 24'
                                    stroke='currentColor'
                                  >
                                    <path
                                      strokeLinecap='round'
                                      strokeLinejoin='round'
                                      strokeWidth={2}
                                      d='M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z'
                                    />
                                  </svg>
                                </button>
                                <button
                                  className='bg-green-500 hover:bg-green-700 dark:bg-green-600 dark:hover:bg-green-800 text-white text-sm py-0.5 px-1.5 rounded transition-colors'
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setMoveDialog({
                                      open: true,
                                      section: "Closed",
                                      idx,
                                      id: item.id,
                                    });
                                  }}
                                  title='Move Pair'
                                >
                                  <svg
                                    xmlns='http://www.w3.org/2000/svg'
                                    className='h-3 w-3'
                                    fill='none'
                                    viewBox='0 0 24 24'
                                    stroke='currentColor'
                                  >
                                    <path
                                      strokeLinecap='round'
                                      strokeLinejoin='round'
                                      strokeWidth={2.5}
                                      d='M5 10l7-7m0 0l7 7m-7-7v18'
                                    />
                                  </svg>
                                </button>
                                <button
                                  className='bg-red-500 hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-800 text-white text-sm py-0.5 px-1.5 rounded transition-colors'
                                  onClick={(e) =>
                                    handleDeletePair(e, item.id, idx, "closed")
                                  }
                                  title='Delete Pair'
                                >
                                  <svg
                                    xmlns='http://www.w3.org/2000/svg'
                                    className='h-3 w-3'
                                    fill='none'
                                    viewBox='0 0 24 24'
                                    stroke='currentColor'
                                  >
                                    <path
                                      strokeLinecap='round'
                                      strokeLinejoin='round'
                                      strokeWidth={2}
                                      d='M6 18L18 6M6 6l12 12'
                                    />
                                  </svg>
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
                {/* Pair Summary table for Closed Pairs */}
                <div className='flex-auto min-w-0 max-w-[450px] flex flex-col justify-stretch'>

                  <div className='bg-black overflow-x-auto rounded-md shadow h-full flex-1 flex flex-col justify-stretch'>
                    <table className='w-full caption-bottom text-sm table-fixed'>
                      <thead className='[&_tr]:border-b dark:[&_tr]:border-gray-700'>
                        <tr
                          style={{ background: "#4875a5ff" }}
                          className='dark:bg-blue-900 dark:bg-opacity-80'
                        >
                          <th className='border-r border-l dark:border-gray-700 px-1 py-1 text-xs text-center'>
                            S P/L
                          </th>
                          <th className='border-r border-l dark:border-gray-700 px-1 py-1 text-xs text-center'>
                            Div Adj
                          </th>
                          <th className='border-r border-l dark:border-gray-700 px-1 py-1 text-xs text-center'>
                            L P/L
                          </th>
                          <th className='border-r border-l dark:border-gray-700 px-1 py-1 text-xs text-center'>
                            Combined PNL
                          </th>
                          <th className='border-r border-l dark:border-gray-700 px-1 py-1 text-xs text-center'>
                            NQW
                          </th>
                          <th className='border-r border-l dark:border-gray-700 px-1 py-1 text-xs text-center'>
                            NQM
                          </th>
                          <th className='border-r border-l dark:border-gray-700 px-1 py-1 text-xs text-center'>
                            NQB
                          </th>
                        </tr>
                      </thead>
                      <tbody className='dark:text-gray-300'>
                        {pairArray && pairArray.length > 0 ? (
                          pairArray
                            .filter(
                              (pair) => pair.status === "WB_ClosedPositions"
                            )
                            .map((pair, idx) => {
                              const short = pair.shortComponent || {};
                              const long = pair.longComponent || {};

                              // PNL now calculated correctly in PairComponent.js
                              const shortPNL = Math.round(
                                parseFloat(short.formattedPnl || 0)
                              );
                              const longPNL = Math.round(
                                parseFloat(long.formattedPnl || 0)
                              );
                              const combinedPNL = (shortPNL + longPNL).toFixed(
                                2
                              );
                              const shortUserDiv = Math.round(parseFloat(short.formattedUserDividend || 0));
                              const longUserDiv = Math.round(parseFloat(long.formattedUserDividend || 0));
                              const combinedUserDiv = shortUserDiv + longUserDiv;
                              const NQW = Math.round(
                                parseFloat(short.formattedAmt|| 0) * parseFloat(short.formattedAsk || 0) +
                                parseFloat(long.formattedAmt|| 0) * parseFloat(long.formattedBid || 0)
                              );
                              const NQM = Math.round(
                                parseFloat(short.formattedAmt|| 0) * ((parseFloat(short.formattedBid || 0) + parseFloat(short.formattedAsk || 0)) / 2)+
                                parseFloat(long.formattedAmt|| 0) * ((parseFloat(long.formattedBid || 0) + parseFloat(long.formattedAsk || 0)) / 2)
                              );
                              const NQB = Math.round(
                                parseFloat(short.formattedAmt|| 0) * parseFloat(short.formattedBid || 0) +
                                parseFloat(long.formattedAmt|| 0) * parseFloat(long.formattedAsk || 0)
                              );
                              return (
                                <tr
                                  key={pair.key || idx}
                                  className='border-b dark:border-gray-700 h-7 align-middle'
                                >
                                  <td className='px-1 py-0.5 text-base-condensed text-right text-red-500 dark:text-red-400'>
                                    {shortPNL || "-"}
                                  </td>
                                  <td className='px-1 py-0.5 text-base-condensed text-right text-blue-700 dark:text-blue-300'>
                                    <TooltipProvider delayDuration={100}>
                                      <Tooltip>
                                        <TooltipTrigger asChild>
                                          <span className="cursor-help">
                                            {combinedUserDiv || "-"}
                                          </span>
                                        </TooltipTrigger>
                                        <TooltipContent side="top">
                                          <div className="text-sm">
                                            <div>Short Div Adj: {shortUserDiv || 0}</div>
                                            <div>Long Div Adj: {longUserDiv || 0}</div>
                                            <div className="border-t pt-1 mt-1 font-semibold">
                                              Total: {combinedUserDiv || 0}
                                            </div>
                                          </div>
                                        </TooltipContent>
                                      </Tooltip>
                                    </TooltipProvider>
                                  </td>
                                  <td className='px-1 py-0.5 text-base-condensed text-right text-green-500 dark:text-green-400'>
                                    {longPNL || "-"}
                                  </td>
                                  <td className='px-1 py-0.5 text-base-condensed text-right'>
                                    {combinedPNL || "-"}
                                  </td>
                                  <td className='px-1 py-0.5 text-base-condensed text-right'>
                                    {NQW || "-"}
                                  </td>
                                  <td className='px-1 py-0.5 text-base-condensed text-right'>
                                    {NQM || "-"}
                                  </td>
                                  <td className='px-1 py-0.5 text-base-condensed text-right'>
                                    {NQB || "-"}
                                  </td>
                                </tr>
                              );
                            })
                        ) : (
                          <tr>
                            <td
                              colSpan={7}
                              className='text-center py-2 text-base-condensed text-gray-400'
                            >
                              No pairs available
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
            {/* end of closed pairs */}

            {/* Edit Data Dialog */}
            <EditDataDialog
              editDialog={editDialog}
              setEditDialog={setEditDialog}
              shortOpenTableData={shortOpenTableData}
              shortLoadedTableData={shortLoadedTableData}
              shortClosedTableData={shortClosedTableData}
              longOpenTableData={longOpenTableData}
              longLoadedTableData={longLoadedTableData}
              longClosedTableData={longClosedTableData}
              setShortOpenTableData={setShortOpenTableData}
              setShortLoadedTableData={setShortLoadedTableData}
              setShortClosedTableData={setShortClosedTableData}
              setLongOpenTableData={setLongOpenTableData}
              setLongLoadedTableData={setLongLoadedTableData}
              setLongClosedTableData={setLongClosedTableData}
            />

            {/* Move Pair Dialog - render once at the root, not inside table rows */}
            <Dialog
              open={moveDialog.open}
              onOpenChange={(open) => setMoveDialog((v) => ({ ...v, open }))}
            >
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Move Pair</DialogTitle>
                </DialogHeader>
                <div className='mb-4'>Choose destination for this pair:</div>
                <div className='flex flex-col gap-2'>
                  {moveDialog.open &&
                    getMoveDestinations(moveDialog.section).map((dest) => (
                      <button
                        key={dest}
                        className='w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 rounded'
                        onClick={() =>
                          handleMovePair(
                            moveDialog.section,
                            moveDialog.id,
                            moveDialog.idx,
                            dest
                          )
                        }
                      >
                        Move to {dest}
                      </button>
                    ))}
                </div>
                <DialogFooter>
                  <DialogClose asChild>
                    <button className='mt-2 w-full border border-gray-300 rounded py-2'>
                      Cancel
                    </button>
                  </DialogClose>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
