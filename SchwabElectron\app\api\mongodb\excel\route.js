"use server";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth-options";
import { saveExcelData, getExcelData, getUserByEmail } from "@/lib/prisma-dal";
import { validateInput, excelDataSchema, sanitizeArrayData } from "@/lib/validation";

export async function POST(request) {
  try {
    // Get the current user session
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Get the user from the database
    const user = await getUserByEmail(session.user.email);

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Use user.id which is already set in getUserByEmail
    const userId = user.id;
    console.log("User ID for saving Excel data:", userId);

    // Parse the request body
    const rawData = await request.json();

    // Validate the Excel data with a lightweight schema
    const validation = validateInput(excelDataSchema, rawData);
    if (!validation.success) {
      return NextResponse.json(
        {
          error: "Invalid Excel data format",
          details: validation.errors
        },
        { status: 400 }
      );
    }

    const excelData = validation.data;

    // Sanitize the data (remove any potentially harmful content)
    const sanitizedData = {
      shortOpenTableData: sanitizeArrayData(excelData.shortOpenTableData),
      shortLoadedTableData: sanitizeArrayData(excelData.shortLoadedTableData),
      longOpenTableData: sanitizeArrayData(excelData.longOpenTableData),
      longLoadedTableData: sanitizeArrayData(excelData.longLoadedTableData),
      shortClosedTableData: sanitizeArrayData(excelData.shortClosedTableData),
      longClosedTableData: sanitizeArrayData(excelData.longClosedTableData)
    };

    // Save the Excel data to MongoDB using Prisma
    const result = await saveExcelData(sanitizedData, userId);

    return NextResponse.json({
      success: true,
      message: "Excel data saved successfully. Previous data was overwritten.",
      result
    });
  } catch (error) {
    console.error("Error saving Excel data:", error);
    return NextResponse.json(
      { error: "Failed to save Excel data", details: error.message },
      { status: 500 }
    );
  }
}

export async function GET(request) {
  try {
    // Get the current user session
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Get the user from the database
    const user = await getUserByEmail(session.user.email);

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Use user.id which is already set in getUserByEmail
    const userId = user.id;
    console.log("User ID for getting Excel data:", userId);

    // Get the Excel data from MongoDB using Prisma
    const excelData = await getExcelData(userId);

    if (!excelData) {
      return NextResponse.json(
        { error: "No Excel data found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      excelData: {
        shortOpenTableData: excelData.shortOpenTableData || [],
        shortLoadedTableData: excelData.shortLoadedTableData || [],
        longOpenTableData: excelData.longOpenTableData || [],
        longLoadedTableData: excelData.longLoadedTableData || [],
        shortClosedTableData: excelData.shortClosedTableData || [],
        longClosedTableData: excelData.longClosedTableData || []
      }
    });
  } catch (error) {
    console.error("Error getting Excel data:", error);
    return NextResponse.json(
      { error: "Failed to get Excel data", details: error.message },
      { status: 500 }
    );
  }
}
