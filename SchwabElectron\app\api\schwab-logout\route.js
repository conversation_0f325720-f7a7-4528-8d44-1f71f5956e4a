import { cookies } from "next/headers";

export async function POST(req) {
  try {
    // Get the cookies object
    const cookiesObj = await cookies();

    // Clear all Schwab-related cookies by setting them to expire
    const schwabCookies = [
      "access_token",
      "authorization_code",
      "refresh_token",
      "client_correlId",
      "client_customerId",
    ];

    // Create response
    const response = new Response(
      JSON.stringify({
        success: true,
        message: "Successfully logged out from Schwab",
      }),
      {
        status: 200,
        headers: { "Content-Type": "application/json" },
      }
    );

    // Clear each cookie by setting it to expire
    schwabCookies.forEach((cookieName) => {
      response.headers.append(
        "Set-Cookie",
        `${cookieName}=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT; HttpOnly; Secure; SameSite=Strict`
      );
    });

    return response;
  } catch (error) {
    console.error("Error during Schwab logout:", error);
    return new Response(
      JSON.stringify({
        success: false,
        error: "Failed to logout from <PERSON><PERSON><PERSON>",
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" },
      }
    );
  }
}
