"use client";

import { Providers } from "@/components/providers.js";
import { MarketDataProvider } from "./testingWebsocket/MarketDataContext.js";
import TopNavbar from "../components/TopNavbar/TopNavbar.jsx";
import { ExcelDataProvider } from "./Strategies/WB/ExcelDataContext.js";
import { EndpointAppProvider } from "../components/EndpointAppContext.js";
import { PairArrayProvider } from "./pairArray/PairArray.js";
import { ComponentArrayProvider } from "./pairArray/PairComponent.js";
import { AccountDataProvider } from "./testingWebsocket/AccountDataContext.js";
import ProtectedRoute from "../components/ProtectedRoute";
import AuthOverlay from "../components/AuthOverlay";
import { usePathname } from "next/navigation";

// import FabOrderEntry from "../components/FabOrderEntry.jsx";

export default function MyClientWrapper({ children }) {
  const pathname = usePathname();

  // Exclude login page from protection
  const isLoginPage = pathname === "/generalLogin";

  // Wrap content with ProtectedRoute if not on login page
  const wrappedContent = isLoginPage ? (
    children
  ) : (
    <ProtectedRoute>{children}</ProtectedRoute>
  );

  return (
    <Providers>
      <MarketDataProvider>
        <AccountDataProvider>
          <ExcelDataProvider>
            <EndpointAppProvider>
              <ComponentArrayProvider>
                <PairArrayProvider>
                  {/* Add the overlay at the top level */}
                  <AuthOverlay />
                  <div className="min-h-screen bg-white dark:bg-gray-950">
                    {/* Top Navigation Bar */}
                    {!isLoginPage && <TopNavbar />}

                    {/* Main Content with top padding for navbar */}
                    <div className={`${!isLoginPage ? 'pt-[60px] min-h-screen' : ''}`}>
                      <main className="dark:text-gray-100">
                        {wrappedContent}
                      </main>
                      {/* Floating Order Entry Button (FAB) visible on all pages for signed-in users */}
                      {/* <FabOrderEntry /> */}
                    </div>
                  </div>
                </PairArrayProvider>
              </ComponentArrayProvider>
            </EndpointAppProvider>
          </ExcelDataProvider>
        </AccountDataProvider>
      </MarketDataProvider>
    </Providers>
  );
};
