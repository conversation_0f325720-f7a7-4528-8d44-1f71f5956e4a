/**
 * Comprehensive User Data Cleaner
 * Utility to clear all user-specific data when logging out
 */

export const clearAllUserData = async () => {
  console.log("🧹 Starting comprehensive user data cleanup...");

  try {
    // 1. Clear localStorage
    if (typeof window !== "undefined") {
      console.log("Clearing localStorage...");

      // Get all localStorage keys before clearing
      const localStorageKeys = Object.keys(localStorage);
      console.log("LocalStorage keys to clear:", localStorageKeys);

      localStorage.clear();
    }

    // 2. Clear sessionStorage
    if (typeof window !== "undefined") {
      console.log("Clearing sessionStorage...");
      sessionStorage.clear();
    }

    // 3. Clear IndexedDB (if any)
    // if (typeof window !== "undefined" && window.indexedDB) {
    //   try {
    //     console.log("Checking for IndexedDB databases...");
    //     const databases = await indexedDB.databases();

    //     if (databases.length > 0) {
    //       console.log(
    //         "Found IndexedDB databases:",
    //         databases.map((db) => db.name)
    //       );

    //       databases.forEach((db) => {
    //         if (db.name) {
    //           console.log(`Deleting IndexedDB: ${db.name}`);
    //           indexedDB.deleteDatabase(db.name);
    //         }
    //       });
    //     } else {
    //       console.log("No IndexedDB databases found");
    //     }
    //   } catch (error) {
    //     console.log(
    //       "IndexedDB not available or error accessing:",
    //       error.message
    //     );
    //   }
    // }

    // 4. Clear any cached API responses
    if (typeof window !== "undefined" && "caches" in window) {
      try {
        console.log("Clearing cache storage...");
        const cacheNames = await caches.keys();

        if (cacheNames.length > 0) {
          console.log("Found caches:", cacheNames);
          await Promise.all(
            cacheNames.map((cacheName) => caches.delete(cacheName))
          );
        } else {
          console.log("No caches found");
        }
      } catch (error) {
        console.log("Cache API not available or error:", error.message);
      }
    }

    // 5. Clear any service worker data (if applicable)
    if (
      typeof window !== "undefined" &&
      "navigator" in window &&
      "serviceWorker" in navigator
    ) {
      try {
        const registrations = await navigator.serviceWorker.getRegistrations();
        if (registrations.length > 0) {
          console.log("Unregistering service workers...");
          await Promise.all(
            registrations.map((registration) => registration.unregister())
          );
        }
      } catch (error) {
        console.log("Service worker cleanup error:", error.message);
      }
    }

    console.log("✅ User data cleanup completed successfully");
    return { success: true };
  } catch (error) {
    console.error("❌ Error during user data cleanup:", error);
    return { success: false, error: error.message };
  }
};

/**
 * Clear specific localStorage keys related to trading data
 */
export const clearTradingData = () => {
  if (typeof window === "undefined") return;

  const tradingKeys = [
    "shortOpenTableData",
    "shortLoadedTableData",
    "longOpenTableData",
    "longLoadedTableData",
    "shortClosedTableData",
    "longClosedTableData",
    "schwabLoggedIn",
    "schwabLoginAttempt",
    "pairArray",
    "tradingPairs",
    "accountData",
    "marketData",
    "transactionData",
    "orderData",
  ];

  console.log("Clearing trading-specific data...");
  tradingKeys.forEach((key) => {
    if (localStorage.getItem(key)) {
      console.log(`Removing localStorage key: ${key}`);
      localStorage.removeItem(key);
    }
  });
};

/**
 * Clear all cookies (client-side accessible ones)
 */
export const clearClientCookies = () => {
  if (typeof document === "undefined") return;

  console.log("Clearing client-side cookies...");

  // Get all cookies
  const cookies = document.cookie.split(";");

  cookies.forEach((cookie) => {
    const eqPos = cookie.indexOf("=");
    const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();

    if (name) {
      // Clear cookie for current domain
      document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
      document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=${window.location.hostname}`;
      document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.${window.location.hostname}`;
    }
  });
};
