"use client";
import TooltipButton from "@/components/TooltipButton";
import { useState, useEffect } from "react";
import {
  retrieveAccessToken,
  retrieveCustomerId,
  retrieveCorrelId,
} from "@/actions/schwabAccess";
import { useExcelData } from "../ExcelDataContext";
import { getServerUrl } from "@/utils/serverConfig";

const shortKeys = [
  "ticker",
  "shares",
  "sector",
  "spread",
  "volume",
  "status",
  "dividend",
];
const longKeys = [
  "ticker",
  "shares",
  "sector",
  "spread",
  "volume",
  "status",
  "dividend",
];

function ExcelInput() {
  const [pasteData, setPasteData] = useState("");
  const [updateStatus, setUpdateStatus] = useState("");
  const [redirectAfterSubmit, setRedirectAfterSubmit] = useState(false); // ✅ Disabled auto-redirect
  const [savedSymbols, setSavedSymbols] = useState([]);
  const {
    setShortLoadedTableData,
    setLongLoadedTableData,
    shortLoadedTableData,
    longLoadedTableData,
    shortOpenTableData,
    longOpenTableData,
    shortClosedTableData,
    longClosedTableData,
  } = useExcelData();

  // Load saved symbols on component mount
  useEffect(() => {
    const loadSavedSymbols = async () => {
      try {
        const response = await fetch("/api/get-saved-symbols");
        if (response.ok) {
          const data = await response.json();
          setSavedSymbols(data.symbolsArray || []);
          console.log("Loaded saved symbols:", data.symbolsArray);
        } else if (response.status === 401) {
          // User not authenticated with Schwab - this is expected if not signed in
          console.log(
            "User not authenticated with Schwab, no saved symbols available"
          );
          setSavedSymbols([]);
        } else {
          console.warn(
            "Failed to load saved symbols:",
            response.status,
            response.statusText
          );
        }
      } catch (error) {
        console.error("Error loading saved symbols:", error);
      }
    };

    loadSavedSymbols();
  }, []);

  const handleChange = (e) => {
    setPasteData(e.target.value);
  };

  const handleSubmit = async () => {
    const rows = pasteData.split(/\r?\n/).filter((row) => row.trim() !== "");
    const parsedData = rows.map((row) => row.split("\t"));
    const existingDataShort =
      shortLoadedTableData.length +
        shortOpenTableData.length +
        shortClosedTableData.length || 0;
    const existingDataLong =
      longLoadedTableData.length +
        longOpenTableData.length +
        longClosedTableData.length || 0;
    const shortData = parsedData.map((row, index) => {
      const obj = {};
      shortKeys.forEach((key, idx) => {
        if (key === "status") {
          obj[key] = "WB_LoadedPairs";
        } else if (key === "dividend") {
          obj[key] = "0";
        } else {
          obj[key] = row[idx] || "";
        }
      });
      return { ...obj, id: (existingDataShort + index).toString() };
    });

    const longData = parsedData.map((row, index) => {
      const obj = {};
      longKeys.forEach((key, idx) => {
        if (key === "status") {
          obj[key] = "WB_LoadedPairs";
        } else if (key === "dividend") {
          obj[key] = "0";
        } else {
          obj[key] = row[idx + 5] || "";
        }
      });
      return { ...obj, id: (existingDataLong + index).toString() };
    });

    setShortLoadedTableData((prev) => [...prev, ...shortData]);
    setLongLoadedTableData((prev) => [...prev, ...longData]);
    setPasteData("");

    // Display status message
    setUpdateStatus(
      "Data processed successfully. Pairs will be saved to database when created."
    );

    // Redirect to dashboard if option is enabled
    if (redirectAfterSubmit) {
      // Wait a moment to ensure data is saved to localStorage
      setTimeout(() => {
        window.location.href = "/Strategies/WB/dashboard";
      }, 500);
    }
  };

  // Function to update stock symbols on the server
  const updateStockSymbols = async () => {
    const serverUrl = getServerUrl();
    try {
      setUpdateStatus("Update in progress...");

      // Collect all ticker symbols from both short and long data
      const shortTickers = [
        ...shortLoadedTableData,
        ...shortOpenTableData,
        ...shortClosedTableData,
      ]
        .map((item) => item.ticker)
        .filter((ticker) => ticker && ticker.trim() !== "");

      const longTickers = [
        ...longLoadedTableData,
        ...longOpenTableData,
        ...longClosedTableData,
      ]
        .map((item) => item.ticker)
        .filter((ticker) => ticker && ticker.trim() !== "");

      // Combine and remove duplicates
      const allTickers = [...new Set([...shortTickers, ...longTickers])];

      if (allTickers.length === 0) {
        setUpdateStatus("No symbols found to update");
        return;
      }

      console.log("Symbols to send to server:", allTickers);

      // First, test if the server is responding
      try {
        const testUrl = `${serverUrl}/api/test`;
        setUpdateStatus(`Verifying server connection: ${testUrl}...`);

        const testResponse = await fetch(testUrl);
        if (!testResponse.ok) {
          setUpdateStatus(
            `Error: The server is not responding correctly. Code: ${testResponse.status}`
          );
          return;
        }

        const testData = await testResponse.json();
        console.log("Test server response:", testData);
        setUpdateStatus(
          `Server connected. Current symbols: ${testData.currentSymbols}. Sending new symbols...`
        );
      } catch (testError) {
        console.error("Error in connection test:", testError);
        setUpdateStatus(
          `Connection error: ${testError.message}. Make sure the server is running on ${serverUrl}`
        );
        return;
      }

      // Send to server
      const url = `${serverUrl}/api/update-stock-symbols`;

      try {
        const accessToken = await retrieveAccessToken();
        const customerId = await retrieveCustomerId();
        const correlId = await retrieveCorrelId();

        // Get user session for email
        const session = await fetch("/api/auth/session");
        const sessionData = await session.json();
        const userEmail = sessionData?.user?.email;

        if (!userEmail) {
          console.warn(
            "No user email found in session, symbols will not be associated with user account"
          );
        }

        const response = await fetch(url, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            symbols: allTickers,
            token: accessToken,
            clientCustomerId: customerId,
            clientCorrelId: correlId,
            userEmail: userEmail,
          }),
          credentials: "include",
        });

        // Check if response is JSON
        const contentType = response.headers.get("content-type");
        if (!contentType || !contentType.includes("application/json")) {
          const textResponse = await response.text();
          console.error("Server returned non-JSON response:", textResponse);
          setUpdateStatus(
            `Error: The server returned an invalid response. Make sure the server is running on ${serverUrl}`
          );
          return;
        }

        const data = await response.json();

        if (response.ok) {
          setUpdateStatus(`Symbols updated successfully: ${data.symbols}`);

          // Update the saved symbols display
          setSavedSymbols(data.symbols.split(","));
        } else {
          setUpdateStatus(`Error: ${data.error || "Unknown error"}`);
        }
      } catch (fetchError) {
        console.error("Error in fetch request:", fetchError);
        setUpdateStatus(
          `Connection error: ${fetchError.message}. Make sure the server is running on ${serverUrl}`
        );
      }
    } catch (error) {
      console.error("Error updating symbols:", error);
      setUpdateStatus(`Error: ${error.message || "Unknown error"}`);
    }
  };

  return (
    <div className='group relative overflow-hidden rounded-xl bg-white dark:bg-gray-800 shadow-xl border border-gray-200 dark:border-gray-700 hover:shadow-2xl transition-all duration-300'>
      {/* Professional Header */}
      <div className='bg-gradient-to-r from-indigo-600 to-purple-600 p-8'>
        <div className='flex items-center justify-between'>
          <div className='flex items-center'>
            <div className='w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mr-4 backdrop-blur-sm border border-white/20'>
              <svg
                xmlns='http://www.w3.org/2000/svg'
                className='h-6 w-6 text-white'
                fill='none'
                viewBox='0 0 24 24'
                stroke='currentColor'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
                />
              </svg>
            </div>
            <div>
              <h2 className='text-2xl font-bold text-white'>Data Import</h2>
              <p className='text-indigo-100 text-sm mt-1'>
                Import trading pairs from Excel spreadsheets
              </p>
            </div>
          </div>
          <div className='hidden md:block'>
            <div className='w-16 h-16 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm border border-white/20'>
              <svg
                xmlns='http://www.w3.org/2000/svg'
                className='h-8 w-8 text-white'
                fill='none'
                viewBox='0 0 24 24'
                stroke='currentColor'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={1.5}
                  d='M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10'
                />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Content Section */}
      <div className='p-8'>
        {/* Data Input Section */}
        <div className='mb-8'>
          <div className='mb-4'>
            <label className='block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2'>
              Excel Data Input
            </label>
            <div className='relative'>
              <textarea
                onChange={handleChange}
                value={pasteData}
                placeholder='Paste your Excel data here in tabular format. Each row should contain trading pair information with columns for ticker, shares, sector, spread, volume.'
                rows='12'
                className='w-full p-4 border-2 border-gray-300 dark:border-gray-600 rounded-xl dark:bg-gray-700 dark:text-gray-200 dark:placeholder-gray-400 focus:ring-2 focus:ring-indigo-500 dark:focus:ring-indigo-400 focus:border-indigo-500 dark:focus:border-indigo-400 transition-all duration-200 resize-none font-mono text-sm'
              />
              <div className='absolute bottom-3 right-3 text-xs text-gray-400 dark:text-gray-500'>
                {pasteData.split("\n").filter((line) => line.trim()).length}{" "}
                rows
              </div>
            </div>
          </div>

          {/* Professional Action Buttons */}
          <div className='flex flex-wrap gap-4'>
            <TooltipButton
              onClick={handleSubmit}
              className='flex items-center px-6 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105'
              tooltipText='Process the pasted Excel data and update the trading tables'
            >
              <svg
                xmlns='http://www.w3.org/2000/svg'
                className='h-5 w-5 mr-2'
                fill='none'
                viewBox='0 0 24 24'
                stroke='currentColor'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z'
                />
              </svg>
              Process Data
            </TooltipButton>

            <TooltipButton
              onClick={updateStockSymbols}
              className='flex items-center px-6 py-3 bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105'
              tooltipText='Update stock symbols on the server for real-time market data synchronization'
            >
              <svg
                xmlns='http://www.w3.org/2000/svg'
                className='h-5 w-5 mr-2'
                fill='none'
                viewBox='0 0 24 24'
                stroke='currentColor'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15'
                />
              </svg>
              Update Symbols
            </TooltipButton>
          </div>
        </div>

        {/* Professional Saved Symbols Display */}
        {savedSymbols.length > 0 && (
          <div className='mb-8 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl border border-blue-200 dark:border-blue-800 shadow-sm'>
            <div className='flex items-center justify-between mb-4'>
              <div className='flex items-center'>
                <div className='w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mr-3'>
                  <svg
                    xmlns='http://www.w3.org/2000/svg'
                    className='h-4 w-4 text-blue-600 dark:text-blue-400'
                    fill='none'
                    viewBox='0 0 24 24'
                    stroke='currentColor'
                  >
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                      d='M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
                    />
                  </svg>
                </div>
                <h4 className='text-sm font-semibold text-blue-800 dark:text-blue-200'>
                  Active Trading Symbols
                </h4>
              </div>
              <span className='inline-flex items-center justify-center w-8 h-8 text-sm font-bold text-white bg-blue-600 rounded-full'>
                {savedSymbols.length}
              </span>
            </div>
            <div className='flex flex-wrap gap-2'>
              {savedSymbols.map((symbol, index) => (
                <span
                  key={index}
                  className='inline-flex items-center px-3 py-1.5 text-sm font-medium text-blue-800 dark:text-blue-200 bg-blue-100 dark:bg-blue-800/30 rounded-lg border border-blue-200 dark:border-blue-700'
                >
                  {symbol}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Professional Status Messages */}
        {updateStatus && (
          <div
            className={`p-4 rounded-xl shadow-sm border ${
              updateStatus.includes("Error")
                ? "bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 text-red-800 dark:text-red-300 border-red-200 dark:border-red-800"
                : "bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 text-green-800 dark:text-green-300 border-green-200 dark:border-green-800"
            }`}
          >
            <div className='flex items-center gap-3'>
              <span className='text-lg'>
                {updateStatus.includes("Error") ? "❌" : "✅"}
              </span>
              <span className='font-medium'>{updateStatus}</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

function ClearButton() {
  const {
    setShortOpenTableData,
    setShortLoadedTableData,
    setLongOpenTableData,
    setLongLoadedTableData,
    setShortClosedTableData,
    setLongClosedTableData,
  } = useExcelData();
  const [clearStatus, setClearStatus] = useState("");

  const clearData = () => {
    setShortOpenTableData([]);
    setShortLoadedTableData([]);
    setLongOpenTableData([]);
    setLongLoadedTableData([]);
    setShortClosedTableData([]);
    setLongClosedTableData([]);
    if (typeof window !== "undefined") {
      localStorage.removeItem("shortOpenTableData");
      localStorage.removeItem("shortLoadedTableData");
      localStorage.removeItem("longOpenTableData");
      localStorage.removeItem("longLoadedTableData");
      localStorage.removeItem("shortClosedTableData");
      localStorage.removeItem("longClosedTableData");
    }
    setClearStatus("All data has been cleared");
    setTimeout(() => setClearStatus(""), 3000);
  };

  return (
    <div className='group relative overflow-hidden rounded-xl bg-white dark:bg-gray-800 shadow-xl border border-gray-200 dark:border-gray-700 hover:shadow-2xl transition-all duration-300'>
      {/* Professional Header */}
      <div className='bg-gradient-to-r from-red-600 to-pink-600 p-6'>
        <div className='flex items-center justify-between'>
          <div className='flex items-center'>
            <div className='w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mr-4 backdrop-blur-sm border border-white/20'>
              <svg
                xmlns='http://www.w3.org/2000/svg'
                className='h-6 w-6 text-white'
                fill='none'
                viewBox='0 0 24 24'
                stroke='currentColor'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16'
                />
              </svg>
            </div>
            <div>
              <h2 className='text-xl font-bold text-white'>Data Management</h2>
              <p className='text-red-100 text-sm mt-1'>
                Clear and reset trading data
              </p>
            </div>
          </div>
          <div className='hidden md:block'>
            <div className='w-16 h-16 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm border border-white/20'>
              <svg
                xmlns='http://www.w3.org/2000/svg'
                className='h-8 w-8 text-white'
                fill='none'
                viewBox='0 0 24 24'
                stroke='currentColor'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={1.5}
                  d='M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z'
                />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Content Section */}
      <div className='p-8'>
        <div className='bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 rounded-xl p-6 border border-red-200 dark:border-red-800 mb-6'>
          <div className='flex items-start'>
            <div className='flex-shrink-0'>
              <div className='w-10 h-10 bg-red-100 dark:bg-red-900/30 rounded-lg flex items-center justify-center'>
                <svg
                  xmlns='http://www.w3.org/2000/svg'
                  className='h-6 w-6 text-red-600 dark:text-red-400'
                  fill='none'
                  viewBox='0 0 24 24'
                  stroke='currentColor'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z'
                  />
                </svg>
              </div>
            </div>
            <div className='ml-4'>
              <h3 className='text-lg font-semibold text-red-800 dark:text-red-200 mb-2'>
                Warning: Destructive Action
              </h3>
              <p className='text-red-700 dark:text-red-300 leading-relaxed'>
                This will permanently clear all Excel data from memory and local
                storage, including all trading pairs, positions, and
                configuration data. This action cannot be undone and will
                require re-importing your data.
              </p>
            </div>
          </div>
        </div>

        <div className='flex justify-center'>
          <TooltipButton
            onClick={clearData}
            className='flex items-center px-8 py-4 bg-gradient-to-r from-red-600 to-pink-600 hover:from-red-700 hover:to-pink-700 text-white font-bold rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105'
            tooltipText='Permanently clear all Excel data from memory and local storage'
          >
            <svg
              xmlns='http://www.w3.org/2000/svg'
              className='h-5 w-5 mr-3'
              fill='none'
              viewBox='0 0 24 24'
              stroke='currentColor'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16'
              />
            </svg>
            Clear All Data
          </TooltipButton>
        </div>
        {/* Status Message */}
        {clearStatus && (
          <div className='mt-6 p-4 rounded-xl shadow-sm border bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 text-green-800 dark:text-green-300 border-green-200 dark:border-green-800'>
            <div className='flex items-center gap-3'>
              <span className='text-lg'>✅</span>
              <span className='font-medium'>{clearStatus}</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default function Home() {
  return (
    <div className='min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-blue-900/20 dark:to-indigo-900/20'>
      {/* Professional Hero Section */}
      <div className='relative overflow-hidden bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 dark:from-black dark:via-slate-900 dark:to-blue-900'>
        {/* Subtle Background Pattern */}
        <div className='absolute inset-0 bg-black/20'>
          <div
            className='absolute inset-0 opacity-30'
            style={{
              backgroundImage:
                'url(\'data:image/svg+xml;utf8,<svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><g fill="white" fill-opacity="0.1"><circle cx="20" cy="20" r="2"/></g></svg>\')',
            }}
          ></div>
        </div>

        <div className='relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16'>
          <div className='text-center'>
            <h1 className='text-4xl md:text-5xl font-bold text-white mb-6 leading-tight'>
              WB Strategy
              <span className='bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent'>
                {" "}
                Configuration
              </span>
            </h1>
          </div>
        </div>

        {/* Subtle Geometric Elements */}
        <div className='absolute top-1/4 left-8 w-24 h-24 border border-white/10 rounded-full'></div>
        <div className='absolute bottom-1/4 right-8 w-16 h-16 border border-purple-400/20 rounded-lg rotate-45'></div>
        <div className='absolute top-1/2 right-1/4 w-12 h-12 bg-gradient-to-br from-purple-500/10 to-pink-500/10 rounded-full'></div>
      </div>

      {/* Configuration Content */}
      <div className='max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12'>
        <div className='space-y-8'>
          <ExcelInput />
          <ClearButton />
        </div>
      </div>
    </div>
  );
}
