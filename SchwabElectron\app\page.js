"use client";
import ProtectedRoute from "../components/ProtectedRoute";
import { useSession, signOut as logoutUser } from "next-auth/react";
import { getAuthorizationCodeURL } from "../actions/schwabAccess";
import { useState, useEffect } from "react";
import Link from "next/link";
import { useMarketData } from "./testingWebsocket/MarketDataContext";
import { useExcelData } from "./Strategies/WB/ExcelDataContext";
import { usePairArray } from "./pairArray/PairArray";
import { useEndpointAppContext } from "../components/EndpointAppContext";
import { clearAllUserData } from "../utils/userDataCleaner";

export default function HomePage() {
  const { data: session, status } = useSession();
  const { disconnectFromSchwab, clearAllUserData: clearMarketData } =
    useMarketData();
  const { clearAllUserData: clearExcelData } = useExcelData();
  const { clearAllUserData: clearPairData } = usePairArray();
  const { clearAllUserData: clearEndpointData } = useEndpointAppContext();
  const [isSchwabCallback, setIsSchwabCallback] = useState(false);
  const [isLoggedInToSchwab, setIsLoggedInToSchwab] = useState(false);

  useEffect(() => {
    console.log("HomePage session:", session, "status:", status);
  }, [session, status]);

  useEffect(() => {
    // Check if we're on the callback URL
    const url = new URL(window.location.href);
    const code = url.searchParams.get("code");
    if (code) {
      setIsSchwabCallback(true);
    }

    // Always check Schwab login status on page load
    checkSchwabLogin();
  }, []);

  const checkSchwabLogin = async () => {
    try {
      // Chiedi lo stato Schwab al backend (cookie httpOnly)
      const res = await fetch("/api/schwab-status", {
        method: "GET",
        credentials: "include",
      });
      const data = await res.json();
      setIsLoggedInToSchwab(data.loggedIn);
      if (data.loggedIn) {
        localStorage.setItem("schwabLoggedIn", "true");
      } else {
        localStorage.removeItem("schwabLoggedIn");
      }
    } catch (error) {
      console.error("Error checking Schwab login status:", error);
    }
  };

  const handleLogInToSchwab = async () => {
    try {
      console.log("Getting Auth URL...");
      const url = new URL(window.location.href);
      let code = url.searchParams.get("code");
      if (!code) {
        // Set a flag to indicate we're attempting to log in
        localStorage.setItem("schwabLoginAttempt", "true");
        window.location.href = await getAuthorizationCodeURL();
        return;
      }

      // If we have a code, we're logged in
      localStorage.setItem("schwabLoggedIn", "true");
      setIsLoggedInToSchwab(true);
    } catch (error) {
      console.error("Error getting OAuth Token:", error);
    }
  };

  const handleLogOutFromSchwab = async () => {
    try {
      // Call the logout API to clear httpOnly cookies
      const response = await fetch("/api/schwab-logout", {
        method: "POST",
        credentials: "include",
      });

      const result = await response.json();

      if (result.success) {
        // Clear localStorage flags
        localStorage.removeItem("schwabLoggedIn");
        localStorage.removeItem("schwabLoginAttempt");

        // Disconnect from WebSocket
        if (disconnectFromSchwab) {
          disconnectFromSchwab();
        }

        // Update state immediately
        setIsLoggedInToSchwab(false);

        console.log("Successfully disconnected from Schwab");
      } else {
        console.error("Failed to logout from Schwab:", result.error);
        // Still update the UI state even if server logout failed
        setIsLoggedInToSchwab(false);
        localStorage.removeItem("schwabLoggedIn");
        localStorage.removeItem("schwabLoginAttempt");

        // Still try to disconnect from WebSocket
        if (disconnectFromSchwab) {
          disconnectFromSchwab();
        }
      }
    } catch (error) {
      console.error("Error logging out from Schwab:", error);
      // Still update the UI state even if there was an error
      setIsLoggedInToSchwab(false);
      localStorage.removeItem("schwabLoggedIn");
      localStorage.removeItem("schwabLoginAttempt");

      // Still try to disconnect from WebSocket
      if (disconnectFromSchwab) {
        disconnectFromSchwab();
      }
    }
  };

  const handleLogout = async () => {
    try {
      console.log("🚪 Starting comprehensive logout process...");

      // 1. Clear all user data from contexts
      console.log("Clearing context data...");
      if (clearMarketData) clearMarketData();
      if (clearExcelData) clearExcelData();
      if (clearPairData) clearPairData();
      if (clearEndpointData) clearEndpointData();

      // 2. Call the full logout API to clear all server-side cookies
      console.log("Clearing server-side cookies...");
      await fetch("/api/full-logout", {
        method: "POST",
        credentials: "include",
      });

      // 3. Use comprehensive data cleaner
      console.log("Running comprehensive data cleanup...");
      await clearAllUserData();

      console.log("✅ All user data cleared, signing out...");

      // 4. Sign out from NextAuth with proper URL
      const redirectUrl = `${window.location.origin}/generalLogin`;
      console.log("Redirecting to:", redirectUrl);

      await logoutUser({
        callbackUrl: redirectUrl,
        redirect: true,
      });
    } catch (error) {
      console.error("❌ Error during logout:", error);
      // Still try to logout even if there are errors
      try {
        const redirectUrl = `${window.location.origin}/generalLogin`;
        await logoutUser({
          callbackUrl: redirectUrl,
          redirect: true,
        });
      } catch (signOutError) {
        console.error("Failed to sign out, forcing redirect:", signOutError);
        // Force redirect as last resort
        if (typeof window !== "undefined") {
          window.location.href = "/generalLogin";
        }
      }
    }
  };

  // Professional navigation cards with SVG icons
  const navigationCards = [
    {
      title: "Account Summary",
      description: "Comprehensive portfolio overview and account balances",
      icon: (
        <svg
          xmlns='http://www.w3.org/2000/svg'
          className='h-8 w-8'
          fill='none'
          viewBox='0 0 24 24'
          stroke='currentColor'
        >
          <path
            strokeLinecap='round'
            strokeLinejoin='round'
            strokeWidth={1.5}
            d='M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z'
          />
        </svg>
      ),
      href: "/accountsSummary",
      bgColor: "bg-gradient-to-br from-slate-700 to-slate-800",
      shadowColor: "shadow-slate-500/20",
      hoverEffect: "hover:shadow-slate-500/40 hover:-translate-y-1",
    },
    {
      title: "WB Dashboard",
      description: "Real-time trading pair monitoring and management",
      icon: (
        <svg
          xmlns='http://www.w3.org/2000/svg'
          className='h-8 w-8'
          fill='none'
          viewBox='0 0 24 24'
          stroke='currentColor'
        >
          <path
            strokeLinecap='round'
            strokeLinejoin='round'
            strokeWidth={1.5}
            d='M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z'
          />
        </svg>
      ),
      href: "/Strategies/WB/dashboard",
      bgColor: "bg-gradient-to-br from-blue-700 to-blue-800",
      shadowColor: "shadow-blue-500/20",
      hoverEffect: "hover:shadow-blue-500/40 hover:-translate-y-1",
    },
    {
      title: "WB Configuration",
      description: "Advanced strategy setup and parameter optimization",
      icon: (
        <svg
          xmlns='http://www.w3.org/2000/svg'
          className='h-8 w-8'
          fill='none'
          viewBox='0 0 24 24'
          stroke='currentColor'
        >
          <path
            strokeLinecap='round'
            strokeLinejoin='round'
            strokeWidth={1.5}
            d='M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z'
          />
          <path
            strokeLinecap='round'
            strokeLinejoin='round'
            strokeWidth={1.5}
            d='M15 12a3 3 0 11-6 0 3 3 0 016 0z'
          />
        </svg>
      ),
      href: "/Strategies/WB/configuration",
      bgColor: "bg-gradient-to-br from-indigo-700 to-indigo-800",
      shadowColor: "shadow-indigo-500/20",
      hoverEffect: "hover:shadow-indigo-500/40 hover:-translate-y-1",
    },
    {
      title: "Saved Pairs",
      description: "Curated collection of trading pair configurations",
      icon: (
        <svg
          xmlns='http://www.w3.org/2000/svg'
          className='h-8 w-8'
          fill='none'
          viewBox='0 0 24 24'
          stroke='currentColor'
        >
          <path
            strokeLinecap='round'
            strokeLinejoin='round'
            strokeWidth={1.5}
            d='M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z'
          />
        </svg>
      ),
      href: "/savedPairs",
      bgColor: "bg-gradient-to-br from-emerald-700 to-emerald-800",
      shadowColor: "shadow-emerald-500/20",
      hoverEffect: "hover:shadow-emerald-500/40 hover:-translate-y-1",
    },
    {
      title: "File Handler",
      description: "Data import and file management utilities",
      icon: (
        <svg
          xmlns='http://www.w3.org/2000/svg'
          className='h-8 w-8'
          fill='none'
          viewBox='0 0 24 24'
          stroke='currentColor'
        >
          <path
            strokeLinecap='round'
            strokeLinejoin='round'
            strokeWidth={1.5}
            d='M5 19a2 2 0 01-2-2V7a2 2 0 012-2h4l2 2h4a2 2 0 012 2v1M5 19h14a2 2 0 002-2v-5a2 2 0 00-2-2H9a2 2 0 00-2 2v5a2 2 0 01-2 2z'
          />
        </svg>
      ),
      href: "/fileHandler",
      bgColor: "bg-gradient-to-br from-gray-700 to-gray-800",
      shadowColor: "shadow-gray-500/20",
      hoverEffect: "hover:shadow-gray-500/40 hover:-translate-y-1",
    },
    {
      title: "Account Activity",
      description: "Transaction history and detailed activity logs",
      icon: (
        <svg
          xmlns='http://www.w3.org/2000/svg'
          className='h-8 w-8'
          fill='none'
          viewBox='0 0 24 24'
          stroke='currentColor'
        >
          <path
            strokeLinecap='round'
            strokeLinejoin='round'
            strokeWidth={1.5}
            d='M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
          />
        </svg>
      ),
      href: "/testingAccountActivity",
      bgColor: "bg-gradient-to-br from-purple-700 to-purple-800",
      shadowColor: "shadow-purple-500/20",
      hoverEffect: "hover:shadow-purple-500/40 hover:-translate-y-1",
    },
  ];

  return (
    <ProtectedRoute>
      <div className='min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-blue-900/20 dark:to-indigo-900/20'>
        {/* Professional Hero Section */}
        <div className='relative overflow-hidden bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 dark:from-black dark:via-slate-900 dark:to-blue-900'>
          {/* Subtle Background Pattern */}
          <div className='absolute inset-0 bg-black/20'>
            <div
              className='absolute inset-0'
              style={{
                backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Cpath d='M20 20c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm10 0c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z'/%3E%3C/g%3E%3C/svg%3E")`,
              }}
            ></div>
          </div>

          <div className='relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-28'>
            <div className='text-center'>
              <h1 className='text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-8 leading-tight'>
                {isSchwabCallback ? (
                  <>
                    <span className='text-emerald-400'>Connection</span>{" "}
                    Established
                  </>
                ) : (
                  <>
                    Welcome to Your <br />
                    <span className='bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent'>
                      Trading Dashboard
                    </span>
                  </>
                )}
              </h1>
            </div>
          </div>

          {/* Subtle Geometric Elements */}
          <div className='absolute top-1/4 left-8 w-32 h-32 border border-white/10 rounded-full'></div>
          <div className='absolute bottom-1/4 right-8 w-24 h-24 border border-blue-400/20 rounded-lg rotate-45'></div>
          <div className='absolute top-1/2 right-1/4 w-16 h-16 bg-gradient-to-br from-blue-500/10 to-cyan-500/10 rounded-full'></div>
        </div>

        {/* Professional User Dashboard */}
        {session && (
          <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8'>
            <div className='bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden'>
              <div className='bg-gradient-to-r from-slate-800 to-blue-800 p-6'>
                <div className='flex flex-col sm:flex-row justify-between items-start sm:items-center'>
                  <div className='flex items-center mb-4 sm:mb-0'>
                    <div className='w-14 h-14 bg-white/10 rounded-lg flex items-center justify-center mr-4 backdrop-blur-sm border border-white/20'>
                      <svg
                        xmlns='http://www.w3.org/2000/svg'
                        className='h-7 w-7 text-white'
                        fill='none'
                        viewBox='0 0 24 24'
                        stroke='currentColor'
                      >
                        <path
                          strokeLinecap='round'
                          strokeLinejoin='round'
                          strokeWidth={1.5}
                          d='M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z'
                        />
                      </svg>
                    </div>
                    <div>
                      <h2 className='text-lg font-semibold text-white'>
                        Welcome,{" "}
                        {session.user?.name ||
                          session.user?.email?.split("@")[0]}
                      </h2>
                      <div className='flex items-center mt-2'>
                        <div
                          className={`w-2.5 h-2.5 rounded-full mr-2 ${
                            isLoggedInToSchwab ? "bg-emerald-400" : "bg-red-400"
                          }`}
                        ></div>
                        <span className='text-slate-300 text-sm'>
                          {isLoggedInToSchwab
                            ? "Schwab Connected • Real-time data active"
                            : "Schwab Disconnected • Limited access"}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className='flex items-center space-x-3'>
                    {session && !isLoggedInToSchwab && (
                      <button
                        onClick={() => handleLogInToSchwab()}
                        className='px-5 py-2.5 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-all duration-200 shadow-md hover:shadow-lg'
                      >
                        Connect Schwab
                      </button>
                    )}
                    {isLoggedInToSchwab && (
                      <button
                        onClick={handleLogOutFromSchwab}
                        className='px-4 py-2 text-slate-300 hover:text-white transition-colors text-sm border border-white/20 rounded-lg hover:bg-white/10'
                      >
                        Disconnect
                      </button>
                    )}
                    <button
                      onClick={() => handleLogout()}
                      className='px-4 py-2 text-slate-300 hover:text-white transition-colors text-sm border border-white/20 rounded-lg hover:bg-white/10'
                    >
                      Sign Out
                    </button>
                  </div>
                </div>
              </div>

              {/* System Status */}
              <div className='p-6 bg-slate-50 dark:bg-gray-800/50'>
                <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
                  <div className='flex items-center'>
                    <div className='w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mr-3'>
                      <svg
                        xmlns='http://www.w3.org/2000/svg'
                        className='h-5 w-5 text-blue-600 dark:text-blue-400'
                        fill='none'
                        viewBox='0 0 24 24'
                        stroke='currentColor'
                      >
                        <path
                          strokeLinecap='round'
                          strokeLinejoin='round'
                          strokeWidth={2}
                          d='M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z'
                        />
                      </svg>
                    </div>
                    <div>
                      <div className='text-sm font-medium text-gray-900 dark:text-white'>
                        Dashboard Services
                      </div>
                      <div className='text-xs text-emerald-600 dark:text-emerald-400 font-medium'>
                        Online
                      </div>
                    </div>
                  </div>
                  <div className='flex items-center'>
                    <div className='w-10 h-10 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center mr-3'>
                      <svg
                        xmlns='http://www.w3.org/2000/svg'
                        className='h-5 w-5 text-purple-600 dark:text-purple-400'
                        fill='none'
                        viewBox='0 0 24 24'
                        stroke='currentColor'
                      >
                        <path
                          strokeLinecap='round'
                          strokeLinejoin='round'
                          strokeWidth={2}
                          d='M13 10V3L4 14h7v7l9-11h-7z'
                        />
                      </svg>
                    </div>
                    <div>
                      <div className='text-sm font-medium text-gray-900 dark:text-white'>
                        Data Processing
                      </div>
                      <div className='text-xs text-emerald-600 dark:text-emerald-400 font-medium'>
                        Active
                      </div>
                    </div>
                  </div>
                  <div className='flex items-center'>
                    <div className='w-10 h-10 bg-emerald-100 dark:bg-emerald-900/30 rounded-lg flex items-center justify-center mr-3'>
                      <svg
                        xmlns='http://www.w3.org/2000/svg'
                        className='h-5 w-5 text-emerald-600 dark:text-emerald-400'
                        fill='none'
                        viewBox='0 0 24 24'
                        stroke='currentColor'
                      >
                        <path
                          strokeLinecap='round'
                          strokeLinejoin='round'
                          strokeWidth={2}
                          d='M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15'
                        />
                      </svg>
                    </div>
                    <div>
                      <div className='text-sm font-medium text-gray-900 dark:text-white'>
                        Market Data
                      </div>
                      <div
                        className={`text-xs font-medium ${
                          isLoggedInToSchwab
                            ? "text-emerald-600 dark:text-emerald-400"
                            : "text-red-600 dark:text-red-400"
                        }`}
                      >
                        {isLoggedInToSchwab ? "Live" : "Offline"}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Professional Navigation Cards */}
        <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16'>
          <div className='text-center mb-16'>
            <h2 className='text-3xl font-bold text-gray-900 dark:text-white mb-4'>
              Platform Modules
            </h2>
            <p className='text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto'>
              Comprehensive suite of professional trading and investment
              management tools designed for institutional-grade performance
            </p>
          </div>

          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8'>
            {navigationCards.map((card, index) => (
              <Link href={card.href} key={index}>
                <div
                  className={`group relative overflow-hidden rounded-xl ${card.bgColor} ${card.shadowColor} shadow-xl ${card.hoverEffect} transition-all duration-300 cursor-pointer border border-white/10`}
                >
                  {/* Subtle Background Pattern */}
                  <div className='absolute inset-0 bg-black/20'>
                    <div className='absolute inset-0 bg-gradient-to-br from-white/5 to-transparent'></div>
                  </div>

                  {/* Content */}
                  <div className='relative p-8 text-white'>
                    <div className='flex items-center justify-between mb-6'>
                      <div className='w-12 h-12 bg-white/10 rounded-lg flex items-center justify-center backdrop-blur-sm border border-white/20'>
                        {card.icon}
                      </div>
                      <div className='w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center backdrop-blur-sm group-hover:bg-white/20 transition-all duration-300'>
                        <svg
                          xmlns='http://www.w3.org/2000/svg'
                          className='h-5 w-5'
                          fill='none'
                          viewBox='0 0 24 24'
                          stroke='currentColor'
                        >
                          <path
                            strokeLinecap='round'
                            strokeLinejoin='round'
                            strokeWidth={2}
                            d='M13 7l5 5m0 0l-5 5m5-5H6'
                          />
                        </svg>
                      </div>
                    </div>

                    <h3 className='text-xl font-semibold mb-3 group-hover:text-blue-200 transition-colors duration-300'>
                      {card.title}
                    </h3>

                    <p className='text-white/80 text-sm leading-relaxed mb-6'>
                      {card.description}
                    </p>

                    <div className='flex items-center text-sm font-medium text-white/90'>
                      <span className='mr-2'>Launch Module</span>
                      <div className='w-4 h-px bg-white/40 group-hover:w-6 transition-all duration-300'></div>
                    </div>
                  </div>

                  {/* Subtle Hover Effect */}
                  <div className='absolute inset-0 bg-gradient-to-r from-white/0 via-white/3 to-white/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000'></div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
