#!/usr/bin/env node

/**
 * Certificate Generation Script
 * Generates self-signed SSL certificates for local development
 * Required for Schwab API integration (HTTPS only)
 */

import { execSync } from "child_process";
import { existsSync, mkdirSync } from "fs";
import { join, dirname } from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, "..");
const certsDir = join(projectRoot, "certificates");

console.log("🔐 Generating SSL certificates for local development...\n");

// Create certificates directory if it doesn't exist
if (!existsSync(certsDir)) {
  mkdirSync(certsDir, { recursive: true });
  console.log("📁 Created certificates directory");
}

// Check if certificates already exist
const certPath = join(certsDir, "localhost.pem");
const keyPath = join(certsDir, "localhost-key.pem");

if (existsSync(certPath) && existsSync(keyPath)) {
  console.log("✅ SSL certificates already exist!");
  console.log(`   Certificate: ${certPath}`);
  console.log(`   Private Key: ${keyPath}`);
  process.exit(0);
}

try {
  // Check if OpenSSL is available
  try {
    execSync("openssl version", { stdio: "pipe" });
    console.log("✅ OpenSSL found");
  } catch (error) {
    console.error("❌ OpenSSL not found. Please install OpenSSL first.");
    console.log("\n📋 Installation instructions:");
    console.log(
      "   Windows: Download from https://slproweb.com/products/Win32OpenSSL.html"
    );
    console.log("   macOS: brew install openssl");
    console.log(
      "   Linux: sudo apt-get install openssl (Ubuntu/Debian) or sudo yum install openssl (CentOS/RHEL)"
    );
    process.exit(1);
  }

  // Generate private key
  console.log("🔑 Generating private key...");
  execSync(`openssl genrsa -out "${keyPath}" 2048`, { stdio: "pipe" });

  // Generate certificate
  console.log("📜 Generating certificate...");
  const opensslCommand = `openssl req -new -x509 -key "${keyPath}" -out "${certPath}" -days 365 -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost" -addext "subjectAltName=DNS:localhost,DNS:127.0.0.1,IP:127.0.0.1"`;

  execSync(opensslCommand, { stdio: "pipe" });

  console.log("\n🎉 SSL certificates generated successfully!");
  console.log(`   Certificate: ${certPath}`);
  console.log(`   Private Key: ${keyPath}`);
  console.log(
    "\n⚠️  Note: These are self-signed certificates for development only."
  );
  console.log("   Your browser will show a security warning - this is normal.");
  console.log('   Click "Advanced" and "Proceed to localhost" to continue.');
} catch (error) {
  console.error("❌ Failed to generate certificates:", error.message);
  console.log("\n🔧 Troubleshooting:");
  console.log("   1. Make sure OpenSSL is installed and in your PATH");
  console.log(
    "   2. Run this script as administrator/sudo if you get permission errors"
  );
  console.log("   3. Check that the certificates directory is writable");
  process.exit(1);
}
