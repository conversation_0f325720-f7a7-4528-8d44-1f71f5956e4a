"use client";
import ProtectedRoute from "../../components/ProtectedRoute";
import { useExcelData } from "../Strategies/WB/ExcelDataContext";

export default function PlTracker() {
  const { tableData } = useExcelData();

  const specificData =
    tableData && tableData[1] ? tableData[1] : ["Dati non disponibili"];

  const temporaryfunction = () => {
    console.log(specificData);
  };

  return (
    <ProtectedRoute>
      <main>
        <h1>P & L Tracker</h1>
        <div>
          <p>Excel Data:</p>
          <p>{specificData[0]}</p>
          <p>{specificData[1]}</p>
          <p>{specificData[2]}</p>
          <p>{specificData[3]}</p>
        </div>
        <button onClick={temporaryfunction}>clicca</button>
      </main>
    </ProtectedRoute>
  );
}
