"use server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth-options";
import { getUserByEmail } from "@/lib/prisma-dal";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export async function GET(req) {
  const session = await getServerSession(authOptions);
  if (!session || !session.user.role || session.user.role.toLowerCase() !== "admin") {
    return new Response(JSON.stringify({ error: "Unauthorized" }), { status: 401 });
  }
  const users = await prisma.users.findMany({
    select: { id: true, name: true, email: true, role: true },
    orderBy: { email: "asc" },
  });
  // Normalize all roles to lowercase for UI consistency, but keep original for DB updates
  const usersWithRole = users.map(u => ({ ...u, role: (u.role || 'user').toLowerCase() }));
  return new Response(JSON.stringify({ users: usersWithRole }), { status: 200 });
}
