/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/schwab-logout/route";
exports.ids = ["app/api/schwab-logout/route"];
exports.modules = {

/***/ "(rsc)/./app/api/schwab-logout/route.js":
/*!****************************************!*\
  !*** ./app/api/schwab-logout/route.js ***!
  \****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\nasync function POST(req) {\n    try {\n        // Get the cookies object\n        const cookiesObj = await (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)();\n        // Clear all Schwab-related cookies by setting them to expire\n        const schwabCookies = [\n            \"access_token\",\n            \"authorization_code\",\n            \"refresh_token\",\n            \"client_correlId\",\n            \"client_customerId\"\n        ];\n        // Create response\n        const response = new Response(JSON.stringify({\n            success: true,\n            message: \"Successfully logged out from Schwab\"\n        }), {\n            status: 200,\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        // Clear each cookie by setting it to expire\n        schwabCookies.forEach((cookieName)=>{\n            response.headers.append(\"Set-Cookie\", `${cookieName}=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT; HttpOnly; Secure; SameSite=Strict`);\n        });\n        return response;\n    } catch (error) {\n        console.error(\"Error during Schwab logout:\", error);\n        return new Response(JSON.stringify({\n            success: false,\n            error: \"Failed to logout from Schwab\"\n        }), {\n            status: 500,\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/schwab-logout/route.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fschwab-logout%2Froute&page=%2Fapi%2Fschwab-logout%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fschwab-logout%2Froute.js&appDir=C%3A%5CUsers%5CEllen%5COneDrive%5CDesktop%5CDashboard%5CSchwabElectron%5CSchwabElectron%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CEllen%5COneDrive%5CDesktop%5CDashboard%5CSchwabElectron%5CSchwabElectron&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fschwab-logout%2Froute&page=%2Fapi%2Fschwab-logout%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fschwab-logout%2Froute.js&appDir=C%3A%5CUsers%5CEllen%5COneDrive%5CDesktop%5CDashboard%5CSchwabElectron%5CSchwabElectron%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CEllen%5COneDrive%5CDesktop%5CDashboard%5CSchwabElectron%5CSchwabElectron&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Ellen_OneDrive_Desktop_Dashboard_SchwabElectron_SchwabElectron_app_api_schwab_logout_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/schwab-logout/route.js */ \"(rsc)/./app/api/schwab-logout/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/schwab-logout/route\",\n        pathname: \"/api/schwab-logout\",\n        filename: \"route\",\n        bundlePath: \"app/api/schwab-logout/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Dashboard\\\\SchwabElectron\\\\SchwabElectron\\\\app\\\\api\\\\schwab-logout\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Users_Ellen_OneDrive_Desktop_Dashboard_SchwabElectron_SchwabElectron_app_api_schwab_logout_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fschwab-logout%2Froute&page=%2Fapi%2Fschwab-logout%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fschwab-logout%2Froute.js&appDir=C%3A%5CUsers%5CEllen%5COneDrive%5CDesktop%5CDashboard%5CSchwabElectron%5CSchwabElectron%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CEllen%5COneDrive%5CDesktop%5CDashboard%5CSchwabElectron%5CSchwabElectron&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fschwab-logout%2Froute&page=%2Fapi%2Fschwab-logout%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fschwab-logout%2Froute.js&appDir=C%3A%5CUsers%5CEllen%5COneDrive%5CDesktop%5CDashboard%5CSchwabElectron%5CSchwabElectron%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CEllen%5COneDrive%5CDesktop%5CDashboard%5CSchwabElectron%5CSchwabElectron&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();