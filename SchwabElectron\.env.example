# ===========================================
# SCHWAB DASHBOARD - ENVIRONMENT VARIABLES
# ===========================================
# Copy this file to .env and fill in your actual values

# ===========================================
# AUTHENTICATION SETTINGS
# ===========================================
# Generate a random secret key for NextAuth.js
# You can use: openssl rand -base64 32
NEXTAUTH_SECRET=your-secret-key-here

# The URL where your app will be running (keep as-is for development)
NEXTAUTH_URL=https://127.0.0.1:3002

# ===========================================
# DATABASE CONFIGURATION
# ===========================================
# MongoDB connection string
# Format: mongodb+srv://username:<EMAIL>/database
DATABASE_URL="mongodb+srv://username:<EMAIL>/schwabdashboard"

# Optional: Backup database URL (if using multiple databases)
SAVERIO_DATABASE_URL="mongodb+srv://username:<EMAIL>/schwabdashboard"

# ===========================================
# SCHWAB API CONFIGURATION
# ===========================================
# Get these from your Schwab Developer Account
# https://developer.schwab.com/
NEXT_PUBLIC_SCHWAB_APP_KEY=your-schwab-app-key-here
NEXT_PUBLIC_SCHWAB_SECRET=your-schwab-secret-here

# OAuth callback URL (keep as-is for development)
NEXT_PUBLIC_SCHWAB_CALLBACKURL="https://127.0.0.1:3002/oauth/schwab"

# Your encrypted Schwab account number
NEXT_PUBLIC_ACCOUNT_NUMBER="your-encrypted-account-number-here"

# ===========================================
# DEVELOPMENT SETTINGS
# ===========================================
# Set to 'true' for development, 'false' for production
CSP_IS_DEVELOPMENT=true

# Environment mode
NODE_ENV=development

# ===========================================
# SETUP INSTRUCTIONS
# ===========================================
# 1. Copy this file to .env
# 2. Fill in all the values above
# 3. Run: npm run setup
# 4. Start development: npm run dev
# 5. Open: https://localhost:3002
#
# For help, see README.md or contact the development team